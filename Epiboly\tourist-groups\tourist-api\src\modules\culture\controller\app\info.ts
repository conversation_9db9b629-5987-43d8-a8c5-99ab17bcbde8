import { Inject, Get } from '@midwayjs/core';
import { CoolController, BaseController } from '@cool-midway/core';
import { CultureInfoEntity } from '../../entity/info';
import { CultureInfoService } from '../../service/info';

/**
 * 企业文化信息app端
 */
@CoolController({
    prefix: '/app/culture',
    api: [],
    entity: CultureInfoEntity,
    service: CultureInfoService,
})
export class AppCultureInfoController extends BaseController {
    @Inject()
    cultureInfoService: CultureInfoService;

    /**
     * 获取企业文化信息
     */
    @Get('/info', { summary: '获取企业文化信息' })
    async getCultureInfo() {
        try {
            // 获取第一条企业文化信息记录
            const cultureInfo = await this.cultureInfoService.cultureInfoEntity
                .createQueryBuilder('info')
                .orderBy('info.createTime', 'DESC')
                .getOne();

            if (!cultureInfo) {
                return this.fail('暂无企业文化信息');
            }

            return this.ok(cultureInfo);
        } catch (error) {
            return this.fail('获取企业文化信息失败');
        }
    }
} 