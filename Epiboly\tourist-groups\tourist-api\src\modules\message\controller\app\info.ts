import { Inject, Post, Body } from '@midwayjs/core';
import { CoolController, BaseController } from '@cool-midway/core';
import { MessageInfoEntity } from '../../entity/info';
import { MessageInfoService } from '../../service/info';

/**
 * 留言信息app端
 */
@CoolController({
    prefix: '/app/message',
    api: [],
    entity: MessageInfoEntity,
    service: MessageInfoService,
})
export class AppMessageInfoController extends BaseController {
    @Inject()
    messageInfoService: MessageInfoService;

    /**
     * 提交留言
     */
    @Post('/submit', { summary: '提交留言' })
    async submit(@Body() body: any) {
        const { name, phone, email, content } = body;

        // 基本参数校验
        if (!name || !name.trim()) {
            return this.fail('请输入姓名');
        }
        if (!phone || !phone.trim()) {
            return this.fail('请输入电话');
        }
        if (!email || !email.trim()) {
            return this.fail('请输入邮箱');
        }
        if (!content || !content.trim()) {
            return this.fail('请输入留言内容');
        }

        // 邮箱格式校验
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return this.fail('邮箱格式不正确');
        }

        // 电话格式校验
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(phone)) {
            return this.fail('电话格式不正确');
        }

        try {
            await this.messageInfoService.messageInfoEntity.save({
                name: name.trim(),
                phone: phone.trim(),
                email: email.trim(),
                content: content.trim()
            });

            return this.ok('留言提交成功！我们会尽快与您联系。');
        } catch (error) {
            console.error('提交留言失败:', error);
            return this.fail('提交留言失败，请稍后再试');
        }
    }
} 