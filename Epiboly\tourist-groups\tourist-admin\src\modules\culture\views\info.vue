<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 - 只在没有数据时显示 -->
			<cl-add-btn v-if="!hasData"/>
			<cl-flex1 />
			<!-- 条件搜索 -->
			<cl-search ref="Search" />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
	</cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
	name: "culture-info",
});

import { useCrud, useTable, useUpsert, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useI18n } from "vue-i18n";
import { ref, computed, watch } from "vue";

const { service } = useCool();
const { t } = useI18n();

// 响应式数据
const tableData = ref<any[]>([]);

// 计算属性：判断是否有数据
const hasData = computed(() => {
	return tableData.value && tableData.value.length > 0;
});

// cl-table
const Table = useTable({
	columns: [
		{ type: "selection" },
		{ label: t("姓名"), prop: "name", minWidth: 140 },
		{
			label: t("主要介绍"),
			prop: "mainIntroduction",
			minWidth: 200,
			showOverflowTooltip: true,
		},
		{
			label: t("次要介绍"),
			prop: "secondaryIntroduction",
			minWidth: 200,
			showOverflowTooltip: true,
		},
		{
			label: t("创始人图片"),
			prop: "founderImage",
			minWidth: 100,
			component: { name: "cl-image", props: { size: 60 } },
		},
		{
			label: t("企业文化"),
			prop: "enterpriseCulture",
			minWidth: 200,
			showOverflowTooltip: true,
		},
		{
			label: t("企业文案"),
			prop: "enterpriseCopywriting",
			minWidth: 200,
			showOverflowTooltip: true,
		},
		{
			label: t("创建时间"),
			prop: "createTime",
			minWidth: 170,
			sortable: "desc",
			component: { name: "cl-date-text" },
		},
		{
			label: t("更新时间"),
			prop: "updateTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{ type: "op", buttons: ["edit"] },
	],
});

// 监听Table的data变化
watch(
	() => Table.value?.data,
	(newData) => {
		tableData.value = newData || [];
	},
	{ immediate: true, deep: true }
);

// cl-upsert
const Upsert = useUpsert({
	items: [
		{
			label: t("姓名"),
			prop: "name",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("主要介绍"),
			prop: "mainIntroduction",
			component: { 
				name: "el-input", 
				props: { 
					type: "textarea", 
					rows: 4, 
					clearable: true 
				} 
			},
		},
		{
			label: t("次要介绍"),
			prop: "secondaryIntroduction",
			component: { 
				name: "el-input", 
				props: { 
					type: "textarea", 
					rows: 4, 
					clearable: true 
				} 
			},
		},
		{
			label: t("创始人图片"),
			prop: "founderImage",
			component: { name: "cl-upload" },
		},
		{
			label: t("企业文化"),
			prop: "enterpriseCulture",
			component: { 
				name: "el-input", 
				props: { 
					type: "textarea", 
					rows: 4, 
					clearable: true 
				} 
			},
		},
		{
			label: t("企业文案"),
			prop: "enterpriseCopywriting",
			component: { 
				name: "el-input", 
				props: { 
					type: "textarea", 
					rows: 4, 
					clearable: true 
				} 
			},
		},
	],
});

// cl-search
const Search = useSearch();

// cl-crud
const Crud = useCrud(
	{
		service: service.culture.info,
	},
	(app) => {
		app.refresh();
	},
);

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}
</script>
