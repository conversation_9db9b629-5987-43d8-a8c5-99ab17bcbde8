<template>
  <div class="user-center">
    <!-- 头部导航 -->
    <TheHeader />
    
    <div class="user-center-container">
      <div class="main-content">
        <!-- 左侧用户信息 -->
        <div class="sidebar">
          <UserProfile :active-section="activeSection" @menu-select="handleMenuSelect" />
        </div>
        
        <!-- 右侧内容区域 -->
        <div class="content-area">
          <!-- 我的订单 -->
          <div v-if="activeSection === 'orders'">
            <!-- 主要Tab区域（替换原标题） -->
            <div class="main-tab-section">
              <div class="main-tab-nav">
                <div 
                  class="main-tab-item" 
                  :class="{ active: mainTab === 'groups' }" 
                  @click="handleMainTabChange('groups')">
                  散拼团
                </div>
                <div 
                  class="main-tab-item" 
                  :class="{ active: mainTab === 'bookings' }" 
                  @click="handleMainTabChange('bookings')">
                  我的预定
                </div>
              </div>
            </div>
            
            <!-- 散拼团内容 -->
            <div v-if="mainTab === 'groups'" class="tab-section">
              <div class="tab-nav">
                <div 
                  class="tab-item" 
                  :class="{ active: activeTab === 'all' }" 
                  @click="activeTab = 'all'">
                  全部拼团
                </div>
                <div 
                  class="tab-item" 
                  :class="{ active: activeTab === 'pending' }" 
                  @click="activeTab = 'pending'">
                  已成团
                  <!-- <span class="tab-badge" v-if="pendingCount">{{ pendingCount }}</span> -->
                </div>
                <div 
                  class="tab-item" 
                  :class="{ active: activeTab === 'expired' }" 
                  @click="activeTab = 'expired'">
                  已失效
                  <!-- <span class="tab-badge" v-if="expiredCount > 0">{{ expiredCount }}</span> -->
                </div>
              </div>
              
              <!-- 列表区域 -->
              <div class="list-container">
                <MyOrders :active-tab="activeTab" :order-type="1" />
              </div>
            </div>
            
            <!-- 我的预定内容 -->
            <div v-else-if="mainTab === 'bookings'" class="tab-section">
              <div class="list-container">
                <MyOrders :active-tab="'all'" :order-type="0" />
              </div>
            </div>
          </div>

          <!-- 通知 -->
          <div v-else-if="activeSection === 'notifications'">
            <div class="content-header">
              <h2 class="content-title">通知中心</h2>
              <div class="title-divider"></div>
            </div>
            <div class="notification-content">
              <Notifications 
                type="notifications" 
                :user-info="currentUser" 
                @unread-count-changed="handleUnreadCountChanged" />
            </div>
          </div>

          <!-- 公告 -->
          <div v-else-if="activeSection === 'announcements'">
            <div class="content-header">
              <h2 class="content-title">系统公告</h2>
              <div class="title-divider"></div>
            </div>
            <div class="announcement-content">
              <Notifications 
                type="announcements" 
                :user-info="currentUser" 
                @unread-count-changed="handleUnreadCountChanged" />
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部组件 -->
    <TheFooter />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue';
import { useRoute } from 'vue-router';
import TheHeader from '../components/TheHeader.vue';
import TheFooter from '../components/TheFooter.vue';
import UserProfile from '../components/UserProfile.vue';
import MyOrders from '../components/MyOrders.vue';
import Notifications from '../components/Notifications.vue';
import { orderApi } from '../api/order';
import { getUserInfo } from '../utils/auth';
import { eventBus, EVENTS } from '../utils/eventBus';

const route = useRoute();

// 当前活动的内容区域 - 默认为我的拼团
const activeSection = ref('orders');

// 主要tab（我的拼团/我的预定）
const mainTab = ref('groups');

// 当前活动的tab
const activeTab = ref('all');

// 当前用户信息
const currentUser = ref(null);

// 订单统计数据
const orderStats = ref({
  all: 0,
  pending: 0,  // 已成团的数量
  expired: 0   // 已失效的数量
});

// 请求完成状态追踪
const requestStatus = reactive({
  orderStats: false,
  userInfo: false
});

// 检查所有请求是否完成并执行翻译
const checkAllRequestsComplete = () => {
  const allComplete = Object.values(requestStatus).every(status => status === true);
  if (allComplete) {
    setTimeout(() => {
      translate.execute();
    }, 50);
  }
};

// 获取当前用户信息
const getCurrentUser = () => {
  const userInfo = getUserInfo();
  if (userInfo) {
    currentUser.value = {
      id: userInfo.id || userInfo.userId,
      nickName: userInfo.nickName,
      phone: userInfo.phone
    };
  }
  requestStatus.userInfo = true;
  checkAllRequestsComplete();
};

// 获取订单统计数据
const loadOrderStats = async () => {
  try {
    // 使用专门的统计API
    const response = await orderApi.getMyOrdersStats();
    if (response.code === 1000 && response.data) {
      orderStats.value = {
        all: response.data.all || 0,
        pending: response.data.completed || 0, // 已成团
        expired: response.data.expired || 0   // 已失效
      };
    }
  } catch (error) {
    console.error('获取订单统计失败:', error);
    // 如果获取失败，保持默认值
  } finally {
    requestStatus.orderStats = true;
    checkAllRequestsComplete();
  }
};

// 格式化数量显示（超过99显示99+）
const formatCount = (count) => {
  if (count > 99) return '99+';
  if (count < 10) return `0${count}`;
  return count.toString();
};

// 计算显示的数量
const expiredCount = computed(() => {
  return orderStats.value.expired > 0 ? formatCount(orderStats.value.expired) : '';
});

const pendingCount = computed(() => {
  return orderStats.value.pending > 0 ? formatCount(orderStats.value.pending) : '';
});

// 处理主tab切换
const handleMainTabChange = (tabType) => {
  mainTab.value = tabType;
  // 切换主tab时重置子tab
  if (tabType === 'groups') {
    activeTab.value = 'all';
  }
};

// 处理菜单选择
const handleMenuSelect = (menuType) => {
  console.log('菜单选择:', menuType); // 调试日志
  activeSection.value = menuType;
  
  // 如果切换到我的拼团，重置tab到全部
  if (menuType === 'orders') {
    mainTab.value = 'groups'; // 默认显示散拼团
    activeTab.value = 'all';
    // 重新加载订单统计
    loadOrderStats();
  }
};

// 根据URL参数设置初始状态
onMounted(() => {
  const type = parseInt(route.query.type) || 0;
  const menuMap = {
    0: 'orders',
    1: 'notifications', 
    2: 'announcements'
  };
  activeSection.value = menuMap[type] || 'orders';
  
  // 如果默认显示订单页面，加载统计数据
  if (activeSection.value === 'orders') {
    loadOrderStats();
  }

  // 获取当前用户信息
  getCurrentUser();
});

// 处理未读数量变化的回调
const handleUnreadCountChanged = () => {
  console.log('未读数量变化，通知头部组件更新');
  // 触发事件总线事件，通知头部组件立即更新未读数量
  eventBus.emit(EVENTS.UNREAD_COUNT_CHANGED);
};
</script>

<style scoped>
.user-center {
  min-height: 100vh;
  background-color: #f6f6f6;
  display: flex;
  flex-direction: column;
}

.user-center-container {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 135px 20px 50px;
  width: 100%;
}

.main-content {
  display: flex;
  gap: 20px;
}

.sidebar {
  width: 225px;
  flex-shrink: 0;
}

.content-area {
  flex: 1;
  min-width: 0; /* 防止内容溢出 */
}

/* 内容标题区域 */
.content-header {
  margin-bottom: 20px;
}

.content-title {
  font-size: 18px;
  color: #C9161C;
  font-weight: bold;
  margin: 0 0 12px 0;
}

.title-divider {
  height: 1px;
  background-color: #eaeaea;
  width: 100%;
}

/* 主要Tab区域 */
.main-tab-section {
  margin-bottom: 20px;
}

.main-tab-nav {
  display: flex;
  gap: 0;
  border-bottom: 1px solid #eaeaea;
  margin-bottom: 20px;
}

.main-tab-item {
  padding: 12px 24px;
  padding-left: 0;
  cursor: pointer;
  font-size: 16px;
  color: #666;
  background: transparent;
  border: none;
  position: relative;
  transition: all 0.3s ease;
  font-weight: 500;
}

.main-tab-item:hover {
  color: #333;
}

.main-tab-item.active {
  color: #c8161b;
  font-weight: bold;
}

.main-tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: calc(50% - 12px);
  transform: translateX(-50%);
  width: calc(100% - 28px); /* 减去左右padding */
  height: 2px;
  background-color: #c8161b;
}

/* Tab区域 */
.tab-section {
  background: transparent;
}

.tab-nav {
  display: flex;
  gap: 0;
  margin-bottom: 20px;
  background: transparent;
}

.tab-item {
  padding: 4px;
  margin-right: 14px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  background: transparent;
  border: none;
  position: relative;
  transition: all 0.3s ease;
}

.tab-item:hover {
  color: #333;
}

.tab-item.active {
  color: #c8161b;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #c8161b;
}

.tab-badge {
  position: absolute;
  right: -26px;
  top: -8px;
  background-color: #e9363a;
  color: white;
  font-size: 12px;
  padding: 1px 6px;
  border-radius: 10px;
  margin-left: 5px;
  font-weight: normal;
}

/* 列表容器 */
.list-container {
  border-radius: 8px;
  min-height: 500px;
}

/* 通知和公告内容区域 */
.notification-content,
.announcement-content {
  background-color: #fff;
  border-radius: 8px;
  min-height: 500px;
}



/* 响应式调整 */
@media (max-width: 992px) {
  .main-content {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .user-center-container {
    padding: 20px 15px 40px;
  }
  
  .content-title {
    font-size: 20px;
  }
  
  .tab-nav {
    flex-wrap: wrap;
  }
  
  .tab-item {
    padding: 10px 16px;
    font-size: 14px;
  }
}
</style> 