<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />

			<cl-add-btn />
			<cl-flex1 />
			<!-- 条件搜索 -->
			<cl-search ref="Search" />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
	</cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
	name: "notice-notice",
});

import { useCrud, useTable, useUpsert, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useI18n } from "vue-i18n";
import { reactive, ref, watch } from "vue";
import { useBase } from "/$/base";
import { ElMessage, ElMessageBox } from "element-plus";
import UserSelect from "../components/user-select.vue";

const { service } = useCool();
const { t } = useI18n();
const { user } = useBase();

// 选项
const options = reactive({
	type: [
		{ label: t("通知"), value: 0, type: "danger" },
		{ label: t("公告"), value: 1, type: "success" },
	],
	// 接收人类型选项
	receiverType: [
		{ label: t("全部用户"), value: "all" },
		{ label: t("指定用户"), value: "selected" },
	],
	// 发送状态选项
	sendStatus: [
		{ label: t("待发送"), value: 0, type: "warning" },
		{ label: t("已发送"), value: 1, type: "success" },
		{ label: t("发送失败"), value: 2, type: "danger" },
	],
});

// cl-upsert
const Upsert = useUpsert({
	items: [
		{
			label: t("通知名称"),
			prop: "title",
			component: { name: "el-input", props: { clearable: true } },
			required: true,
		},
		{
			label: t("通知内容"),
			prop: "content",
			component: {
				name: "el-input",
				props: { type: "textarea", rows: 4 },
			},
			required: true,
		},
		{
			label: t("通知类型"),
			prop: "type",
			component: { name: "el-radio-group", options: options.type },
			value: 0,
			required: true,
			span: 12,
		},
		{
			label: t("接收人类型"),
			prop: "receiverType",
			component: { 
				name: "el-radio-group", 
				options: options.receiverType,
			},
			value: "all",
			required: true,
			span: 12,
		},
		{
			label: t("选择用户"),
			prop: "receiverIds",
			component: {
				name: "notice-user-select",
				props: {
					userType: "user",
					placeholder: t("选择指定用户时需要填写，全部用户时可忽略")
				}
			},
			span: 24,
		},
		{
			label: t("发送人备注"),
			prop: "senderRemark",
			component: { 
				name: "el-input", 
				props: { 
					clearable: true,
					placeholder: t("可选，用于标识发送人身份")
				} 
			},
			span: 24,
		},
	],
	
	// 提交前处理数据
	onSubmit: async (data, { next, close, done }) => {
		try {
			// 根据接收人类型处理数据
			if (data.receiverType === "all") {
				// 全部用户，后端处理
				data.receiverIds = [];
				data.sendToAll = true;
				data.sendToAdmin = false;
				// 不设置receiverCount，让后端计算实际用户数量
			} else if (data.receiverType === "selected") {
				// 指定用户
				data.sendToAll = false;
				data.sendToAdmin = false;
				data.receiverCount = data.receiverIds ? data.receiverIds.length : 0;
			}
			
			// 设置发送人ID为当前登录用户
			if (!data.senderId && user.info) {
				data.senderId = user.info.id;
			}
			
			// 设置默认发送状态为已发送
			data.sendStatus = 1;
			
			console.log('发送数据:', data);
			
			let result;
			try {
				// 尝试使用标准的add接口
				result = await service.notice.notice.add(data);
			} catch (addError) {
				console.log('标准接口失败，尝试批量发送接口:', addError);
				// 如果标准接口失败，使用批量发送接口
				result = await service.notice.notice.request({
					url: '/batchSend',
					method: 'POST',
					data: data
				});
			}
			
			console.log('接口返回:', result);
			
			ElMessage.success(`${t("发送成功")}: ${data.title}, ${t("接收人数")}: ${result.receiverCount || '计算中'}`);
			done();
			close();
			refresh(); // 刷新列表
		} catch (error: any) {
			console.error('发送失败:', error);
			ElMessage.error(error.message || t("发送失败"));
			done();
		}
	},
});

// cl-table
const Table = useTable({
	columns: [
		{ type: "selection" },
		{ label: t("发送人"), prop: "senderName", minWidth: 140 },
		{ label: t("通知名称"), prop: "title", minWidth: 140 },
		{
			label: t("通知内容"),
			prop: "content",
			showOverflowTooltip: true,
			minWidth: 200,
		},
		{
			label: t("接收人类型"),
			prop: "receiverType",
			minWidth: 120,
			formatter: (row: any) => {
				// 优先根据sendToAll字段判断
				if (row.sendToAll === true) return t("全部用户");
				if (row.sendToAdmin === true) return t("管理员");
				// 如果sendToAll字段不存在，根据receiverIds判断
				if (!row.receiverIds || row.receiverIds.length === 0) {
					return t("全部用户");
				}
				return t("指定用户");
			},
		},
		{
			label: t("接收人数"),
			prop: "receiverCount",
			minWidth: 100,
			formatter: (row: any) => {
				const isAllUsers = row.sendToAll === true || (!row.receiverIds || row.receiverIds.length === 0);
				
				if (isAllUsers) {
					// 如果是全部用户，显示实际数量，如果没有数量则显示"全部用户"
					return row.receiverCount > 0 ? row.receiverCount : t("全部用户");
				}
				return row.receiverCount || 0;
			},
		},
		{
			label: t("通知类型"),
			prop: "type",
			minWidth: 120,
			dict: options.type,
		},
		{
			label: t("发送状态"),
			prop: "sendStatus",
			minWidth: 100,
			dict: options.sendStatus,
		},
		{
			label: t("创建时间"),
			prop: "createTime",
			minWidth: 170,
			sortable: "desc",
			component: { name: "cl-date-text" },
		},
		{
			label: t("更新时间"),
			prop: "updateTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{ 
			type: "op", 
			buttons: [
				"edit", 
				"delete",
				{
					label: t("发送详情"),
					type: "primary",
					onClick: (row: any) => viewSendDetail(row)
				}
			] 
		},
	],
});

// cl-search
const Search = useSearch();

// cl-crud
const Crud = useCrud(
	{
		service: service.notice.notice,
	},
	(app) => {
		app.refresh();
	},
);

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}

// 重新发送通知
async function resendNotice(row: any) {
	try {
		await service.notice.notice.request({
			url: '/resend',
			method: 'POST',
			data: { id: row.id }
		});
		ElMessage.success(t("重新发送成功"));
		refresh();
	} catch (error: any) {
		ElMessage.error(error.message || t("重新发送失败"));
	}
}

// 查看发送详情
function viewSendDetail(rows: any) {
	let row = rows.scope.row;
	// 使用API获取发送详情
	service.notice.notice.request({
		url: '/sendDetail',
		method: 'GET',
		params: { id: row.id }
	}).then((res: any) => {
		// 显示详情弹窗
		ElMessageBox.alert(`
			<div style="text-align: left;">
				<h3>${row.title}</h3>
				<p><strong>发送内容：</strong>${row.content}</p>
				<p><strong>发送人：</strong>${res.notice?.senderName || '系统'}</p>
				<p><strong>发送时间：</strong>${res.notice?.sendTime || res.notice?.createTime}</p>
				<p><strong>接收人数：</strong>${res.notice?.receiverCount || 0}</p>
				<p><strong>发送状态：</strong>${row.sendStatus === 1 ? '已发送' : row.sendStatus === 2 ? '发送失败' : '待发送'}</p>
				${res.receivers && res.receivers.length > 0 ? 
					`<p><strong>接收人列表：</strong><br/>${res.receivers.map((r: any) => r.name || r.username).join('、')}</p>` : 
					''}
			</div>
		`, `${t("通知发送详情")}`, {
			dangerouslyUseHTMLString: true,
			confirmButtonText: t("确定")
		});
	}).catch((error: any) => {
		ElMessage.error(error.message || t("获取详情失败"));
	});
}
</script>
