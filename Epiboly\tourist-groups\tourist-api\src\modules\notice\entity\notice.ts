import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 通知信息
 */
@Entity('notice_notice')
export class NoticeNoticeEntity extends BaseEntity {
  @Column({ comment: '通知名称' })
  title: string;

  @Column({ comment: '通知内容', type: 'text' })
  content: string;

  @Index()
  @Column({ comment: '发布时间', nullable: true })
  publishTime: Date;

  @Index()
  @Column({ comment: '发送人ID', nullable: true })
  senderId: number;

  @Column({ comment: '发送人备注', nullable: true })
  senderRemark: string;

  @Column({ comment: '通知类型', dict: ['通知', '公告'], default: 0 })
  type: number;

  @Column({ comment: '接收人ID列表', type: 'json', nullable: true })
  receiverIds: number[];

  @Column({ comment: '接收人类型', default: 'all' })
  receiverType: string;

  @Column({ comment: '接收人数量', default: 0 })
  receiverCount: number;

  @Column({ comment: '是否发送给全部用户', default: false })
  sendToAll: boolean;

  @Column({ comment: '是否发送给管理员', default: false })
  sendToAdmin: boolean;

  @Column({ comment: '发送状态', dict: ['待发送', '已发送', '发送失败'], default: 0 })
  sendStatus: number;

  @Column({ comment: '发送时间', nullable: true })
  sendTime: Date;

  @Column({ comment: '失败原因', nullable: true })
  failReason: string;
}
