import { BaseEntity } from '../../base/entity/base';
import { Column, Entity, Index } from 'typeorm';

/**
 * 留言信息
 */
@Entity('message_info')
export class MessageInfoEntity extends BaseEntity {
  @Column({ comment: '姓名', nullable: true })
  name: string;

  @Index()
  @Column({ comment: '电话', nullable: true })
  phone: string;

  @Index()
  @Column({ comment: '邮箱', nullable: true })
  email: string;

  @Column({ comment: '留言内容', type: 'text', nullable: true })
  content: string;
}
