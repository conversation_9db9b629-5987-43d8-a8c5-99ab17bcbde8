// 认证相关工具函数

/**
 * 获取token
 */
export function getToken() {
  return localStorage.getItem('token')
}

/**
 * 设置token
 */
export function setToken(token) {
  localStorage.setItem('token', token)
}

/**
 * 移除token
 */
export function removeToken() {
  localStorage.removeItem('token')
  localStorage.removeItem('refreshToken')
  localStorage.removeItem('tokenExpire')
  localStorage.removeItem('refreshTokenExpire')
  localStorage.removeItem('isLoggedIn')
  localStorage.removeItem('userInfo')
}

/**
 * 获取刷新token
 */
export function getRefreshToken() {
  return localStorage.getItem('refreshToken')
}

/**
 * 设置刷新token
 */
export function setRefreshToken(refreshToken) {
  localStorage.setItem('refreshToken', refreshToken)
}

/**
 * 检查是否已登录
 */
export function isLoggedIn() {
  const token = getToken()
  const loginStatus = localStorage.getItem('isLoggedIn')
  return !!(token && loginStatus === 'true')
}

/**
 * 检查token是否过期
 */
export function isTokenExpired() {
  const expireTime = localStorage.getItem('tokenExpire')
  if (!expireTime) return false
  
  return Date.now() > parseInt(expireTime)
}

/**
 * 检查刷新token是否过期
 */
export function isRefreshTokenExpired() {
  const refreshExpireTime = localStorage.getItem('refreshTokenExpire')
  if (!refreshExpireTime) return false
  
  return Date.now() > parseInt(refreshExpireTime)
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  const userInfo = localStorage.getItem('userInfo')
  try {
    return userInfo ? JSON.parse(userInfo) : null
  } catch (error) {
    console.error('解析用户信息失败:', error)
    return null
  }
}

/**
 * 设置用户信息
 */
export function setUserInfo(userInfo) {
  localStorage.setItem('userInfo', JSON.stringify(userInfo))
}

/**
 * 清除所有认证信息
 */
export function clearAuth() {
  removeToken()
}

/**
 * 登出
 */
export function logout() {
  clearAuth()
  // 跳转到登录页
  if (window.location.pathname !== '/login') {
    window.location.href = '/login'
  }
} 