import { Provide } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { ReservationInfoEntity } from '../entity/info';

/**
 * 预定信息
 */
@Provide()
export class ReservationInfoService extends BaseService {
  @InjectEntityModel(ReservationInfoEntity)
  reservationInfoEntity: Repository<ReservationInfoEntity>;
}
