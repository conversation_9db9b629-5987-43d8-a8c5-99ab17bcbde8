import { Inject } from '@midwayjs/core';
import { Cool<PERSON><PERSON>roller, BaseController } from '@cool-midway/core';
import { ProductInfoEntity } from '../../entity/info';
import { ProductInfoService } from '../../service/info';

/**
 * 产品信息
 */
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: ProductInfoEntity,
  service: ProductInfoService,
  pageQueryOp: {
    keyWordLikeFields: ['a.name'],
    fieldEq: ['a.status', 'a.isMostPopular'],
    where: async ctx => {
      const { scoreStart, scoreEnd, priceStart, priceEnd } = ctx.request.body;
      const where = [];
      if (scoreStart !== undefined && scoreEnd !== undefined) {
        where.push([
          'a.score BETWEEN :scoreStart AND :scoreEnd',
          { scoreStart, scoreEnd },
        ]);
      }
      if (priceStart !== undefined && priceEnd !== undefined) {
        where.push([
          'a.price BETWEEN :priceStart AND :priceEnd',
          { priceStart, priceEnd },
        ]);
      }
      return where;
    },
  },
})
export class AdminProductInfoController extends BaseController {
  @Inject()
  productInfoService: ProductInfoService;
}
