import { Inject, Get, Query } from '@midwayjs/core';
import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core';
import { MemberInfoEntity } from '../../entity/info';
import { MemberInfoService } from '../../service/info';

/**
 * 成员信息app端
 */
@CoolController({
    prefix: '/app/member',
    api: [],
    entity: MemberInfoEntity,
    service: MemberInfoService,
})
export class AppMemberInfoController extends BaseController {
    @Inject()
    memberInfoService: MemberInfoService;

    /**
     * 获取成员列表
     */
    @Get('/list', { summary: '获取成员列表' })
    async list() {
        const data = await this.memberInfoService.memberInfoEntity.find({
            where: { status: 1 },
            order: { createTime: 'DESC' }
        });

        // 按类型分组
        const customDesigners = data.filter(item => item.type === 0); // 定制师
        const tourGuides = data.filter(item => item.type === 1); // 导游

        return this.ok({
            customDesigners: customDesigners.map(item => ({
                id: item.id,
                name: item.name,
                position: item.position,
                description: item.introduction,
                avatar: item.photo,
                type: item.type
            })),
            tourGuides: tourGuides.map(item => ({
                id: item.id,
                name: item.name,
                position: item.position,
                description: item.introduction,
                avatar: item.photo,
                type: item.type
            }))
        });
    }

    /**
     * 根据类型获取成员列表
     */
    @Get('/listByType', { summary: '根据类型获取成员列表' })
    async listByType(@Query('type') type?: number) {
        const where: any = { status: 1 };
        if (type !== undefined) {
            where.type = type;
        }

        const data = await this.memberInfoService.memberInfoEntity.find({
            where,
            order: { createTime: 'DESC' }
        });

        return this.ok(data.map(item => ({
            id: item.id,
            name: item.name,
            position: item.position,
            description: item.introduction,
            avatar: item.photo,
            type: item.type
        })));
    }
} 