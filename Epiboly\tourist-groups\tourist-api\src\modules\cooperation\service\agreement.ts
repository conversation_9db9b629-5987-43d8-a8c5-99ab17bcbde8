import { Provide } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { CooperationAgreementEntity } from '../entity/agreement';

/**
 * 协议信息
 */
@Provide()
export class CooperationAgreementService extends BaseService {
  @InjectEntityModel(CooperationAgreementEntity)
  cooperationAgreementEntity: Repository<CooperationAgreementEntity>;
}
