import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 成员信息
 */
@Entity('member_info')
export class MemberInfoEntity extends BaseEntity {
  @Column({ comment: '姓名' })
  name: string;

  @Column({ comment: '职位' })
  position: string;

  @Column({ comment: '类型', dict: ['定制师', '导游'], default: 0 })
  type: number;

  @Column({ comment: '介绍', type: 'text', nullable: true })
  introduction: string;

  @Column({ comment: '照片', nullable: true })
  photo: string;

  @Column({ comment: '状态', dict: ['禁用', '启用'], default: 1 })
  status: number;
}
