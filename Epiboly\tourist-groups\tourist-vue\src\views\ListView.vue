<script setup>
import { ref, onMounted, computed, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { useAppStore } from '../stores/gameStore';

const router = useRouter();
const appStore = useAppStore();

// 过滤器状态
const filters = ref({
    category: 'all',
    type: 'all',
    search: ''
});

// 分类选项
const categoryOptions = [
    { value: 'all', label: '所有分类' },
    { value: 'category1', label: '分类一' },
    { value: 'category2', label: '分类二' },
    { value: 'category3', label: '分类三' }
];

// 类型选项
const typeOptions = [
    { value: 'all', label: '所有类型' },
    { value: 'type1', label: '类型一' },
    { value: 'type2', label: '类型二' },
    { value: 'type3', label: '类型三' },
    { value: 'type4', label: '类型四' }
];

// 已筛选的项目
const filteredItems = ref([]);

// 加载状态
const isLoading = ref(true);

// 响应式数据
const searchQuery = ref('');
const selectedCategory = ref('全部');
const selectedType = ref('全部');
const currentPage = ref(1);
const pageSize = ref(12);

// 请求完成状态追踪
const requestStatus = reactive({
  dataInit: false
});

// 检查所有请求是否完成并执行翻译
const checkAllRequestsComplete = () => {
  const allComplete = Object.values(requestStatus).every(status => status === true);
  if (allComplete) {
    setTimeout(() => {
      translate.execute();
    }, 50);
  }
};

// 处理筛选变化
const handleFilterChange = () => {
    filteredItems.value = appStore.filteredItems(
        filters.value.category,
        filters.value.type,
        filters.value.search
    );
};

// 处理项目点击
const handleItemClick = (item) => {
    router.push({
        name: 'detail',
        params: { id: item.id }
    });
};

// 切换选择项目
const toggleItemSelection = (item, event) => {
    event.stopPropagation();
    appStore.toggleItemSelection(item.id);
};

onMounted(async () => {
    try {
        // 初始化数据
        await appStore.initData();

        // 应用初始筛选
        handleFilterChange();
    } catch (error) {
        console.error('初始化数据失败:', error);
    } finally {
        isLoading.value = false;
        requestStatus.dataInit = true;
        checkAllRequestsComplete();
    }
});
</script>

<template>
    <div class="max-w-7xl mx-auto px-4 py-8">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-2xl font-bold">项目列表</h1>
            <p class="text-gray-500 mt-2">浏览并管理您的项目</p>
        </div>

        <!-- 筛选器部分 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- 分类筛选 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">分类</label>
                    <select v-model="filters.category" @change="handleFilterChange" class="w-full border-gray-300 rounded-md shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                        <option v-for="option in categoryOptions" :key="option.value" :value="option.value">
                            {{ option.label }}
                        </option>
                    </select>
                </div>

                <!-- 类型筛选 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">类型</label>
                    <select v-model="filters.type" @change="handleFilterChange" class="w-full border-gray-300 rounded-md shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                        <option v-for="option in typeOptions" :key="option.value" :value="option.value">
                            {{ option.label }}
                        </option>
                    </select>
                </div>

                <!-- 搜索筛选 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">搜索</label>
                    <div class="relative">
                        <input type="text" v-model="filters.search" @input="handleFilterChange" class="w-full border-gray-300 rounded-md shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50 pl-10" placeholder="搜索项目...">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 项目列表 -->
        <div v-if="isLoading" class="flex justify-center items-center py-12">
            <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
        </div>

        <div v-else-if="filteredItems.length === 0" class="bg-white rounded-lg shadow-md p-12 text-center">
            <svg class="mx-auto h-16 w-16 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 class="mt-4 text-lg font-medium text-gray-900">没有找到符合条件的项目</h3>
            <p class="mt-2 text-sm text-gray-500">请尝试更改筛选条件或清除搜索内容</p>
        </div>

        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div v-for="item in filteredItems" :key="item.id" class="bg-white rounded-lg shadow-md overflow-hidden cursor-pointer transition-transform hover:scale-[1.02]" @click="handleItemClick(item)">
                <div class="relative h-48">
                    <img :src="item.image" :alt="item.name" class="w-full h-full object-cover">
                    <div class="absolute top-0 right-0 p-2">
                        <button @click="toggleItemSelection(item, $event)" :class="['rounded-full p-2', item.selected ? 'bg-primary-100 text-primary-500' : 'bg-black/20 text-white']">
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path v-if="item.selected" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="p-4">
                    <div class="flex items-center justify-between">
                        <div class="px-2 py-1 bg-gray-100 rounded-md text-xs text-gray-600">{{ item.category }}</div>
                        <div class="px-2 py-1 bg-gray-100 rounded-md text-xs text-gray-600">{{ item.type }}</div>
                    </div>
                    <h3 class="mt-2 text-lg font-semibold text-gray-900 line-clamp-1">{{ item.name }}</h3>
                    <p class="mt-1 text-sm text-gray-500 line-clamp-2">
                        这里是项目描述信息，可以展示一些关于项目的重要信息。
                    </p>
                </div>
            </div>
        </div>

        <!-- 已选择项目浮动按钮 -->
        <div v-if="appStore.selectedItems.length > 0" class="fixed bottom-6 right-6">
            <div class="bg-primary-500 text-white rounded-full px-6 py-3 shadow-lg">
                已选择 {{ appStore.selectedItems.length }} 个项目
            </div>
        </div>
    </div>
</template>

<style scoped>
.line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style> 