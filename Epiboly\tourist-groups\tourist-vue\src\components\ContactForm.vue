<template>
  <div class="contact-form">
    <h2 class="form-title">在线留言</h2>

    <div class="form-container">
      <el-form :model="messageForm" label-width="80px">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <el-form-item label="姓名">
            <el-input v-model="messageForm.name" placeholder="请输入姓名"></el-input>
          </el-form-item>

          <el-form-item label="电话">
            <el-input v-model="messageForm.phone" placeholder="请输入电话"></el-input>
          </el-form-item>
        </div>

        <el-form-item label="邮箱">
          <el-input v-model="messageForm.email" placeholder="请输入邮箱"></el-input>
        </el-form-item>

        <el-form-item label="留言内容">
          <el-input v-model="messageForm.content" type="textarea" rows="4" placeholder="请输入留言内容（10~500字符）"></el-input>
        </el-form-item>

        <div class="form-buttons">
          <el-button type="default" class="reset-btn" @click="resetForm">重置</el-button>
          <el-button type="danger" class="submit-btn" @click="submitForm">提交</el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';

// 留言表单数据
const messageForm = ref({
  name: '',
  phone: '',
  email: '',
  content: ''
});

// 重置表单
const resetForm = () => {
  messageForm.value = {
    name: '',
    phone: '',
    email: '',
    content: ''
  };
};

// 提交表单
const submitForm = () => {
  // 表单验证
  if (!messageForm.value.name) {
    ElMessage.warning('请输入姓名');
    return;
  }

  if (!messageForm.value.phone) {
    ElMessage.warning('请输入电话');
    return;
  }

  // 模拟表单提交
  ElMessage.success('留言已提交，我们将尽快与您联系');
  resetForm();
};
</script>

<style scoped>
.contact-form {
  width: 100%;
}

.form-title {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 30px;
}

.form-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.form-buttons {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin-top: 20px;
}

.reset-btn,
.submit-btn {
  min-width: 120px;
  font-size: 16px;
}

.submit-btn {
  background-color: #c9161c;
  border: none;
}

.submit-btn:hover {
  background-color: #a61016;
}

@media (max-width: 768px) {
  .form-buttons {
    gap: 20px;
  }
}
</style>