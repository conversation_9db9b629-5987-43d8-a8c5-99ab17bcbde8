import { Provide } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { ProductCategoryEntity } from '../entity/category';
import { ProductProductCategoryEntity } from '../entity/product-category';

/**
 * 产品分类服务
 */
@Provide()
export class ProductCategoryService extends BaseService {
    @InjectEntityModel(ProductCategoryEntity)
    productCategoryEntity: Repository<ProductCategoryEntity>;

    @InjectEntityModel(ProductProductCategoryEntity)
    productProductCategoryEntity: Repository<ProductProductCategoryEntity>;

    /**
     * 获取扁平化分类列表
     */
    async list(query: any) {
        const { isShowHome, isShowBusinessScope } = query;
        const where: any = { status: 1 };

        if (isShowHome !== undefined) {
            where.isShowHome = isShowHome;
        }

        if (isShowBusinessScope !== undefined) {
            where.isShowBusinessScope = isShowBusinessScope;
        }

        return this.productCategoryEntity.find({
            where,
            order: { sort: 'ASC', createTime: 'ASC' }
        });
    }

    /**
     * 分页查询
     */
    async categoryPage(query: any) {
        const { current = 1, size = 20, keyWord, status, isShowHome, isShowBusinessScope } = query;
        const where: any = {};

        if (keyWord) {
            where.name = { $like: `%${keyWord}%` };
        }

        if (status !== undefined) {
            where.status = status;
        }

        if (isShowHome !== undefined) {
            where.isShowHome = isShowHome;
        }

        if (isShowBusinessScope !== undefined) {
            where.isShowBusinessScope = isShowBusinessScope;
        }

        const [list, total] = await this.productCategoryEntity.findAndCount({
            where,
            order: { sort: 'ASC', createTime: 'DESC' },
            skip: (current - 1) * size,
            take: size,
        });

        return {
            list,
            pagination: {
                page: current,
                size,
                total,
            },
        };
    }

    /**
     * 新增分类
     */
    async addCategory(data: any) {
        return this.productCategoryEntity.save(data);
    }

    /**
     * 更新分类
     */
    async updateCategory(data: any) {
        const { id, ...updateData } = data;

        await this.productCategoryEntity.update(id, updateData);
        return this.productCategoryEntity.findOne({ where: { id } });
    }

    /**
     * 删除分类
     */
    async deleteCategory(ids: number[]) {
        // 检查是否有产品使用此分类
        for (const id of ids) {
            const productCount = await this.productProductCategoryEntity.count({
                where: { categoryId: id }
            });
            if (productCount > 0) {
                throw new Error('存在关联产品，无法删除');
            }
        }

        return this.productCategoryEntity.delete(ids);
    }

    /**
     * 获取分类详情
     */
    async info(id: number) {
        return this.productCategoryEntity.findOne({ where: { id } });
    }

    /**
     * 根据产品ID获取分类列表
     */
    async getByProductId(productId: number) {
        const relations = await this.productProductCategoryEntity.find({
            where: { productId },
            relations: ['category']
        });
        return relations.map(rel => rel.category);
    }

    /**
     * 设置产品分类
     */
    async setProductCategories(productId: number, categoryIds: number[]) {
        // 删除原有关联
        await this.productProductCategoryEntity.delete({ productId });

        // 添加新关联
        if (categoryIds && categoryIds.length > 0) {
            const relations = categoryIds.map(categoryId => ({
                productId,
                categoryId
            }));
            await this.productProductCategoryEntity.save(relations);
        }
    }
}