import { Inject } from '@midwayjs/core';
import { <PERSON><PERSON><PERSON>roller, BaseController } from '@cool-midway/core';
import { BannerInfoEntity } from '../../entity/info';
import { BannerInfoService } from '../../service/info';

/**
 * 轮播图信息
 */
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: BannerInfoEntity,
  service: BannerInfoService,
  pageQueryOp: {
    keyWordLikeFields: ['a.position'],
    fieldEq: ['a.status'],
    where: async ctx => {
      const { createTime } = ctx.request.body;
      if (createTime && createTime.length === 2) {
        return [
          [
            'a.createTime BETWEEN :start AND :end',
            { start: createTime[0], end: createTime[1] },
          ],
        ];
      }
      return [];
    },
  },
})
export class AdminBannerInfoController extends BaseController {
  @Inject()
  bannerInfoService: BannerInfoService;
}
