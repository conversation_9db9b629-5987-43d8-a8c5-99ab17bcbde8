<script setup>
import { ref, onMounted, reactive } from 'vue';
import TheHeader from '../components/TheHeader.vue';
import TheFooter from '../components/TheFooter.vue';
import { ElPagination, ElMessage, ElCarousel, ElCarouselItem } from 'element-plus';
import { commonApi, productApi, categoryApi } from '../api';

// 引入图片资源
import bannerImg from '@/assets/business/banner.png';
import adv1Img from '@/assets/business/adv1.png';
import adv2Img from '@/assets/business/adv2.png';
import icon1Img from '@/assets/business/icon1.png';
import icon2Img from '@/assets/business/icon2.png';
import tag1Img from '@/assets/business/tag1.png';
import productDetailImg from '@/assets/down.png';
import { useRouter } from 'vue-router';

const router = useRouter()

// 当前语言
const currentLanguage = ref('chinese_simplified');

// 轮播图数据
const bannerList = ref([]);
const bannerLoading = ref(false);

// 请求完成状态追踪
const requestStatus = reactive({
  banner: false,
  cities: false,
  categories: false,
  products: false,
  recommend: false,
  adverts: false
});

// 检查所有请求是否完成并执行翻译
const checkAllRequestsComplete = () => {
  const allComplete = Object.values(requestStatus).every(status => status === true);
  if (allComplete) {
    setTimeout(() => {
      translate.execute();
    }, 50);
  }
};

// 获取Banner数据
const fetchBannerData = async () => {
  bannerLoading.value = true;
  try {
    const res = await commonApi.getBannerList({ position: 2 });

    if (res && res.data) {
      bannerList.value = res.data.pics.map((item, index) => ({
        id: index + 1,
        image: item,
        link: res.data.path || '#'
      }));
    }
  } catch (error) {
    console.error('获取Banner数据失败:', error);
    ElMessage.error('获取Banner数据失败，使用默认图片');
    // 使用默认数据
    bannerList.value = [
      { id: 1, image: bannerImg, link: '#' },
    ];
  } finally {
    bannerLoading.value = false;
    requestStatus.banner = true;
    checkAllRequestsComplete();
  }
};

// 城市选择
const cityOptions = ref(['全部']);
const selectedCity = ref('全部');

// 获取城市列表
const fetchCities = async () => {
  try {
    const res = await productApi.getCities();
    if (res && res.data) {
      cityOptions.value = ['全部', ...res.data,];
    }
  } catch (error) {
    console.error('获取城市列表失败:', error);
    ElMessage.error('获取城市列表失败，使用默认数据');
    cityOptions.value = ['全部'];
  } finally {
    requestStatus.cities = true;
    checkAllRequestsComplete();
  }
};

// 主题分类列表
const themesList = ref([]);

// 获取分类数据
const fetchCategories = async () => {
  try {
    const res = await categoryApi.getCategoryList({ isShowBusinessScope: 1 });
    if (res && res.data) {
      // 添加"全部"选项作为第一个
      themesList.value = [
        { id: 0, name: '全部', active: true, detailFile: null },
        ...res.data.map((item, index) => ({
          id: item.id,
          name: item.name,
          active: false,
          detailFile: item.detailFile
        }))
      ];
    }
  } catch (error) {
    console.error('获取分类数据失败:', error);
    ElMessage.error('获取分类数据失败，使用默认数据');
    // 使用默认数据
    themesList.value = [
      { id: 0, name: '全部', active: true },
      { id: 1, name: '跟团行程', active: false },
      { id: 2, name: '合作产品', active: false },
      { id: 3, name: '研学产品', active: false },
      { id: 4, name: 'VIP私人订制', active: false },
      { id: 5, name: '实惠服务', active: false },
    ];
  } finally {
    requestStatus.categories = true;
    checkAllRequestsComplete();
    // 分类数据获取完成后，获取产品列表
    fetchProductList();
  }
};

// 当前页码
const currentPage = ref(1);
const pageSize = ref(6);
const total = ref(0);

// 产品列表数据
const hotelList = ref([]);
const listLoading = ref(false);

// 推荐列表
const recommendList = ref([]);

// 右侧广告数据
const sidebarAds = reactive([]);

// 获取广告列表
const fetchAdvertList = async () => {
  try {
    const res = await commonApi.getAdvertList();
    if (res && res.data) {
      sidebarAds.splice(0, sidebarAds.length, ...res.data.map(item => ({
        id: item.id,
        image: item.coverImage,
        link: item.jumpLink || '',
        title: item.name
      })));
    }
  } catch (error) {
    console.error('获取广告数据失败:', error);
    ElMessage.error('获取广告数据失败，使用默认数据');
  } finally {
    requestStatus.adverts = true;
    checkAllRequestsComplete();
  }
};

// 处理广告点击
const handleAdClick = (ad) => {
  if (!ad.link || ad.link === '' || ad.link === '#') {
    // 如果链接为空，不执行跳转
    return;
  }
  // 如果链接不为空，执行跳转
  window.open(ad.link, '_blank');
};

// 获取产品列表
const fetchProductList = async () => {
  listLoading.value = true;
  try {
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      city: selectedCity.value !== '全部' ? selectedCity.value : undefined,
    };

    // 获取当前选中的分类ID
    const activeTheme = themesList.value.find(item => item.active);
    if (activeTheme && activeTheme.id !== 0) {
      params.categoryId = activeTheme.id;
    }

    const res = await productApi.getProductList(params);
    if (res && res.data) {
      hotelList.value = res.data.list.map(item => ({
        id: item.id,
        name: item.name,
        views: item.views || 0,
        location: item.city || '未知地点',
        price: item.price,
        image: item.cover || 'https://img1.baidu.com/it/u=1640439575,1051556704&fm=253&fmt=auto&app=120&f=JPEG?w=500&h=651',
        description: item.introduce || '暂无介绍',
        tag: item.tag || ''
      }));
      total.value = res.data.total;
    }
  } catch (error) {
    console.error('获取产品列表失败:', error);
    ElMessage.error('获取产品列表失败');
  } finally {
    listLoading.value = false;
    requestStatus.products = true;
    checkAllRequestsComplete();
  }
};

// 获取推荐产品
const fetchRecommendedProducts = async () => {
  try {
    const res = await productApi.getRecommendedProducts(5);
    if (res && res.data) {
      recommendList.value = res.data.map(item => ({
        id: item.id,
        title: item.name,
        price: item.price,
        originalPrice: item.originalPrice || item.price,
        image: item.cover || adv1Img,
        soldCount: item.bookingCount || 0
      }));
    }
  } catch (error) {
    console.error('获取推荐产品失败:', error);
  } finally {
    requestStatus.recommend = true;
    checkAllRequestsComplete();
  }
};

// 获取当前选中的主题
const getActiveTheme = () => {
  const activeTheme = themesList.value.find(item => item.active);
  return activeTheme ? activeTheme.name : '全部';
};

// 获取当前选中分类的详情文件
const getActiveThemeDetailFile = () => {
  const activeTheme = themesList.value.find(item => item.active);
  return activeTheme && activeTheme.id !== 0 ? activeTheme.detailFile : null;
};

// 处理产品明细下载点击事件
const handleDetailFileDownload = async () => {
  const detailFile = getActiveThemeDetailFile();
  if (!detailFile) {
    ElMessage.error('明细文件不存在');
    return;
  }

  try {
    // 通过fetch获取文件内容，这样可以强制下载而不是打开
    const response = await fetch(detailFile.replace('8001', '8080'));
    if (!response.ok) {
      throw new Error('文件下载失败');
    }

    const blob = await response.blob();

    // 从URL中提取文件名，如果没有则使用默认名称
    const url = new URL(detailFile.replace('8001', '8080'));
    let fileName = url.pathname.split('/').pop() || `产品明细_${getActiveTheme()}`;

    // 如果文件名没有扩展名，根据Content-Type添加适当的扩展名
    if (!fileName.includes('.')) {
      const contentType = response.headers.get('content-type');
      if (contentType) {
        // 图片格式
        if (contentType.includes('image/jpeg')) fileName += '.jpg';
        else if (contentType.includes('image/png')) fileName += '.png';
        else if (contentType.includes('image/gif')) fileName += '.gif';
        else if (contentType.includes('image/webp')) fileName += '.webp';
        else if (contentType.includes('image/bmp')) fileName += '.bmp';
        else if (contentType.includes('image/tiff')) fileName += '.tiff';
        else if (contentType.includes('image/svg')) fileName += '.svg';
        // 文档格式
        else if (contentType.includes('application/pdf')) fileName += '.pdf';
        else if (contentType.includes('application/msword')) fileName += '.doc';
        else if (contentType.includes('application/vnd.openxmlformats-officedocument.wordprocessingml.document')) fileName += '.docx';
        // Excel格式
        else if (contentType.includes('application/vnd.ms-excel')) fileName += '.xls';
        else if (contentType.includes('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')) fileName += '.xlsx';
        // PowerPoint格式
        else if (contentType.includes('application/vnd.ms-powerpoint')) fileName += '.ppt';
        else if (contentType.includes('application/vnd.openxmlformats-officedocument.presentationml.presentation')) fileName += '.pptx';
        else if (contentType.includes('application/vnd.ms-powerpoint.presentation.macroEnabled.12')) fileName += '.pptm';
        else if (contentType.includes('application/vnd.openxmlformats-officedocument.presentationml.slideshow')) fileName += '.ppsx';
        else if (contentType.includes('application/vnd.ms-powerpoint.slideshow.macroEnabled.12')) fileName += '.ppsm';
        // 压缩格式
        else if (contentType.includes('application/zip')) fileName += '.zip';
        else if (contentType.includes('application/x-rar-compressed')) fileName += '.rar';
        else if (contentType.includes('application/x-7z-compressed')) fileName += '.7z';
        else if (contentType.includes('application/gzip')) fileName += '.gz';
        else if (contentType.includes('application/x-tar')) fileName += '.tar';
        // 可执行文件
        else if (contentType.includes('application/vnd.android.package-archive')) fileName += '.apk';
        else if (contentType.includes('application/x-msdownload')) fileName += '.exe';
        // 文本格式
        else if (contentType.includes('text/plain')) fileName += '.txt';
        else if (contentType.includes('text/csv')) fileName += '.csv';
        else if (contentType.includes('application/json')) fileName += '.json';
        else if (contentType.includes('application/xml')) fileName += '.xml';
        // 视频格式
        else if (contentType.includes('video/mp4')) fileName += '.mp4';
        else if (contentType.includes('video/avi')) fileName += '.avi';
        else if (contentType.includes('video/quicktime')) fileName += '.mov';
        else if (contentType.includes('video/x-msvideo')) fileName += '.avi';
        // 音频格式
        else if (contentType.includes('audio/mpeg')) fileName += '.mp3';
        else if (contentType.includes('audio/wav')) fileName += '.wav';
        else if (contentType.includes('audio/ogg')) fileName += '.ogg';
      }
    }

    // 创建下载链接
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = fileName;
    link.style.display = 'none';

    // 添加到DOM，触发下载，然后清理
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 释放URL对象
    window.URL.revokeObjectURL(downloadUrl);

    ElMessage.success('开始下载明细文件');
  } catch (error) {
    console.error('下载失败:', error);
    ElMessage.error('文件下载失败，请稍后重试');
  }
};

// 切换主题分类
const changeTheme = (id) => {
  themesList.value.forEach(item => {
    item.active = item.id === id;
  });
  currentPage.value = 1; // 重置页码
  fetchProductList(); // 重新获取数据
};

// 切换城市
const changeCity = (city) => {
  selectedCity.value = city;
  currentPage.value = 1; // 重置页码
  fetchProductList(); // 重新获取数据
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchProductList();
};

// 查询产品详情
const viewDetail = (id) => {
  router.push(`/details?id=${id}`)
};

onMounted(() => {
  // 设置语言检测
  setTimeout(() => {
    currentLanguage.value = localStorage.getItem('to') || 'chinese_simplified';
  }, 200);
  setTimeout(() => {
    currentLanguage.value = localStorage.getItem('to') || 'chinese_simplified';
  }, 800);
  setTimeout(() => {
    currentLanguage.value = localStorage.getItem('to') || 'chinese_simplified';
  }, 2000);

  // 加载数据
  fetchBannerData();
  fetchCities();
  fetchCategories();
  fetchRecommendedProducts();
  fetchAdvertList();
});
</script>

<template>
  <div class="business-view">
    <TheHeader />

    <!-- 主要内容区域，包含背景图片 -->
    <div class="main-content-wrapper">
      <!-- 顶部大图轮播 -->
      <div class="banner-container">
        <el-carousel v-if="bannerList.length > 0" v-loading="bannerLoading" height="400px" :interval="5000"
          class="banner-carousel">
          <el-carousel-item v-for="item in bannerList" :key="item.id">
            <div class="h-full w-full">
              <img :src="item.image" class="w-full h-full object-cover" />
            </div>
          </el-carousel-item>
        </el-carousel>

        <!-- 如果没有banner数据且不在加载中，显示默认图片 -->
        <div v-else-if="!bannerLoading" class="banner-image">
          <img :src="bannerImg" alt="业务范围" class="w-full h-full object-cover" />
        </div>

        <!-- 加载状态 -->
        <div v-else class="banner-loading" v-loading="bannerLoading">
          <div class="h-[400px] bg-gray-200"></div>
        </div>
      </div>

      <div class="container mx-auto px-4 py-8">
        <div class="flex flex-wrap">

          <!-- 筛选区域 -->
          <div class="filter-section bg-white mb-6 rounded">
            <!-- 所在城市 -->
            <div class="city-filter border-b border-[#d0d0d0]">
              <div class="location-icon-wrapper">
                <img :src="icon1Img" alt="当前城市" class="w-[28px] h-[28px] ml-4" />
                <span class="text-lg font-medium ml-4" style="margin-top: 3px;">所在城市：</span>
              </div>
              <div class="city-list">
                <a v-for="(city, index) in cityOptions" :key="index" href="javascript:void(0)" class="city-item"
                  :class="city === selectedCity ? 'selected-btn' : 'normal-btn'" @click="changeCity(city)">
                  {{ city }}
                </a>
              </div>
            </div>

            <!-- 主题分类 -->
            <div class="theme-filter border-b border-[#d0d0d0] relative">
              <div class="theme-icon-wrapper">
                <img :src="icon2Img" alt="主题分类" class="w-[28px] h-[28px] ml-4" />
                <span class="text-lg font-medium ml-4">主题分类：</span>
              </div>
              <div class="theme-list">
                <a v-for="theme in themesList" :key="theme.id" href="javascript:void(0)" class="theme-item"
                  :class="theme.active ? 'selected-btn' : 'normal-btn'" @click="changeTheme(theme.id)">
                  {{ theme.name }}
                </a>
              </div>

              <!-- 产品明细按钮 -->
              <a v-if="getActiveThemeDetailFile()" href="javascript:void(0)" class="product-detail-btn"
                @click="handleDetailFileDownload">
                <span class="text-[#c7161b] font-medium mr-2">产品明细</span>
                <img :src="productDetailImg" alt="产品明细" class="w-[16px] h-[16px]">
              </a>
            </div>
          </div>


          <!-- 左侧主内容区域 -->
          <div class="w-full lg:w-3/4 lg:pr-6">


            <!-- 酒店列表 -->
            <div class="grid gap-6 mb-10 border-t border-[#d0d0d0] pt-6" v-loading="listLoading">
              <div v-for="hotel in hotelList" :key="hotel.id"
                class="flex border-b border-dashed border-[#BABABA] pb-6 relative">
                <!-- 热推标签 -->
                <div v-if="hotel.tag" class="absolute top-0 left-0 z-10 transform -translate-y-0.5 -translate-x-0.5">
                  <img :src="tag1Img" alt="热推标签" class="w-16 h-16" />
                </div>

                <!-- 酒店图片 - 左侧 -->
                <div class="w-[280px] h-[190px] mr-6 overflow-hidden flex-shrink-0">
                  <img :src="hotel.image" :alt="hotel.name" class="w-full h-full object-cover">
                </div>

                <!-- 酒店信息 - 中间 -->
                <div class="flex-1 mr-6 relative top-[-2px]">
                  <div class="mb-4 leading-relaxed">
                    <h3 class="product-title inline text-[24px] font-bold text-gray-800 mr-3"
                      style="line-height: 1.1;padding-bottom: 10px;">{{ hotel.name }}</h3>

                  </div>

                  <div class="text-[16px] text-gray-600 mb-3">城市：{{ hotel.location }}</div>

                  <p class="text-[15px] text-gray-600 description-text">
                    特点：{{ hotel.description }}
                  </p>
                </div>

                <!-- 价格信息 - 右侧 -->
                <div class="w-[150px] flex flex-col items-end justify-evenly">
                  <div
                    class="inline-block px-3 h-[29px] bg-[#FFA118] rounded-[15px] align-middle text-white text-sm whitespace-nowrap leading-[29px] relative top-[-5px]">
                    已成功{{ hotel.views }}人
                  </div>
                  <!-- 俄语价格格式 -->
                  <div v-if="currentLanguage === 'russian'"
                    class="text-red-600 font-bold text-[18px] mb-2 text-right whitespace-nowrap">Цена от $<span
                      class="font-bold text-[30px]">{{ Math.floor(hotel.price) }}</span></div>
                  <!-- 中文价格格式 -->
                  <div v-else class="flex items-baseline mb-2">
                    <span class="text-[14px] text-[#A3A3A3] mr-2">价格</span>
                    <span class="text-red-600 font-bold text-[24px]">$</span>
                    <span class="text-red-600 font-bold text-[40px]">{{ Math.floor(hotel.price) }}</span>
                    <span class="text-red-600 text-[18px] ml-1">起</span>
                  </div>

                  <button class="w-[136px] h-[45px] bg-[#c7161b] text-white rounded-[4px] hover:opacity-90 transition"
                    @click="viewDetail(hotel.id)">
                    查看详情
                  </button>
                </div>
              </div>

              <!-- 空状态 -->
              <div v-if="!listLoading && hotelList.length === 0" class="text-center py-20">
                <div class="text-gray-400 text-lg">暂无产品数据</div>
              </div>
            </div>

            <!-- 分页控件 -->
            <div class="flex justify-center my-8">
              <el-pagination v-model:currentPage="currentPage" :page-size="pageSize" :total="total"
                layout="prev, pager, next, jumper, goto" @current-change="handleCurrentChange" background />
            </div>
          </div>

          <!-- 右侧边栏 -->
          <div class="w-full lg:w-1/4">
            <!-- 广告区域 -->
            <div class="sidebar-ads mb-6 pl-4" v-if="sidebarAds.length > 0">
              <div v-for="ad in sidebarAds" :key="ad.id" class="mb-4">
                <div class="block cursor-pointer" @click="handleAdClick(ad)">
                  <img :src="ad.image" :alt="ad.title" class="w-full shadow-sm hover:shadow-md transition">
                </div>
              </div>
            </div>

            <!-- 同类推荐 -->
            <div class="bg-white rounded-lg overflow-hidden shadow-sm p-4 mb-6 pr-0">
              <h3 class="text-lg font-bold pb-3 mb-4 text-[#595959] border-b border-[#ebebeb]">同类推荐</h3>
              <div class="space-y-6">
                <div v-for="(item, index) in recommendList.slice(0, 5)" @click="viewDetail(item.id)" :key="index"
                  class="mb-6 last:mb-0">
                  <div class="w-full mb-2 overflow-hidden">
                    <img :src="item.image" :alt="item.title" class="w-full h-[167px] object-cover rounded">
                  </div>
                  <div>
                    <h4 class="text-gray-800 font-medium mb-2 text-base">{{ item.title }}</h4>
                    <div class="flex justify-between items-center">
                      <div class="flex items-baseline">
                        <span class="text-red-600 font-medium text-[19px]">${{ Math.floor(item.price) }}</span>
                        <span class="text-gray-400 line-through ml-2 text-sm">${{ Math.floor(item.originalPrice)
                        }}</span>
                      </div>
                      <div class="text-[#7A7A7A] text-[14px]">
                        预订{{ item.soldCount }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <TheFooter />
  </div>
</template>

<style scoped>
.business-view {
  min-height: 100vh;
  padding-top: 65px;
  background-color: #fff;
}

/* 主要内容区域背景图片 */
.main-content-wrapper {
  background-image: url('@/assets/images/home-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  min-height: calc(100vh - 65px);
}

@media (min-width: 1400px) and (max-width: 1750px) {
  .product-title {
    font-size: 26px;
  }

  .main-content-wrapper {}
}

.banner-container {
  margin-top: 20px;
}

.banner-image {
  height: 400px;
  width: 100%;
  overflow: hidden;
}

.banner-loading {
  height: 400px;
  width: 100%;
}

.filter-section {
  width: 100%;
  border: 1px solid #d0d0d0;
  border-top: 3px solid #c7161b;
}

.city-filter,
.theme-filter {
  display: flex;
  align-items: flex-start;
  padding: 14px 0;
  min-height: 40px;
}

.location-icon-wrapper,
.theme-icon-wrapper {
  display: flex;
  align-items: center;
  min-width: 130px;
  flex-shrink: 0;
}

.city-list,
.theme-list {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 4px 8px;
  padding: 0 12px;
}

.city-item,
.theme-item {
  margin: 2px 0;
}

/* 自定义轮播箭头样式 */
:deep(.el-carousel__arrow) {
  background-color: rgba(255, 255, 255, 0.5);
  width: 36px;
  height: 36px;
  border-radius: 50%;
}

:deep(.el-carousel__arrow:hover) {
  background-color: rgba(255, 255, 255, 0.8);
}

:deep(.el-carousel__arrow--left) {
  left: 20px;
}

:deep(.el-carousel__arrow--right) {
  right: 20px;
}

/* 修改Element Plus分页组件样式 */
:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background-color: #c7161b;
}

:deep(.el-pagination.is-background .el-pager li:not(.is-disabled):hover) {
  color: #c7161b;
}

:deep(.el-pagination button:hover) {
  color: #c7161b;
}

/* 添加新的选中/未选中样式 */
.selected-btn {
  min-width: 88px;
  width: fit-content;
  padding: 0 10px;
  height: 32px;
  background: #c9161c;
  border-radius: 4px;
  color: #fff;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.normal-btn {
  padding: 6px 12px;
  color: #4b5563;
  font-size: 14px;
  height: 32px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.normal-btn:hover {
  color: #c7161b;
}

/* 产品明细按钮样式 */
.product-detail-btn {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  z-index: 10;
}

/* 描述文本样式 - 限制7行 */
.description-text {
  display: -webkit-box;
  -webkit-line-clamp: 8;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
