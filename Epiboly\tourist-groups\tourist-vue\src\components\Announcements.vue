<template>
  <div class="announcements">
    <h2 class="component-title">公告</h2>
    
    <div v-if="announcements.length > 0" class="announcements-list">
      <div v-for="announcement in announcements" :key="announcement.id" class="announcement-item">
        <div class="announcement-header">
          <h3 class="announcement-title">{{ announcement.title }}</h3>
          <span class="announcement-date">{{ announcement.publishDate }}</span>
        </div>
        <div class="announcement-content">
          <p>{{ announcement.content }}</p>
        </div>
      </div>
    </div>
    
    <div v-else class="empty-data">
      <el-empty description="暂无公告信息"></el-empty>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';

// 模拟公告数据
const announcements = ref([
  {
    id: 1,
    title: '关于旅游拼团服务升级的通知',
    content: '为了给用户提供更好的服务体验，我们将于近期对旅游拼团系统进行升级优化...',
    publishDate: '2023-05-20',
    type: 'system'
  },
  {
    id: 2,
    title: '端午节假期旅游安全提醒',
    content: '端午节假期即将到来，为确保大家的出行安全，请注意以下事项...',
    publishDate: '2023-05-18',
    type: 'safety'
  },
  {
    id: 3,
    title: '新增热门旅游线路推荐',
    content: '夏季旅游旺季来临，我们新增了多条热门旅游线路，包括海滨度假、山地避暑等...',
    publishDate: '2023-05-15',
    type: 'promotion'
  },
  {
    id: 4,
    title: '用户服务协议更新说明',
    content: '根据相关法律法规要求，我们对用户服务协议进行了更新，主要涉及...',
    publishDate: '2023-05-10',
    type: 'policy'
  }
]);

// 公告类型文字映射
const getTypeText = (type) => {
  const typeMap = {
    'system': '系统公告',
    'safety': '安全提醒',
    'promotion': '活动推广',
    'policy': '政策更新'
  };
  return typeMap[type] || '一般公告';
};

// 查看详情
const viewDetail = (announcementId) => {
  console.log('查看公告详情:', announcementId);
  ElMessage.info('查看公告详情功能开发中...');
};
</script>

<style scoped>
.announcements {
  padding: 20px;
}

.component-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
  border-left: 4px solid #c8161b;
  padding-left: 10px;
}

.announcements-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.announcement-item {
  background-color: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  transition: box-shadow 0.3s ease;
}

.announcement-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.announcement-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.announcement-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin: 0;
  flex: 1;
  margin-right: 15px;
}

.announcement-date {
  font-size: 14px;
  color: #999;
  white-space: nowrap;
}

.announcement-content {
  margin-bottom: 15px;
}

.announcement-content p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.announcement-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.announcement-type {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: bold;
}

.announcement-type.system {
  background-color: #e6f7ff;
  color: #1890ff;
}

.announcement-type.safety {
  background-color: #fff7e6;
  color: #fa8c16;
}

.announcement-type.promotion {
  background-color: #f6ffed;
  color: #52c41a;
}

.announcement-type.policy {
  background-color: #fff2f0;
  color: #c8161b;
}

.empty-data {
  margin-top: 40px;
  text-align: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .announcement-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .announcement-title {
    margin-right: 0;
    margin-bottom: 5px;
  }
  
  .announcement-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style> 