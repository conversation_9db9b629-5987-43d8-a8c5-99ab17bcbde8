import { post, get } from '../utils/request';

// 留言和通知相关API
const messageApi = {
  // 提交留言
  submitMessage: (data) => post('/app/message/submit', data),

  // 通知相关API
  // 获取消息列表（通知/公告）
  getNotificationList: (data) => post('/app/notification/list', data),

  // 标记消息为已读
  markAsRead: (data) => post('/app/notification/markAsRead', data),

  // 获取用户未读消息数量
  getUnreadCount: (userId) => get(`/app/notification/unreadCount?userId=${userId}`),

  // 获取用户消息统计
  getUserStatistics: (data) => post('/app/notification/statistics', data),

  // 批量标记消息为已读
  markMultipleAsRead: (data) => post('/app/notification/markMultipleAsRead', data),
};

export default messageApi; 