import { ALL, Config, Middleware, InjectClient } from '@midwayjs/core';
import { NextFunction, Context } from '@midwayjs/koa';
import { IMiddleware, Init, Inject } from '@midwayjs/core';
import * as jwt from 'jsonwebtoken';
import * as _ from 'lodash';
import { CoolCommException, CoolUrlTagData, TagTypes } from '@cool-midway/core';
import { CachingFactory, MidwayCache } from '@midwayjs/cache-manager';
import { Utils } from '../../../comm/utils';

/**
 * 用户
 */
@Middleware()
export class UserMiddleware implements IMiddleware<Context, NextFunction> {
  @Config(ALL)
  coolConfig;

  @Inject()
  coolUrlTagData: CoolUrlTagData;

  @InjectClient(CachingFactory, 'default')
  midwayCache: MidwayCache;

  @Config('module.user.jwt')
  jwtConfig;

  @Config('module.user.ignoreUrls')
  ignoreUrlsConfig;

  ignoreUrls: string[] = [];

  @Config('koa.globalPrefix')
  prefix;

  @Inject()
  utils: Utils;

  @Init()
  async init() {
    const defaultIgnoreUrls = this.coolUrlTagData.byKey(TagTypes.IGNORE_TOKEN, 'app');
    //  '/app/order/my-orders', '/app/order/my-orders-stats',
    this.ignoreUrls = [...new Set([...defaultIgnoreUrls, '/app/user/login/forgetPassword', '/app/site/companies', '/app/order/join-group', '/app/order/group-info', '/app/advert/list', '/app/product/cities', '/app/product/category/list', '/app/notification/list', '/app/product/list', '/app/product/detail', '/app/product/recommended', '/app/product/category', '/app/product/search', '/app/product/hot-cities', '/app/product/stats', '/app/attraction/list', '/app/order/create', '/app/order/query', '/app/order/confirm', '/app/types/getByType', '/app/types/getTypesGames', '/app/site/info', '/app/game/getGameList', '/app/journal/journalList', '/app/base/comm/upload', '/app/magazine/query', '/app/magazine/info', '/app/notice/list', '/app/banner/list', '/app/news/list', '/app/journal/list', '/app/comm/upload', '/app/member/list', '/app/member/listByType', '/app/culture/info', '/app/reservation/list', '/app/message/submit', '/app/cooperation/info', '/app/application/submit'])];
  }

  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      let { url } = ctx;
      url = url.replace(this.prefix, '').split('?')[0];
      if (_.startsWith(url, '/app/')) {
        const token = ctx.get('Authorization');

        try {
          ctx.user = jwt.verify(token, this.jwtConfig.secret);

          if (ctx.user.isRefresh) {
            throw new CoolCommException('登录失效~');
          }

          // 检查用户是否被禁用（删除申请后的强制下线）
          if (ctx.user && ctx.user.id) {
            const isDisabled = await this.midwayCache.get(`user:disabled:${ctx.user.id}`);
            if (isDisabled) {
              console.log(`用户 ${ctx.user.id} 已被强制下线（申请被删除）`);
              ctx.user = null; // 清除用户信息
              throw new CoolCommException('登录失效~');
            }
          }
        } catch (error) {
          if (error instanceof CoolCommException && error.message === '登录失效~') {
            ctx.user = null; // 确保用户信息被清除
            throw error; // 重新抛出异常
          }
          if (url === '/app/user/info/person') {
            console.log('JWT验证失败:', error.message);
          }
          ctx.user = null; // JWT验证失败时清除用户信息
        }
        // 使用matchUrl方法来检查URL是否应该被忽略
        const isIgnored = this.ignoreUrls.some(pattern =>
          this.utils.matchUrl(pattern, url)
        );
        if (isIgnored) {
          await next();
          return;
        } else {
          if (!ctx.user) {
            ctx.status = 401;
            throw new CoolCommException('登录失效~');
          }
        }
      }
      await next();
    };
  }
}
