import { Inject, Post, Body, Get, Query } from '@midwayjs/core';
import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core';
import { NoticeNoticeEntity } from '../../entity/notice';
import { NoticeNoticeService } from '../../service/notice';
import { BaseSysUserEntity } from '../../../base/entity/sys/user';

/**
 * 通知信息
 */
@CoolController({
  api: ['delete', 'update', 'info', 'list', 'page'],
  entity: NoticeNoticeEntity,
  service: NoticeNoticeService,
  pageQueryOp: {
    keyWordLikeFields: ['a.title'],
    fieldEq: ['a.type', 'a.sendStatus'],
    join: [
      {
        entity: BaseSysUserEntity,
        alias: 'b',
        condition: 'a.senderId = b.id',
        type: 'leftJoin',
      },
    ],
    select: ['a.*', 'b.name as senderName'],
    where: async ctx => {
      const { publishTime } = ctx.request.body;
      if (publishTime && publishTime.length === 2) {
        return [
          [
            'a.publishTime BETWEEN :start AND :end',
            { start: publishTime[0], end: publishTime[1] },
          ],
        ];
      }
    },
  },
})
export class AdminNoticeNoticeController extends BaseController {
  @Inject()
  noticeNoticeService: NoticeNoticeService;

  /**
   * 新增通知（重写，调用batchSend方法）
   */
  @Post('/add')
  async addNotice(@Body() body: any) {
    console.log('收到新增通知请求:', body);
    return this.ok(await this.noticeNoticeService.batchSend(body));
  }

  /**
   * 批量发送通知
   */
  @Post('/batchSend')
  async batchSend(@Body() body: any) {
    console.log('收到批量发送请求:', body);
    return this.ok(await this.noticeNoticeService.batchSend(body));
  }

  /**
   * 重新发送通知
   */
  @Post('/resend')
  async resend(@Body() body: { id: number }) {
    return this.ok(await this.noticeNoticeService.resend(body.id));
  }

  /**
   * 获取发送详情
   */
  @Get('/sendDetail')
  async getSendDetail(@Query('id') id: string) {
    const noticeId = parseInt(id);
    if (isNaN(noticeId)) {
      return this.fail('无效的通知ID');
    }
    return this.ok(await this.noticeNoticeService.getSendDetail(noticeId));
  }
}
