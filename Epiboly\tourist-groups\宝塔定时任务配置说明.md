# 宝塔面板定时任务配置说明

## 脚本文件位置
将 `宝塔定时任务脚本.sh` 上传到服务器，建议放在项目目录下：
```bash
/www/wwwroot/tourist-api/宝塔定时任务脚本.sh
```

## 设置脚本权限
```bash
chmod +x /www/wwwroot/tourist-api/宝塔定时任务脚本.sh
```

## 宝塔面板定时任务配置

登录宝塔面板 → 计划任务 → 添加任务

### 1. 定时任务测试
- **任务名称**: 订单模块-定时任务测试
- **执行周期**: 每分钟
- **脚本内容**:
```bash
/www/wwwroot/tourist-api/宝塔定时任务脚本.sh test
```

### 2. 更新过期拼团订单
- **任务名称**: 订单模块-更新过期拼团订单
- **执行周期**: 每天
- **执行时间**: 01:00
- **脚本内容**:
```bash
/www/wwwroot/tourist-api/宝塔定时任务脚本.sh expired
```

### 3. 更新未成团过期订单
- **任务名称**: 订单模块-更新未成团过期订单
- **执行周期**: 每天
- **执行时间**: 01:30
- **脚本内容**:
```bash
/www/wwwroot/tourist-api/宝塔定时任务脚本.sh unfulfilled
```

## 手动清理测试数据
当需要清理测试定时任务产生的数据时，可以手动执行：
```bash
/www/wwwroot/tourist-api/宝塔定时任务脚本.sh clean
```

## 日志查看
定时任务的执行日志保存在：
```bash
/www/wwwlogs/tourist-api/cron.log
```

可以通过以下命令查看日志：
```bash
# 查看最新日志
tail -f /www/wwwlogs/tourist-api/cron.log

# 查看今天的日志
grep "$(date '+%Y-%m-%d')" /www/wwwlogs/tourist-api/cron.log
```

## 脚本功能说明

### 1. test_schedule_task()
- **功能**: 测试定时任务是否正常工作
- **执行频率**: 每分钟
- **操作**: 向 `message_info` 表插入测试数据
- **备注**: 验证完成后建议禁用此任务

### 2. update_expired_orders()
- **功能**: 更新过期的拼团订单状态
- **执行频率**: 每天凌晨1点
- **操作**: 
  - 查找产品已过期的拼团订单
  - 将订单状态更新为"已失效"(status=2)
  - 发送拼团失败通知

### 3. update_unfulfilled_orders()
- **功能**: 更新未成团的过期订单状态
- **执行频率**: 每天凌晨1点30分
- **操作**:
  - 查找拼团截止但人数不足的产品
  - 将相关订单状态更新为"已失效"
  - 发送针对性拼团失败通知

## 数据库配置说明

脚本中的数据库配置：
```bash
DB_NAME="tour"           # 数据库名
DB_USER="tour"           # 数据库用户名
DB_PASSWORD="B8w4MzSibXzKnMb5"  # 数据库密码
DB_HOST="localhost"      # 数据库主机
DB_PORT="3306"          # 数据库端口
```

## 注意事项

1. **权限设置**: 确保脚本有执行权限
2. **数据库连接**: 脚本会自动检查数据库连接状态
3. **日志记录**: 所有操作都会记录到日志文件
4. **容错处理**: 脚本包含错误处理机制
5. **API备用**: 优先使用API接口，失败时降级到直接数据库操作

## 测试验证

### 验证脚本是否正常工作
```bash
# 测试数据库连接
/www/wwwroot/tourist-api/宝塔定时任务脚本.sh test

# 手动执行过期订单更新
/www/wwwroot/tourist-api/宝塔定时任务脚本.sh expired

# 手动执行未成团订单更新
/www/wwwroot/tourist-api/宝塔定时任务脚本.sh unfulfilled
```

### 验证测试数据
查看 `message_info` 表是否有测试数据插入：
```sql
SELECT * FROM message_info WHERE name = '定时任务测试' ORDER BY id DESC LIMIT 5;
```

### 验证通知发送
查看 `message_notification` 表是否有通知记录：
```sql
SELECT * FROM message_notification WHERE senderRemark LIKE '%系统自动%' ORDER BY id DESC LIMIT 5;
```

## 停用测试任务

验证完成后，建议停用测试定时任务：
1. 在宝塔面板中禁用"订单模块-定时任务测试"任务
2. 或者修改 `tourist-api/src/modules/order/config.ts` 中的配置：
```typescript
{
  name: '定时任务测试',
  service: 'orderOrderService',
  method: 'testScheduleTask',
  cron: '0 * * * * *',
  immediate: false,
  enable: false  // 设置为 false
}
``` 