<template>
  <div class="contact-page">
    <!-- 头部导航 -->
    <TheHeader />

    <section class="relative" style="padding-top: 85px;">
      <el-carousel v-if="bannerImages.length > 0" v-loading="bannerLoading" height="374px" class="banner-carousel"
        :autoplay="true" :interval="5000" :draggable="true">
        <el-carousel-item v-for="(image, index) in bannerImages" :key="index">
          <div class="relative h-full">
            <img :src="image.src" class="w-full h-full object-cover" />
          </div>
        </el-carousel-item>
      </el-carousel>
    </section>

    <div class="w-full" :style="{
      backgroundImage: `url(${bgImg})`,
      backgroundRepeat: 'no-repeat',
      backgroundPositionX: '-150px',
      backgroundSize: '110% 100%',
    }">

      <div class="container mx-auto px-4 py-12" v-loading="companyLoading">
        <div class="flex flex-col md:flex-row gap-8 mt-16">
          <!-- 左侧联系方式区域 -->
          <div class="contact-methods md:w-1/2" v-loading="siteInfoLoading">
            <!-- 投诉联系方式 -->
            <div class="contact-method-card">
              <div class="contact-icon">
                <img src="@/assets/contact/icon1.png" alt="投诉" class="w-[61px] h-[57px]" />
              </div>
              <div class="contact-details">
                <h4 class="method-title">投诉联系方式</h4>
                <p class="method-text">投诉联系人：{{ siteInfo?.complaintContact || '暂无联系人信息' }}</p>
                <p class="method-text">电话：{{ siteInfo?.complaintPhone || '暂无电话信息' }}</p>
                <p class="method-text">邮箱：{{ siteInfo?.complaintEmail || '暂无邮箱信息' }}</p>
              </div>
            </div>

            <!-- 合作联系方式 -->
            <div class="contact-method-card">
              <div class="contact-icon">
                <img src="@/assets/contact/icon2.png" alt="合作" class="w-[62px] h-[62px]" />
              </div>
              <div class="contact-details">
                <h4 class="method-title">合作联系方式</h4>
                <p class="method-text">合作联系人：{{ siteInfo?.cooperationContact || '暂无联系人信息' }}</p>
                <p class="method-text">电话：{{ siteInfo?.cooperationPhone || '暂无电话信息' }}</p>
                <p class="method-text">邮箱：{{ siteInfo?.cooperationEmail || '暂无邮箱信息' }}</p>
              </div>
            </div>

            <!-- 紧急联系方式 -->
            <div class="contact-method-card">
              <div class="contact-icon">
                <img src="@/assets/contact/icon3.png" alt="紧急" class="w-[70px] h-[58px]" />
              </div>
              <div class="contact-details">
                <h4 class="method-title">紧急联系方式</h4>
                <p class="method-text">紧急联系人：{{ siteInfo?.emergencyContact || '暂无联系人信息' }}</p>
                <p class="method-text">电话：{{ siteInfo?.emergencyPhone || '暂无电话信息' }}</p>
                <p class="method-text">邮箱：{{ siteInfo?.emergencyEmail || '暂无邮箱信息' }}</p>
              </div>
            </div>
          </div>

          <!-- 右侧在线留言表单 -->
          <div class="online-message md:w-1/2">
            <h3 class="text-4xl font-bold mb-6 text-left px-6 py-2">在线留言</h3>
            <div class="message-form p-6 rounded-lg">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
                <div>
                  <input type="text" :class="[
                    'w-full border-b border-gray-500 p-2 outline-none bg-transparent underline-input',
                    formData.name && !nameValid ? 'border-red-500' : '',
                    formData.name && nameValid ? 'border-green-500' : ''
                  ]" placeholder="请输入姓名（至少2个字符）" v-model="formData.name">
                  <div v-if="formData.name && !nameValid" class="text-red-500 text-sm mt-1">
                    姓名至少2个字符
                  </div>
                </div>
                <div>
                  <input type="tel" :class="[
                    'w-full border-b border-gray-500 p-2 outline-none bg-transparent underline-input',
                    formData.phone && !phoneValid ? 'border-red-500' : '',
                    formData.phone && phoneValid ? 'border-green-500' : ''
                  ]" placeholder="请输入电话（手机号码）" v-model="formData.phone">
                  <div v-if="formData.phone && !phoneValid" class="text-red-500 text-sm mt-1">
                    请输入正确的手机号码
                  </div>
                </div>
              </div>
              <div class="mb-10">
                <input type="email" :class="[
                  'w-full border-b border-gray-500 p-2 outline-none bg-transparent underline-input',
                  formData.email && !emailValid ? 'border-red-500' : '',
                  formData.email && emailValid ? 'border-green-500' : ''
                ]" placeholder="请输入邮箱" v-model="formData.email">
                <div v-if="formData.email && !emailValid" class="text-red-500 text-sm mt-1">
                  请输入正确的邮箱格式
                </div>
              </div>
              <div class="mb-16 relative">
                <textarea :class="[
                  'w-full border-b border-gray-500 p-2 outline-none bg-transparent underline-input h-24',
                  formData.message && !messageValid ? 'border-red-500' : '',
                  formData.message && messageValid ? 'border-green-500' : ''
                ]" placeholder="请输入留言内容（10-500字符）" v-model="formData.message" maxlength="500"></textarea>
                <div class="absolute bottom-1 right-0 text-sm text-gray-400">
                  {{ formData.message.length }}/500
                </div>
                <div v-if="formData.message && !messageValid" class="text-red-500 text-sm mt-1">
                  留言内容需要10-500个字符
                </div>
              </div>
              <div class="flex">
                <button class="py-2 bg-[#A9A9A9] text-white w-[358px] h-[68px] disabled:opacity-50" @click="resetForm"
                  :disabled="formLoading">重置</button>
                <button
                  class="py-2 bg-[#c9161c] text-white w-[358px] h-[68px] disabled:opacity-50 disabled:cursor-not-allowed"
                  @click="submitForm" :disabled="formLoading">
                  <span v-if="!formLoading">提交</span>
                  <span v-else>提交中...</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <div class="line w-[1452px] mx-auto"></div>

        <!-- 公司信息区域 -->
        <div v-if="allCompanies.length > 0" class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12 justify-items-center">
          <!-- 循环显示所有公司 -->
          <div v-for="company in allCompanies" :key="company.id" class="company-card">
            <div class="company-image">
              <img :src="company.image || '@/assets/images/default-company.png'"
                :alt="company.isHeadOffice ? '总公司' : '分公司'" class="w-full h-full object-cover" />
            </div>
            <div class="company-info">
              <h3 class="info-title">
                {{ company.name }}
                <span v-if="company.isHeadOffice" class="text-sm text-red-500 ml-2">(总公司)</span>
              </h3>
              <p class="info-text">地址：{{ company.address || '暂无地址信息' }}</p>
              <p class="info-text">电话：{{ company.phone || '暂无电话信息' }}</p>
              <p class="info-text">邮箱：{{ company.email || '暂无邮箱信息' }}</p>
              <p v-if="company.contactPerson" class="info-text">联系人：{{ company.contactPerson }}</p>
              <p v-if="company.description" class="info-text text-sm text-gray-600 mt-2">{{ company.description }}</p>
            </div>
          </div>
        </div>

        <!-- 如果没有公司信息，显示默认提示 -->
        <div v-else class="flex justify-center items-center py-20">
          <div class="company-info text-center">
            <h3 class="info-title">暂无公司信息</h3>
            <p class="info-text">公司信息正在完善中，请稍后查看</p>
          </div>
        </div>


      </div>
    </div>

    <!-- 底部组件 -->
    <TheFooter />
  </div>
</template>

<script setup>
import TheHeader from '../components/TheHeader.vue';
import TheFooter from '../components/TheFooter.vue';
import banner1Img from '@/assets/images/banner.png';
import bgImg from '@/assets/common/bg.png';
import { ref, onMounted, computed, reactive } from 'vue';
import { commonApi, companyApi } from '../api';
import siteApi from '../api/site';
import messageApi from '../api/message';
import { ElMessage } from 'element-plus';


const bannerImages = ref([]);
const bannerLoading = ref(false);
const siteInfo = ref(null);
const siteInfoLoading = ref(false);
const companyLoading = ref(false);
const formLoading = ref(false);
const allCompanies = ref([]);
const formData = ref({
  name: '',
  phone: '',
  email: '',
  message: ''
});

// 请求完成状态追踪
const requestStatus = reactive({
  banner: false,
  siteInfo: false,
  company: false
});

// 检查所有请求是否完成并执行翻译
const checkAllRequestsComplete = () => {
  const allComplete = Object.values(requestStatus).every(status => status === true);
  if (allComplete) {
    // 所有请求完成后执行翻译
    if (window.translate && window.translate.execute) {
      window.translate.execute();
    }
  }
};

// 实时验证计算属性
const nameValid = computed(() => {
  return formData.value.name.trim().length >= 2;
});

const phoneValid = computed(() => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(formData.value.phone.trim());
});

const emailValid = computed(() => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(formData.value.email.trim());
});

const messageValid = computed(() => {
  const length = formData.value.message.trim().length;
  return length >= 10 && length <= 500;
});

const formValid = computed(() => {
  return nameValid.value && phoneValid.value && emailValid.value && messageValid.value;
});

// 表单校验规则
const validateForm = () => {
  if (!formData.value.name || !formData.value.name.trim()) {
    ElMessage.warning('请输入姓名');
    return false;
  }

  if (formData.value.name.trim().length < 2) {
    ElMessage.warning('姓名至少2个字符');
    return false;
  }

  if (!formData.value.phone || !formData.value.phone.trim()) {
    ElMessage.warning('请输入电话');
    return false;
  }

  // 电话格式校验
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(formData.value.phone.trim())) {
    ElMessage.warning('请输入正确的手机号码');
    return false;
  }

  if (!formData.value.email || !formData.value.email.trim()) {
    ElMessage.warning('请输入邮箱');
    return false;
  }

  // 邮箱格式校验
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(formData.value.email.trim())) {
    ElMessage.warning('请输入正确的邮箱格式');
    return false;
  }

  if (!formData.value.message || !formData.value.message.trim()) {
    ElMessage.warning('请输入留言内容（10~500字符）');
    return false;
  }

  if (formData.value.message.trim().length < 10) {
    ElMessage.warning('留言内容至少10个字符');
    return false;
  }

  if (formData.value.message.trim().length > 500) {
    ElMessage.warning('留言内容不能超过500个字符');
    return false;
  }

  return true;
};

// 获取Banner数据
const fetchBannerData = async () => {
  bannerLoading.value = true;
  try {
    const res = await commonApi.getBannerList({ position: 4 });

    if (res && res.data) {
      bannerImages.value = res.data.pics.map(item => ({
        src: item,
        link: res.data.path
      }));
    }
  } catch (error) {
    console.error('获取Banner数据失败:', error);
    ElMessage.error('获取Banner数据失败，使用默认图片');
    // 使用默认数据
    bannerImages.value = [
      {
        src: banner1Img,
        link: '#'
      }
    ];
  } finally {
    bannerLoading.value = false;
    requestStatus.banner = true;
    checkAllRequestsComplete();
  }
};

// 获取站点信息（用于联系方式）
const fetchSiteInfo = async () => {
  siteInfoLoading.value = true;
  try {
    const res = await siteApi.getSiteInfo();
    if (res && res.data) {
      siteInfo.value = res.data;
    }
  } catch (error) {
    console.error('获取站点信息失败:', error);
    ElMessage.error('获取站点信息失败');
  } finally {
    siteInfoLoading.value = false;
    requestStatus.siteInfo = true;
    checkAllRequestsComplete();
  }
};

// 获取公司信息
const fetchCompanyInfo = async () => {
  companyLoading.value = true;
  try {
    const res = await companyApi.getCompanies();
    if (res && res.data && Array.isArray(res.data)) {
      // 对公司列表进行排序：总公司在前，然后按照sort字段和创建时间排序
      allCompanies.value = res.data.sort((a, b) => {
        // 总公司优先
        if (a.isHeadOffice && !b.isHeadOffice) return -1;
        if (!a.isHeadOffice && b.isHeadOffice) return 1;

        // 然后按sort字段排序
        if (a.sort !== b.sort) return a.sort - b.sort;

        // 最后按创建时间排序
        return new Date(b.createTime) - new Date(a.createTime);
      });
    }
  } catch (error) {
    console.error('获取公司信息失败:', error);
    ElMessage.warning('获取公司信息失败，将显示默认信息');
  } finally {
    companyLoading.value = false;
    requestStatus.company = true;
    checkAllRequestsComplete();
  }
};

// 重置表单
const resetForm = () => {
  formData.value = {
    name: '',
    phone: '',
    email: '',
    message: ''
  };
};

// 提交表单
const submitForm = async () => {
  // 表单校验
  if (!validateForm()) {
    return;
  }

  formLoading.value = true;

  try {
    const submitData = {
      name: formData.value.name.trim(),
      phone: formData.value.phone.trim(),
      email: formData.value.email.trim(),
      content: formData.value.message.trim()
    };

    const res = await messageApi.submitMessage(submitData);

    if (res && res.code === 1000) {
      ElMessage.success(res.message || '留言提交成功！我们会尽快与您联系。');
      resetForm();
    } else {
      ElMessage.error(res.message || '提交留言失败，请稍后再试');
    }
  } catch (error) {
    console.error('提交留言失败:', error);
    ElMessage.error('提交留言失败，请稍后再试');
  } finally {
    formLoading.value = false;
  }
};

onMounted(() => {
  fetchBannerData();
  fetchSiteInfo();
  fetchCompanyInfo();
});
</script>

<style scoped>
.contact-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.banner-image {
  height: 400px;
  width: 100%;
  margin-top: 60px;
  /* 头部导航栏的高度 */
  overflow: hidden;
}

.section-title {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 30px;
}

/* 公司信息卡片样式 */
.company-card {
  max-width: 678px;
  width: 100%;
  overflow: hidden;
  position: relative;
}

.company-image {
  height: 423px;
  overflow: hidden;
  position: relative;
}

.company-info {
  padding: 20px;
}

.info-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
}

.info-text {
  color: #666;
  line-height: 1.4;
  margin-bottom: 0px;
}

/* 联系方式卡片样式 */
.contact-method-card {
  display: flex;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-bottom: 1px solid #C9C9C9;
}

.contact-icon {
  flex-shrink: 0;
  margin-right: 20px;
  width: 60px;
  height: 60px;
  color: #c9161c;
}

.icon-svg {
  width: 100%;
  height: 100%;
}

.method-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
}

.method-text {
  color: #666;
  line-height: 1.4;
  /* margin-bottom: 6px; */
}

/* 在线留言表单样式 */
.online-message {
  padding: 20px;
  background: #FFF8F7;
}

.message-form {}

.message-form input,
.message-form textarea {
  border: none;
  border-bottom: 1px solid #A9A9A9;
  padding: 10px;
  width: 100%;
  transition: border-color 0.2s;
}

.message-form input:focus,
.message-form textarea:focus {
  border-color: #457E85;
  outline: none;
}

.message-form label {
  font-size: 14px;
  color: #333;
}

.underline-input {
  border-radius: 0;
  border: none;
  border-bottom: 1px solid #A9A9A9;
}

.message-form button {
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  border: none;
}

.message-form button:hover {
  opacity: 0.9;
}

@media (max-width: 768px) {
  .banner-image {
    height: 200px;
  }

  .form-buttons {
    gap: 20px;
  }

  .company-card {
    max-width: 100%;
  }

  .company-image {
    height: 250px;
  }
}

/* 确保图片不会超出容器 */
.company-image img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Grid布局优化 */
@media (min-width: 768px) {
  .company-card {
    justify-self: center;
  }
}

/* 超大屏幕适配 */
@media (min-width: 1400px) {
  .company-card {
    max-width: 678px;
  }
}

.line {
  width: 1452px;
  height: 1px;
  background-color: #e5e5e5;
  margin-top: 20px;
  margin-bottom: 20px;
}
</style>