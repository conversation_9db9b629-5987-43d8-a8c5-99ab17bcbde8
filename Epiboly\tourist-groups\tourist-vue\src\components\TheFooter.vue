<script setup>
// 定义底部导航菜单项，与头部一致
const navItems = [
  { title: '首页', path: '/' },
  { title: '公司介绍', path: '/about' },
  { title: '业务范围', path: '/business' },
  { title: '合作协议', path: '/cooperation' },
  { title: '联系我们', path: '/contact' }
];
</script>

<template>
  <footer class="bg-white py-4" style="height: 225px;">
    <div class="container mx-auto px-4 h-full flex flex-col">
      <div class="flex-grow flex items-center">
        <!-- 左侧Logo -->
        <div class="flex-shrink-0">
          <img src="@/assets/logo.png" alt="Logo" class="w-[136px] h-[79px]" />
        </div>

        <!-- 中间导航菜单 -->
        <div class="flex-grow">
          <nav class="flex justify-center items-center space-x-8">
            <a 
              v-for="(item, index) in navItems" 
              :key="index" 
              :href="item.path"
              class="text-gray-600 hover:text-red-600 transition-colors duration-200 text-sm"
            >
              {{ item.title }}
            </a>
          </nav>
        </div>
      </div>
      
    </div>
  </footer>
</template>

<style scoped>
/* 如果需要额外样式可以在这里添加 */
</style> 