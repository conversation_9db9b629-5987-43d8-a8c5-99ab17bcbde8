<script setup>
import { ref, onMounted } from 'vue';
import TheHeader from '../components/TheHeader.vue';
import TheFooter from '../components/TheFooter.vue';
import api from '../api';
import { ElMessage } from 'element-plus';
import banner1Img from '@/assets/images/banner.png';
import { useRouter } from 'vue-router';

const router = useRouter();

const { commonApi, reservationApi, productApi, categoryApi } = api;

// banner轮播图数据
const bannerImages = ref([]);
const bannerLoading = ref(false);

// 获取Banner数据
const fetchBannerData = async () => {
  bannerLoading.value = true;
  try {
    const res = await commonApi.getBannerList({ position: 0 });

    if (res && res.data) {
      bannerImages.value = res.data.pics.map(item => ({
        src: item,
        link: res.data.path
      }));
    }
  } catch (error) {
    console.error('获取Banner数据失败:', error);
    ElMessage.error('获取Banner数据失败，使用默认图片');
    // 使用默认数据
    bannerImages.value = [
      {
        src: banner1Img,
        alt: '旅行风光1'
      },
      {
        src: banner1Img,
        alt: '旅行风光2'
      }
    ];
  } finally {
    bannerLoading.value = false;
  }
};

// 热门产品类别
const productCategories = ref([]);

// 获取分类数据
const fetchCategories = async () => {
  try {
    const res = await categoryApi.getCategoryList({ isShowHome: 1 });
    if (res && res.data) {
      productCategories.value = [
        { id: 0, name: '全部', active: true },
        ...res.data.map((item, index) => ({
          id: item.id,
          name: item.name,
          active: false
        }))
      ];
    }
  } catch (error) {
    console.error('获取分类数据失败:', error);
    ElMessage.error('获取分类数据失败，使用默认数据');
    // 使用默认数据
    productCategories.value = [
      { id: 0, name: '全部', active: true },
      { id: 1, name: '惊喜旅行', active: false },
      { id: 2, name: '奢华游览', active: false },
      { id: 3, name: 'MICE', active: false },
      { id: 4, name: '经典旅游产品', active: false },
      { id: 5, name: '研学产品', active: false },
      { id: 6, name: '散拼团', active: false }
    ];
  } finally {
    fetchHotProducts();
  }
};

// 切换分类
const toggleCategory = (categoryId) => {
  productCategories.value.forEach(category => {
    category.active = category.id === categoryId;
  });
  fetchHotProducts(categoryId);
};

// 页面加载时获取Banner数据
onMounted(() => {
  fetchBannerData();
  fetchCategories();
  fetchReservationData();
  fetchAttractionData();
});

// 热门产品数据
const hotProducts = ref([]);
const hotProductsLoading = ref(false);

// 获取热门产品数据
const fetchHotProducts = async (categoryId = null) => {
  hotProductsLoading.value = true;
  try {
    const params = {
      isHot: 1,  // 获取热门产品
      pageSize: 15
    };

    // 如果有选择分类且不是"全部"，则添加categoryId参数
    if (categoryId && categoryId !== 0) {
      params.categoryId = categoryId;
    }

    const res = await productApi.getProductList(params);

    if (res && res.code === 1000) {
      hotProducts.value = res.data.list.map(item => ({
        id: item.id,
        title: item.name,
        subtitle: item.subtitle,
        description: item.introduce,
        price: parseFloat(item.price),
        originalPrice: parseFloat(item.originalPrice),
        rating: parseFloat(item.score),
        cover: item.cover,
        city: item.city,
        destination: item.destination,
        days: item.days,
        views: item.views,
        bookingCount: item.bookingCount,
        tag: item.tag
      }))
    }
  } catch (error) {
    console.error('获取热门产品数据失败:', error);
    ElMessage.error('获取热门产品数据失败，使用默认数据');
    setDefaultHotProducts();
  } finally {
    hotProductsLoading.value = false;
  }
};

// 设置默认热门产品数据
const setDefaultHotProducts = () => {
  hotProducts.value = Array(4).fill().map((_, index) => ({
    id: index + 1,
    title: '桐影峡—清江的山水诗篇',
    subtitle: '品质纯玩团·24H接送机+赠：特色美食',
    description: '尽享奢华，贴心旅游体验在旅游的世界里，享受奢华的贴心服务。我们为您提供一流的酒店住宿...',
    price: 1258,
    originalPrice: 1999,
    rating: 5,
    cover: 'https://img2.baidu.com/it/u=3974615956,1410053108&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1067',
    city: '恩施',
    destination: '桐影峡',
    days: 2,
    views: 40804,
    bookingCount: 1250,
    tag: '当季热推'
  }));
};

// 主要景点数据
const spotlightImages = ref([]);

// 本期推荐按钮组
const featuredSpotButtons = ref([]);

// 获取景点数据
const fetchAttractionData = async () => {
  try {
    const res = await commonApi.getAttractionList();

    if (res && res.data) {
      // 设置主要景点轮播图数据
      spotlightImages.value = res.data.attractions.map(item => ({
        id: item.id,
        src: item.pic,
        alt: item.name,
        description: item.introduce
      }));

      // 设置精选景点按钮数据
      featuredSpotButtons.value = res.data.featureds.map((item, index) => ({
        id: item.id,
        name: item.name,
        active: index === 0 // 第一个按钮默认激活
      })).slice(0, 4);
    }
  } catch (error) {
    console.error('获取景点数据失败:', error);
    ElMessage.error('获取景点数据失败，使用默认数据');
    // 使用默认数据
    setDefaultAttractionData();
  }
};

// 设置默认景点数据
const setDefaultAttractionData = () => {
  spotlightImages.value = [
    { id: 1, src: 'https://img2.baidu.com/it/u=3974615956,1410053108&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1067', alt: '桐影峡—清江的山水诗篇', description: '尽享奢华，贴心旅游体验在旅游的世界里，享受奢华的贴心服务。我们为您提供一流的酒店住宿。尽享奢华，贴心旅游体验在旅游的世界里，享受奢华的贴心服务。我们为您提供一流的酒店住宿。尽享奢华，贴心旅游体验在旅游的世界里，享受奢华的贴心服务。我们为您提供一流的酒店住宿...' },
    { id: 2, src: 'https://img2.baidu.com/it/u=3974615956,1410053108&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1067', alt: '香格里拉—雪山草甸', description: '尽享奢华，贴心旅游体验在旅游的世界里，享受奢华的贴心服务。我们为您提供一流的酒店住宿。尽享奢华，贴心旅游体验在旅游的世界里，享受奢华的贴心服务。我们为您提供一流的酒店住宿...' },
    { id: 3, src: 'https://img2.baidu.com/it/u=3974615956,1410053108&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1067', alt: '张家界—峰林奇观', description: '尽享奢华，贴心旅游体验在旅游的世界里，享受奢华的贴心服务。我们为您提供一流的酒店住宿。尽享奢华，贴心旅游体验在旅游的世界里，享受奢华的贴心服务。我们为您提供一流的酒店住宿...' },
    { id: 4, src: 'https://img2.baidu.com/it/u=3974615956,1410053108&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1067', alt: '黄山—云海奇松', description: '尽享奢华，贴心旅游体验在旅游的世界里，享受奢华的贴心服务。我们为您提供一流的酒店住宿。尽享奢华，贴心旅游体验在旅游的世界里，享受奢华的贴心服务。我们为您提供一流的酒店住宿...' },
    { id: 5, src: 'https://img2.baidu.com/it/u=3974615956,1410053108&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1067', alt: '桂林—山水甲天下', description: '尽享奢华，贴心旅游体验在旅游的世界里，享受奢华的贴心服务。我们为您提供一流的酒店住宿。尽享奢华，贴心旅游体验在旅游的世界里，享受奢华的贴心服务。我们为您提供一流的酒店住宿...' }
  ];

  featuredSpotButtons.value = [
    { id: 1, name: '景点名称', active: true },
    { id: 2, name: '景点名称', active: false },
    { id: 3, name: '景点名称', active: false },
    { id: 4, name: '景点名称', active: false },
  ];
};

// 切换精选景点按钮
const toggleFeaturedSpot = (buttonId) => {
  featuredSpotButtons.value.forEach(button => {
    button.active = button.id === buttonId;
    handleProductClick(buttonId);
  });
};

// 当前展示的景点索引
const currentSpotlight = ref(1);

// 控制轮播图导航
const prevSlide = () => {
  if (currentSpotlight.value > 0) {
    currentSpotlight.value--;
  } else {
    currentSpotlight.value = spotlightImages.value.length - 1;
  }
};

const nextSlide = () => {
  if (currentSpotlight.value < spotlightImages.value.length - 1) {
    currentSpotlight.value++;
  } else {
    currentSpotlight.value = 0;
  }
};

// 单项预定数据
const bookingItems = ref([]);

// 获取单项预定数据
const fetchReservationData = async () => {
  try {
    const res = await reservationApi.getReservationList();
    if (res && res.data) {
      // 如果API返回数据，使用API数据
      bookingItems.value = res.data.map(item => ({
        id: item.id,
        title: item.title || item.name,
        icon: item.icon || item.name,
        content: item.content,
        coverImage: item.coverImage
      }));
    } else {
      // 如果没有数据，使用默认数据
      setDefaultBookingItems();
    }
  } catch (error) {
    console.error('获取单项预定数据失败:', error);
    // 出错时使用默认数据
    setDefaultBookingItems();
  }
};

// 处理产品点击事件
const handleProductClick = (id) => {
  router.push(`/details?id=${id}`);
};

const handleBookingClick = (id) => {
  router.push('/contact');
};

// 设置默认单项预定数据
const setDefaultBookingItems = () => {
  bookingItems.value = [
    { id: 1, title: '酒店', icon: 'hotel', description: '尽享奢华，贴心旅游体验在旅游的世界里，享受奢华的贴心服务。我们为您提供一流的酒店住宿...' },
    { id: 2, title: '飞机', icon: 'airplane', description: '尽享奢华，贴心旅游体验在旅游的世界里，享受奢华的贴心服务。我们为您提供一流的酒店住宿...' },
    { id: 3, title: '火车', icon: 'train', description: '尽享奢华，贴心旅游体验在旅游的世界里，享受奢华的贴心服务。我们为您提供一流的酒店住宿...' },
    { id: 4, title: '导游', icon: 'guide', description: '尽享奢华，贴心旅游体验在旅游的世界里，享受奢华的贴心服务。我们为您提供一流的酒店住宿...' },
    { id: 5, title: '签证', icon: 'visa', description: '尽享奢华，贴心旅游体验在旅游的世界里，享受奢华的贴心服务。我们为您提供一流的酒店住宿...' },
    { id: 6, title: '景点', icon: 'landmark', description: '尽享奢华，贴心旅游体验在旅游的世界里，享受奢华的贴心服务。我们为您提供一流的酒店住宿...' },
    { id: 7, title: '翻译', icon: 'translation', description: '尽享奢华，贴心旅游体验在旅游的世界里，享受奢华的贴心服务。我们为您提供一流的酒店住宿...' },
  ];
};
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <TheHeader />

    <section class="relative min-h-[40vh]" style="padding-top: 85px;">
      <el-carousel v-if="bannerImages.length > 0" v-loading="bannerLoading" height="40vh"
        class="banner-carousel fixed-carousel" :autoplay="true" :interval="5000" :draggable="true">
        <el-carousel-item v-for="(image, index) in bannerImages" :key="index">
          <div class="relative h-full w-full">
            <img :src="image.src" class="banner-image" />
          </div>
        </el-carousel-item>
      </el-carousel>

      <!-- 默认占位 -->
      <div v-else class="w-full h-[40vh] bg-gray-200 flex items-center justify-center">
        <div class="text-gray-500">加载中...</div>
      </div>
    </section>

    <div class="product-sections-wrapper">
      <section class="py-24 hot-product-section mx-auto min-h-[700px]">
        <div class="flex flex-col lg:flex-row gap-32 justify-center items-start">
          <div class="relative w-[496px] h-[628px] flex-shrink-0">
            <img
              :src="hotProducts.length > 0 ? hotProducts[0].cover : 'https://img2.baidu.com/it/u=3974615956,1410053108&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1067'"
              :alt="hotProducts.length > 0 ? hotProducts[0].title : 'Travel Group'"
              class="w-full h-full hot-product-image object-cover" />

            <div class="absolute bg-white rounded-[20px] rating-card rating-score-card">
              <div class="flex flex-col items-center justify-center h-full">
                <div class="text-red-600 font-bold">{{ hotProducts.length > 0 ? hotProducts[0].rating : '4.8' }}</div>
                <div class="text-gray-600">产品评分</div>
              </div>
            </div>

            <div class="absolute top-1/3 -left-10 bg-white rounded-[20px] rating-card rating-views-card">
              <div class="flex flex-col items-center justify-center h-full">
                <div class="text-red-600 font-bold">{{ hotProducts.length > 0 ? hotProducts[0].views : '4880' }}+</div>
                <div class="text-gray-600">浏览人数</div>
              </div>
            </div>

            <div class="absolute bottom-6 -right-10 bg-white rounded-[20px] rating-card rating-booking-card">
              <div class="flex flex-col items-center justify-center h-full">
                <div class="text-red-600 font-bold">{{ hotProducts.length > 0 ? hotProducts[0].bookingCount : '1250' }}+
                </div>
                <div class="text-gray-600">预订人数</div>
              </div>
            </div>
          </div>

          <div class="w-[505px] flex flex-col py-10 flex-shrink-0">
            <h2 class="text-3xl font-bold mb-14 text-[40px]">最受<span class="text-red-600">欢迎</span>的旅行产品</h2>

            <div class="flex justify-between items-start mb-6">
              <div>
                <h3 class="text-base text-[20px] text-gray-700 mb-1">{{ hotProducts.length > 0 ? hotProducts[0].title :
                  '副标题题副标题副标题副标题副标题' }}</h3>
              </div>
              <span class="text-2xl font-bold text-[#333] mt-[1.3rem] text-[32px] relative left-[20px]">${{
                hotProducts.length > 0 ? hotProducts[0].price : 1258 }}</span>
            </div>

            <p class="text-gray-600 mb-5 min-h-[80px] leading-[2.5]">
              {{ hotProducts.length > 0 ? hotProducts[0].subtitle : '副标题副标题副标题副标题副标题副标题' }} <br>{{ hotProducts.length >
                0 ? hotProducts[0].description :
                '尽享奢华，贴心旅游体验在旅游的世界里，享受奢华的贴心服务。我们为您提供一流的酒店住宿、美食体验以及私人定制的行程安排，让您轻松享受旅行的快乐，尽享无忧的旅程。' }}
            </p>

            <div class="flex items-center justify-start mt-4">
              <button class="bg-red-600 text-white px-8 py-3 rounded-[10px] hover:bg-red-700 transition"
                @click="handleProductClick(hotProducts[0].id)">
                了解详情
              </button>
            </div>
          </div>
        </div>
      </section>

      <section class="pt-8 pb-16 min-h-[800px]">
        <div class=" mx-auto px-4">
          <h2 class="text-3xl font-bold text-center mb-10">热门产品</h2>
          <div class="flex flex-wrap justify-center gap-4 mb-12">
            <button v-for="category in productCategories" :key="category.id"
              class="px-8 py-2 text-sm transition-colors duration-200 rounded-full"
              :class="category.active ? 'bg-red-600 text-white' : 'bg-[#fad9ce] text-gray-700 hover:bg-[#fad9ce]'"
              @click="toggleCategory(category.id)">
              {{ category.name }}
            </button>
          </div>

          <div v-loading="hotProductsLoading"
            :class="hotProducts.length <= 4 ? 'flex justify-center min-h-[688px]' : 'overflow-x-auto custom-scrollbar min-h-[688px]'">
            <div :class="hotProducts.length <= 4 ? 'flex gap-6 pb-4' : 'flex gap-6 pb-4'"
              :style="hotProducts.length <= 4 ? '' : 'width: max-content;'">
              <div v-for="product in hotProducts" :key="product.id" style="border-radius: 20px;"
                class="w-[458px] bg-white overflow-hidden shadow-sm hover:shadow-md transition-shadow h-[688px] flex-shrink-0">
                <div class="relative h-full" @click="handleProductClick(product.id)">
                  <img :src="product.cover" :alt="product.title" class="w-full h-full object-cover cursor-pointer" />
                  <div class="absolute bottom-0 left-0 w-full bg-gradient-to-t from-black/60 to-transparent p-5">
                    <h3 class="font-bold text-lg text-white mb-1">{{ product.title }}</h3>
                    <p v-if="product.subtitle" class="text-white/90 text-sm mb-2">{{ product.subtitle }}</p>
                    <p class="text-white text-sm mt-1 line-clamp-2">
                      {{ product.description }}
                    </p>
                    <div class="flex justify-between items-center mt-4">
                      <div class="flex items-center">
                        <el-rate :model-value="product.rating" disabled allow-half
                          :colors="['#e29f58', '#e29f58', '#e29f58']" void-color="#e5e7eb" text-color="#ffffff"
                          score-template="{value}" class="rating-stars" />
                      </div>
                      <div class="text-right">
                        <span class="text-2xl font-bold text-white">${{ product.price }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <section class="py-16 container mx-auto px-4 min-h-[900px]">
      <h2 class="text-3xl font-bold text-center">主要景点</h2>

      <div class="w-48 h-1 bg-red-600 mx-auto mt-6 mb-10"></div>

      <div class="text-center mb-10 text-gray-600">
        <p>都市超感|私家团|经典团|特色游|都市超感|私家团</p>
      </div>

      <!-- 轮播图区域 -->
      <div v-if="spotlightImages.length > 0" class="relative w-full mx-auto ">
        <div class="flex items-start justify-center gap-4">
          <!-- 最左侧灰色图片 -->
          <div class="grayscale opacity-30 relative flex-shrink-0">
            <img :src="spotlightImages[(currentSpotlight + spotlightImages.length - 2) % spotlightImages.length].src"
              :alt="spotlightImages[(currentSpotlight + spotlightImages.length - 2) % spotlightImages.length].alt"
              class="w-[428px] h-[512px] object-cover" style="min-width: 428px;" />
          </div>

          <!-- 左侧灰色图片 -->
          <div class="grayscale opacity-40 relative flex-shrink-0">
            <img :src="spotlightImages[(currentSpotlight + spotlightImages.length - 1) % spotlightImages.length].src"
              :alt="spotlightImages[(currentSpotlight + spotlightImages.length - 1) % spotlightImages.length].alt"
              class="w-[428px] h-[512px] object-cover" style="min-width: 428px;" />
          </div>

          <!-- 中间彩色主图片 -->
          <div class="relative z-10 flex-shrink-0">
            <img :src="spotlightImages[currentSpotlight].src" :alt="spotlightImages[currentSpotlight].alt"
              class="w-[570px] h-[650px] object-cover" style="min-width: 570px;" />

            <!-- 左导航按钮 -->
            <button @click="prevSlide"
              class="absolute -left-40 bottom-0 transform -translate-y-0 -translate-x-0 flex items-center justify-center z-20 w-[145px] h-[145px] bg-[#c9161c] rounded-[8px]">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-white" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            <!-- 右导航按钮 -->
            <button @click="nextSlide"
              class="absolute -right-40 bottom-0 transform -translate-y-0 translate-x-0 flex items-center justify-center z-20 w-[145px] h-[145px] bg-[#c9161c] rounded-[8px]">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-white" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          <!-- 右侧灰色图片 -->
          <div class="grayscale opacity-40 relative flex-shrink-0">
            <img :src="spotlightImages[(currentSpotlight + 1) % spotlightImages.length].src"
              :alt="spotlightImages[(currentSpotlight + 1) % spotlightImages.length].alt"
              class="w-[428px] h-[512px] object-cover" style="min-width: 428px;" />
          </div>

          <!-- 最右侧灰色图片 -->
          <div class="grayscale opacity-30 relative flex-shrink-0">
            <img :src="spotlightImages[(currentSpotlight + 2) % spotlightImages.length].src"
              :alt="spotlightImages[(currentSpotlight + 2) % spotlightImages.length].alt"
              class="w-[428px] h-[512px] object-cover" style="min-width: 428px;" />
          </div>
        </div>
      </div>

      <!-- 景点名称和描述 -->
      <div class="mt-10 text-center">
        <h3 v-if="spotlightImages.length > 0" class="text-xl font-bold mb-4">{{ spotlightImages[currentSpotlight].alt }}
        </h3>
        <p v-if="spotlightImages.length > 0" class="text-gray-600 max-w-3xl mx-auto">
          {{ spotlightImages[currentSpotlight].description }}
        </p>
      </div>
    </section>

    <!-- 本期推荐 -->
    <section class="py-20 relative overflow-hidden min-h-[803px] h-[803px]">
      <img src="@/assets/images/beach_bg.png" alt="精选景点背景"
        class="absolute inset-0 w-full h-full object-cover object-center" />

      <div class="container mx-auto px-4 relative z-10 h-full flex flex-col justify-center">
        <!-- 标题和描述 -->
        <h2 class="text-4xl font-bold text-white mb-4">本期推荐</h2>

        <div class="w-20 h-0.5 bg-white mb-10"></div>

        <p class="text-white mb-12 max-w-xl leading-relaxed">
          本期强烈推荐的新产品或新路线，期待您来探索和体验。
        </p>

        <!-- 按钮组 -->
        <div class="grid grid-cols-2 gap-6 max-w-xl">
          <button v-for="button in featuredSpotButtons" :key="button.id"
            class="w-full py-4 px-3 text-center font-medium rounded-md transition-colors duration-200"
            :class="button.active ? 'bg-red-600 text-white hover:bg-red-700' : 'bg-white/30 backdrop-blur-sm text-white hover:bg-white/40'">
            {{ button.name }}
          </button>
        </div>
      </div>
    </section>

    <!-- 单项预定区 -->
    <section class="py-16 booking-section min-h-[800px]">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold text-center mb-6">单项预定</h2>
        <p class="text-center mb-12 max-w-4xl mx-auto">
          尽享奢华，贴心旅游体验在旅游的世界里，享受奢华的贴心服务。我们为您提供一流的酒店住宿、美食体验
        </p>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 lg:grid-rows-2 gap-6">
          <!-- 首个卡片，占据两行 -->
          <div v-if="bookingItems.length > 0"
            class="cursor-pointer relative overflow-hidden lg:row-span-2 h-[580px] lg:h-auto"
            @click="handleBookingClick(bookingItems[0].id)">
            <img :src="bookingItems[0].coverImage || '@/assets/images/beach_bg.png'" :alt="bookingItems[0].title"
              class="w-full h-full object-cover" />
            <div class="absolute inset-0 flex flex-col justify-end p-6">
              <h3 class="text-2xl font-bold mb-4 text-left text-white">{{ bookingItems[0].title }}</h3>
              <p class="text-sm text-white text-ellipsis-3">
                {{ bookingItems[0].content }}
              </p>
            </div>
          </div>

          <!-- 其余卡片 -->
          <div v-for="item in bookingItems.slice(1)" :key="item.id"
            class="cursor-pointer relative overflow-hidden h-[308px]" @click="handleBookingClick(item.id)">
            <img :src="item.coverImage || '@/assets/images/beach_bg.png'" :alt="item.title"
              class="w-full h-full object-cover" />
            <div class="absolute inset-0 flex flex-col justify-end p-6">
              <h3 class="text-2xl font-bold mb-4 text-left text-white">{{ item.title }}</h3>
              <p class="text-sm text-white text-ellipsis-3">
                {{ item.content }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 底部组件 -->
    <TheFooter />
  </div>
</template>

<style scoped>
.product-section {
  background-image: url('@/assets/images/product_bg1.png'), url('@/assets/images/product_bg2.png');
  background-position: center bottom, center top 150px;
  background-repeat: no-repeat, no-repeat;
  background-size: 100%, 100%;
}

.product-sections-wrapper {
  background: url('@/assets/images/home-bg.png') no-repeat #fefcfc;
  background-size: 100% 100%;
}

.booking-section {
  background-image: url('@/assets/about/bg2.png');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  background-color: #fff;
}

.hot-product-section {
  background-color: transparent;
}

.rating-card {
  width: 160px;
  height: 110px;
  box-shadow: 3px 2px 8px 2px rgba(114, 114, 114, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 8px;
  box-sizing: border-box;
  position: absolute;
  z-index: 10;
}

.rating-card .flex {
  width: 100%;
  max-width: 100%;
}

.rating-card .text-red-600 {
  font-size: 24px;
  line-height: 1.2;
  margin-bottom: 4px;
  word-break: break-word;
  text-align: center;
}

.rating-card .text-gray-600 {
  font-size: 14px;
  line-height: 1.3;
  text-align: center;
  word-break: break-word;
  hyphens: auto;
  overflow-wrap: break-word;
}


.rating-views-card {
  top: 45%;
  left: -16%;
  transform: translateY(-50%);
}

.rating-score-card {
  top: 8%;
  right: -16%;
}

.rating-booking-card {
  bottom: 8%;
  right: -16%;
}

.hot-product-image {
  border-radius: 40px;
}

.text-ellipsis-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.rating-stars {
  --el-rate-text-color: #ffffff;
}

.rating-stars .el-rate__text {
  color: #ffffff !important;
  margin-left: 8px;
  font-size: 14px;
}

.fixed-carousel {
  width: 100% !important;
  height: 40vh !important;
  min-height: 40vh !important;
  max-height: 40vh !important;
}

.fixed-carousel .el-carousel__container {
  height: 40vh !important;
}

.fixed-carousel .el-carousel__item {
  height: 40vh !important;
}

.banner-image {
  width: 100% !important;
  height: 40vh !important;
  min-height: 40vh !important;
  max-height: 40vh !important;
  object-fit: cover;
  object-position: center;
  display: block;
}

/* 自定义滚动条样式 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f3f4f6;
}

.custom-scrollbar::-webkit-scrollbar {
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(90deg, #cbd5e1, #cbd5e1);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(90deg, #94a3b8, #94a3b8);
}
</style>
