import { Provide } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { CompanyEntity } from '../entity/company';

/**
 * 公司信息服务
 */
@Provide()
export class CompanyService extends BaseService {
    @InjectEntityModel(CompanyEntity)
    companyEntity: Repository<CompanyEntity>;

    /**
 * 设置总公司
 * @param id 公司ID
 */
    async setHeadOffice(id: number) {
        // 首先取消所有总公司标识
        await this.companyEntity.update({}, { isHeadOffice: false });

        // 设置指定公司为总公司
        await this.companyEntity.update({ id }, { isHeadOffice: true });

        return true;
    }

    /**
     * 获取总公司信息
     */
    async getHeadOffice() {
        const headOffice = await this.companyEntity.findOne({
            where: { isHeadOffice: true, status: 1 }
        });

        return headOffice;
    }

    /**
     * 获取所有公司列表
     */
    async getAllCompanies() {
        const companies = await this.companyEntity.find({
            where: { status: 1 },
            order: { sort: 'ASC', createTime: 'DESC' }
        });

        return companies;
    }

    /**
     * 获取分公司列表（非总公司的其他公司）
     */
    async getBranches() {
        const branches = await this.companyEntity.find({
            where: { isHeadOffice: false, status: 1 },
            order: { sort: 'ASC', createTime: 'DESC' }
        });

        return branches;
    }

    /**
     * 获取公司联系信息（包含总公司和第一个分公司的基本信息）
     */
    async getContactInfo() {
        // 获取总公司
        const headOffice = await this.getHeadOffice();

        // 获取第一个分公司
        const firstBranch = await this.companyEntity.findOne({
            where: { isHeadOffice: false, status: 1 },
            order: { sort: 'ASC', createTime: 'DESC' }
        });

        return {
            headOffice: headOffice ? {
                id: headOffice.id,
                name: headOffice.name,
                image: headOffice.image,
                address: headOffice.address,
                phone: headOffice.phone,
                email: headOffice.email,
                contactPerson: headOffice.contactPerson,
                description: headOffice.description
            } : null,
            branch: firstBranch ? {
                id: firstBranch.id,
                name: firstBranch.name,
                image: firstBranch.image,
                address: firstBranch.address,
                phone: firstBranch.phone,
                email: firstBranch.email,
                contactPerson: firstBranch.contactPerson,
                description: firstBranch.description
            } : null
        };
    }
} 