import { Inject, Post, Body, Get, Query } from '@midwayjs/core';
import { CoolController, BaseController } from '@cool-midway/core';
import { CultureInfoEntity } from '../../entity/info';
import { CultureInfoService } from '../../service/info';

/**
 * 企业文化信息
 */
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: CultureInfoEntity,
  service: CultureInfoService,
  pageQueryOp: {
    keyWordLikeFields: ['name'],
  },
})
export class AdminCultureInfoController extends BaseController {
  @Inject()
  cultureInfoService: CultureInfoService;
}
