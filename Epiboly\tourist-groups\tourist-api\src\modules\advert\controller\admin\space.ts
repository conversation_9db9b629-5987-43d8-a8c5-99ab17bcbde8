import { Inject } from '@midwayjs/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BaseController } from '@cool-midway/core';
import { AdvertSpaceEntity } from '../../entity/space';
import { AdvertSpaceService } from '../../service/space';

/**
 * 广告位信息
 */
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: AdvertSpaceEntity,
  service: AdvertSpaceService,
  pageQueryOp: {
    keyWordLikeFields: ['a.name'],
  },
})
export class AdminAdvertSpaceController extends BaseController {
  @Inject()
  advertSpaceService: AdvertSpaceService;
}
