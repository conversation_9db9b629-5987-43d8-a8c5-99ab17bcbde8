import { Inject } from '@midwayjs/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BaseController } from '@cool-midway/core';
import { MemberInfoEntity } from '../../entity/info';
import { MemberInfoService } from '../../service/info';

/**
 * 成员信息
 */
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: MemberInfoEntity,
  service: MemberInfoService,
  pageQueryOp: {
    keyWordLikeFields: ['a.name', 'a.position'],
    fieldEq: ['a.type', 'a.status'],
    select: ['a.*'],
  },
})
export class AdminMemberInfoController extends BaseController {
  @Inject()
  memberInfoService: MemberInfoService;
}
