import { Provide, Inject } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { OrderOrderEntity } from '../entity/order';
import { OrderGroupEntity } from '../entity/group';
import { ProductProductEntity } from '../../product/entity/product';
import { UserInfoService } from '../../user/service/info';
import { OrderGroupService } from './group';
import { MessageNotificationService } from '../../message/service/notification';
import { MessageInfoEntity } from '../../message/entity/info';
import * as moment from 'moment';

/**
 * 订单
 */
@Provide()
export class OrderOrderService extends BaseService {
  @InjectEntityModel(OrderOrderEntity)
  orderOrderEntity: Repository<OrderOrderEntity>;

  @InjectEntityModel(OrderGroupEntity)
  orderGroupEntity: Repository<OrderGroupEntity>;

  @InjectEntityModel(ProductProductEntity)
  productProductEntity: Repository<ProductProductEntity>;

  @InjectEntityModel(MessageInfoEntity)
  messageInfoEntity: Repository<MessageInfoEntity>;

  @Inject()
  userInfoService: UserInfoService;

  @Inject()
  orderGroupService: OrderGroupService;

  @Inject()
  messageNotificationService: MessageNotificationService;

  /**
   * 获取实体对象
   */
  getOrmRepository() {
    return this.orderOrderEntity;
  }

  /**
   * 生成订单号
   * @returns
   */
  async generateOrderNumber() {
    const timestamp = moment().format('YYYYMMDDHHmmss');
    const random = Math.random().toString().slice(-4);
    return timestamp + random;
  }

  /**
   * 新增订单
   * @param param
   * @returns
   */
  async add(param: any) {
    param.orderNumber = await this.generateOrderNumber();
    return await this.orderOrderEntity.save(param);
  }

  /**
   * 创建预定订单
   * @param orderData 订单数据
   * @returns 
   */
  async createBookingOrder(orderData: any) {
    const { productId, quantity, contactName, contactPhone, bookingDate, remarks, userId } = orderData;

    // 获取产品信息
    const product = await this.productProductEntity.findOne({
      where: { id: productId }
    });

    if (!product) {
      throw new Error('产品不存在');
    }

    // 验证预订日期是否在产品的有效期内
    const bookingDateObj = new Date(bookingDate);
    const startDateObj = new Date(product.startDate);
    const endDateObj = new Date(product.endDate);

    if (bookingDateObj < startDateObj || bookingDateObj > endDateObj) {
      throw new Error('预订日期超出产品有效期范围');
    }

    // 计算总金额
    const totalAmount = Number(product.price) * quantity;

    // 创建订单数据
    const newOrderData = {
      productId,
      orderType: 0, // 预定订单
      orderStatus: 0, // 已预定
      quantity,
      contactName,
      contactPhone,
      bookingDate: bookingDateObj,
      totalAmount,
      remarks: remarks || '',
      userConfirmed: false,
      userId: userId || null
    };

    // 创建订单
    const result = await this.add(newOrderData);

    return {
      orderNumber: result.orderNumber,
      orderId: result.id,
      totalAmount,
      quantity,
      contactName,
      contactPhone,
      bookingDate,
      product: {
        name: product.name,
        price: product.price
      }
    };
  }

  /**
   * 创建拼团订单
   * @param orderData 订单数据
   * @returns 
   */
  async createGroupOrder(orderData: any) {
    const { productId, quantity, contactName, contactPhone, bookingDate, remarks, userId } = orderData;

    // 获取产品信息
    const product = await this.productProductEntity.findOne({
      where: { id: productId }
    });

    if (!product) {
      throw new Error('产品不存在');
    }

    // 验证预订日期是否在产品的有效期内
    const bookingDateObj = new Date(bookingDate);
    const startDateObj = new Date(product.startDate);
    const endDateObj = new Date(product.endDate);

    if (bookingDateObj < startDateObj || bookingDateObj > endDateObj) {
      throw new Error('预订日期超出产品有效期范围');
    }

    // 使用新的多团逻辑
    const joinResult = await this.orderGroupService.joinGroup(productId, userId, {
      quantity,
      contactName,
      contactPhone,
      bookingDate: bookingDateObj,
      remarks
    });

    // 计算总金额
    const totalAmount = Number(product.price) * quantity;

    // 重新获取拼团信息以检查是否成团
    const updatedGroupInfo = await this.getGroupInfo(productId, userId);

    // 检查拼团是否已成功（满员）
    if (updatedGroupInfo.isGroupComplete) {
      try {
        await this.sendGroupSuccessNotification(updatedGroupInfo.product, updatedGroupInfo);
      } catch (error) {
        console.error('发送拼团成功通知失败:', error);
      }
    }

    return {
      success: true,
      message: '成功加入拼团',
      orderNumber: joinResult.orderNumber,
      orderId: joinResult.orderId,
      totalAmount,
      quantity,
      contactName,
      contactPhone,
      bookingDate,
      groupInfo: updatedGroupInfo,
      product: {
        name: product.name,
        price: product.price
      }
    };
  }

  /**
   * 分页查询订单（包含产品信息）
   * @param query 查询参数
   * @returns 
   */
  async page(query: any) {
    return this.sqlRenderPage(
      `
      SELECT 
        o.*,
        p.productCode,
        p.name as productName,
        p.subtitle as productSubtitle,
        p.price as productPrice,
        p.originalPrice as productOriginalPrice,
        p.cover as productCover,
        p.city as productCity,
        p.destination as productDestination,
        p.days as productDays,
        p.productType as productType
      FROM order_order o
      LEFT JOIN product_product p ON o.productId = p.id
      WHERE 1=1
      ${query.orderNumber ? `AND o.orderNumber LIKE '%${query.orderNumber}%'` : ''}
      ${query.productId ? `AND o.productId = ${query.productId}` : ''}
      ${query.orderType !== undefined ? `AND o.orderType = ${query.orderType}` : ''}
      ${query.orderStatus !== undefined ? `AND o.orderStatus = ${query.orderStatus}` : ''}
      ${query.userConfirmed !== undefined ? `AND o.userConfirmed = ${query.userConfirmed}` : ''}
      ${query.contactName ? `AND o.contactName LIKE '%${query.contactName}%'` : ''}
      ${query.contactPhone ? `AND o.contactPhone LIKE '%${query.contactPhone}%'` : ''}
      `,
      query
    );
  }

  /**
   * 列表查询订单（包含产品信息）
   * @param query 查询参数
   * @returns 
   */
  async list(query: any = {}) {
    const queryBuilder = this.orderOrderEntity
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.product', 'product');

    if (query.orderNumber) {
      queryBuilder.andWhere('order.orderNumber LIKE :orderNumber', {
        orderNumber: `%${query.orderNumber}%`
      });
    }

    if (query.productId) {
      queryBuilder.andWhere('order.productId = :productId', {
        productId: query.productId
      });
    }

    if (query.orderType !== undefined) {
      queryBuilder.andWhere('order.orderType = :orderType', {
        orderType: query.orderType
      });
    }

    if (query.orderStatus !== undefined) {
      queryBuilder.andWhere('order.orderStatus = :orderStatus', {
        orderStatus: query.orderStatus
      });
    }

    queryBuilder.orderBy('order.createTime', 'DESC');

    if (query.limit) {
      queryBuilder.limit(query.limit);
    }

    return await queryBuilder.getMany();
  }

  /**
   * 获取订单详情（包含产品信息）
   * @param id 订单ID
   * @returns 
   */
  async info(id: number) {
    return await this.orderOrderEntity
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.product', 'product')
      .where('order.id = :id', { id })
      .getOne();
  }

  /**
   * 根据产品ID查询订单列表
   * @param productId 产品ID
   * @returns 
   */
  async getOrdersByProductId(productId: number) {
    return await this.orderOrderEntity
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.product', 'product')
      .where('order.productId = :productId', { productId })
      .orderBy('order.createTime', 'DESC')
      .getMany();
  }

  /**
   * 根据订单号查询订单详情（包含产品信息）
   * @param orderNumber 订单号
   * @returns 
   */
  async getOrderByNumber(orderNumber: string) {
    return await this.orderOrderEntity
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.product', 'product')
      .where('order.orderNumber = :orderNumber', { orderNumber })
      .getOne();
  }

  /**
   * 统计产品的订单数量
   * @param productId 产品ID
   * @returns 
   */
  async countOrdersByProduct(productId: number) {
    return await this.orderOrderEntity
      .createQueryBuilder('order')
      .where('order.productId = :productId', { productId })
      .getCount();
  }

  /**
   * 获取订单统计信息
   * @param query 查询条件
   * @returns 
   */
  async getOrderStatistics(query: any = {}) {
    const queryBuilder = this.orderOrderEntity
      .createQueryBuilder('order')
      .leftJoin('order.product', 'product');

    if (query.productId) {
      queryBuilder.where('order.productId = :productId', {
        productId: query.productId
      });
    }

    if (query.startDate && query.endDate) {
      queryBuilder.andWhere('order.createTime BETWEEN :startDate AND :endDate', {
        startDate: query.startDate,
        endDate: query.endDate
      });
    }

    const result = await queryBuilder
      .select([
        'COUNT(*) as totalOrders',
        'SUM(CASE WHEN order.orderStatus = 0 THEN 1 ELSE 0 END) as pendingOrders',
        'SUM(CASE WHEN order.orderStatus = 1 THEN 1 ELSE 0 END) as confirmedOrders',
        'SUM(CASE WHEN order.orderStatus = 2 THEN 1 ELSE 0 END) as expiredOrders',
        'SUM(order.totalAmount) as totalAmount',
        'AVG(order.totalAmount) as avgAmount'
      ])
      .getRawOne();

    return result;
  }

  /**
   * 获取产品拼团信息 - 支持多团
   * @param productId 产品ID
   * @param userId 用户ID (可选，如果提供则返回用户参与的团信息)
   * @returns 
   */
  async getGroupInfo(productId: number, userId?: number) {
    // 获取产品信息
    const product = await this.productProductEntity.findOne({
      where: { id: productId }
    });

    if (!product) {
      throw new Error('产品不存在');
    }

    // 如果提供了用户ID，返回用户参与的团信息
    if (userId) {
      const userGroupInfo = await this.orderGroupService.getUserGroupInfo(productId, userId);
      if (userGroupInfo) {
        return {
          product,
          currentMembers: userGroupInfo.members,
          totalRequired: userGroupInfo.group.maxSize,
          currentCount: userGroupInfo.group.currentSize,
          remainingSlots: userGroupInfo.group.remainingSize,
          isGroupComplete: userGroupInfo.group.isCompleted,
          groupId: userGroupInfo.group.id,
          groupNumber: userGroupInfo.group.groupNumber,
          userHasJoined: true
        };
      }
    }

    // 获取可用的团列表
    const availableGroupsInfo = await this.orderGroupService.getAvailableGroups(productId);

    // 找到第一个可用的团（未满员的团）
    const availableGroup = availableGroupsInfo.groups.find(group => group.isAvailable);

    if (availableGroup) {
      return {
        product,
        currentMembers: availableGroup.members,
        totalRequired: availableGroup.maxSize,
        currentCount: availableGroup.currentSize,
        remainingSlots: availableGroup.remainingSize,
        isGroupComplete: availableGroup.isCompleted,
        groupId: availableGroup.id,
        groupNumber: availableGroup.groupNumber,
        userHasJoined: false
      };
    }

    // 如果没有可用的团，返回新团的默认信息
    return {
      product,
      currentMembers: [],
      totalRequired: product.groupSize || 5,
      currentCount: 0,
      remainingSlots: product.groupSize || 5,
      isGroupComplete: false,
      groupId: null,
      groupNumber: null,
      userHasJoined: false
    };
  }

  /**
   * 加入拼团
   * @param productId 产品ID
   * @param userId 用户ID (可选，如果提供则获取真实用户信息)
   * @param userIdentifier 用户标识符 (可以是IP、设备ID等，可选)
   * @returns 
   */
  async joinGroup(productId: number, userId?: number, userIdentifier?: string) {
    // 检查拼团是否还有空位
    const groupInfo = await this.getGroupInfo(productId);

    if (groupInfo.isGroupComplete) {
      throw new Error('拼团人数已满，无法加入');
    }

    let contactName = '游客';
    let contactPhone = '13800000000';
    let avatar = 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png';

    // 如果提供了用户ID，获取真实的用户信息
    if (userId) {
      try {
        const userInfo = await this.userInfoService.person(userId);
        if (userInfo) {
          contactName = userInfo.nickName || '用户';
          contactPhone = userInfo.phone || '13800000000';
          avatar = userInfo.avatarUrl || avatar;
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
        // 如果获取用户信息失败，使用默认值继续
      }
    }

    // 生成最终的用户标识符
    const finalUserIdentifier = userIdentifier || `user_${userId || Date.now()}_${Math.random().toString(36).slice(-6)}`;

    // 检查用户是否已经加入过这个拼团
    let existingOrder;
    if (userId) {
      // 如果有用户ID，优先通过用户ID和联系信息检查
      existingOrder = await this.orderOrderEntity
        .createQueryBuilder('order')
        .where('order.productId = :productId', { productId })
        .andWhere('order.orderType = 1') // 散拼团订单
        .andWhere('order.orderStatus = 1') // 已入团状态
        .andWhere(
          '(order.remarks LIKE :userIdPattern OR order.contactName = :contactName OR order.contactPhone = :contactPhone)',
          {
            userIdPattern: `%user_${userId}_%`,
            contactName,
            contactPhone
          }
        )
        .getOne();
    } else {
      // 如果没有用户ID，通过标识符检查
      existingOrder = await this.orderOrderEntity
        .createQueryBuilder('order')
        .where('order.productId = :productId', { productId })
        .andWhere('order.orderType = 1') // 散拼团订单
        .andWhere('order.orderStatus = 1') // 已入团状态
        .andWhere('order.remarks LIKE :userIdentifier', {
          userIdentifier: `%${finalUserIdentifier}%`
        })
        .getOne();
    }

    if (existingOrder) {
      throw new Error('您已经加入过这个拼团，不能重复加入！');
    }

    // 创建拼团订单
    const orderData = {
      productId,
      orderType: 1, // 散拼团
      orderStatus: 1, // 已入团
      quantity: 1, // 默认1人
      contactName, // 使用真实联系人姓名
      contactPhone, // 使用真实联系电话
      totalAmount: Number(groupInfo.product.price) || 0,
      bookingDate: new Date(),
      userConfirmed: true,
      remarks: userId
        ? `拼团订单 - userId:${userId} - ${finalUserIdentifier}`
        : `拼团订单 - ${finalUserIdentifier}` // 保存用户ID到备注中
    };

    const result = await this.add(orderData);

    // 重新获取拼团信息
    const updatedGroupInfo = await this.getGroupInfo(productId);

    // 检查拼团是否已成功（满员）
    if (updatedGroupInfo.isGroupComplete) {
      try {
        await this.sendGroupSuccessNotification(updatedGroupInfo.product, updatedGroupInfo);
      } catch (error) {
        console.error('发送拼团成功通知失败:', error);
      }
    }

    return {
      success: true,
      message: '成功加入拼团',
      orderId: result.id,
      groupInfo: updatedGroupInfo,
      userIdentifier: finalUserIdentifier
    };
  }

  /**
   * 获取我的订单列表
   * @param contactPhone 联系人电话
   * @param page 页码
   * @param pageSize 每页数量
   * @returns 
   */
  async getMyOrders(contactPhone: string, page: number = 1, pageSize: number = 10, orderType: number = 1) {
    const queryBuilder = this.orderOrderEntity
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.product', 'product')
      .where('order.contactPhone = :contactPhone', { contactPhone })
      .andWhere('order.orderType = :orderType', { orderType })
      .orderBy('order.createTime', 'DESC');

    // 计算总数
    const total = await queryBuilder.getCount();

    // 分页查询
    const offset = (page - 1) * pageSize;
    queryBuilder.skip(offset).take(pageSize);

    const orders = await queryBuilder.getMany();

    // 格式化订单数据
    const formattedOrders = orders.map(order => ({
      id: order.id,
      orderNumber: order.orderNumber,
      productTitle: order.product?.name || '产品已下架',
      productImage: order.product?.cover || 'https://via.placeholder.com/80x60/CCCCCC/FFFFFF?text=暂无图片',
      description: order.product?.subtitle || '',
      contactPerson: order.contactName,
      contactPhone: order.contactPhone,
      groupType: order.orderType === 0 ? '预定订单' : '散拼团',
      peopleCount: order.quantity,
      status: this.getOrderStatusKey(order.orderStatus),
      bookingDate: order.bookingDate,
      totalAmount: order.totalAmount,
      remarks: order.remarks,
      createTime: order.createTime,
      userConfirmed: order.userConfirmed
    }));

    return {
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
      list: formattedOrders
    };
  }

  /**
   * 根据用户ID获取我的订单列表
   * @param userId 用户ID
   * @param page 页码
   * @param pageSize 每页数量
   * @param orderType 订单类型 0:预定订单 1:散拼团
   * @returns 
   */
  async getMyOrdersByUserId(userId: number, page: number = 1, pageSize: number = 10, orderType?: number) {
    // 构建查询条件：直接通过用户ID查询
    const queryBuilder = this.orderOrderEntity
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.product', 'product')
      .where('order.userId = :userId', { userId });

    // 如果指定了订单类型，添加过滤条件
    if (orderType !== undefined) {
      console.log('orderType', orderType);
      queryBuilder.andWhere('order.orderType = :orderType', { orderType });
    }

    queryBuilder.orderBy('order.createTime', 'DESC');

    // 计算总数
    const total = await queryBuilder.getCount();

    // 分页查询
    const offset = (page - 1) * pageSize;
    queryBuilder.skip(offset).take(pageSize);

    const orders = await queryBuilder.getMany();

    // 格式化订单数据
    const formattedOrders = await Promise.all(orders.map(async order => {
      let displayStatus = this.getOrderStatusKey(order.orderStatus);

      // 对于拼团订单，检查拼团状态
      if (order.orderType === 1 && order.orderStatus === 1) {
        try {
          const groupInfo = await this.getGroupInfo(order.productId);
          if (groupInfo.isGroupComplete) {
            displayStatus = 'completed'; // 已成团
          } else {
            displayStatus = 'confirmed'; // 已入团
          }
        } catch (error) {
          console.error('获取拼团信息失败:', error);
          displayStatus = 'confirmed'; // 默认已入团
        }
      }

      return {
        id: order.id,
        orderNumber: order.orderNumber,
        productTitle: order.product?.name || '产品已下架',
        productImage: order.product?.cover || 'https://via.placeholder.com/80x60/CCCCCC/FFFFFF?text=暂无图片',
        description: order.product?.subtitle || '',
        contactPerson: order.contactName,
        contactPhone: order.contactPhone,
        groupType: order.orderType === 0 ? '预定订单' : '散拼团',
        peopleCount: order.quantity,
        status: displayStatus,
        bookingDate: order.bookingDate,
        totalAmount: order.totalAmount,
        remarks: order.remarks,
        createTime: order.createTime,
        userConfirmed: order.userConfirmed
      };
    }));

    return {
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
      list: formattedOrders
    };
  }

  /**
   * 获取我的订单统计数据
   * @param userId 用户ID
   * @returns 
   */
  async getMyOrdersStats(userId: number) {
    // 构建查询条件：直接通过用户ID查询
    const orders = await this.orderOrderEntity
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.product', 'product')
      .where('order.userId = :userId', { userId })
      .getMany();

    // 统计各种状态的订单数量
    let allCount = orders.length;
    let completedCount = 0; // 已成团数量
    let expiredCount = 0;   // 已失效数量

    // 处理订单状态统计，需要检查拼团状态
    for (const order of orders) {
      if (order.orderStatus === 2) {
        expiredCount++; // 已失效
      } else if (order.orderType === 1 && order.orderStatus === 1) {
        // 对于拼团订单，检查是否已成团
        try {
          const groupInfo = await this.getGroupInfo(order.productId);
          if (groupInfo.isGroupComplete) {
            completedCount++; // 已成团
          }
        } catch (error) {
          console.error('获取拼团信息失败:', error);
          // 如果获取失败，不计入已成团
        }
      }
    }

    return {
      all: allCount,
      completed: completedCount,
      expired: expiredCount
    };
  }

  /**
   * 确认订单
   * @param orderNumber 订单号
   * @param contactPhone 联系人电话
   * @returns 
   */
  async confirmOrder(orderNumber: string, contactPhone: string) {
    // 查找订单
    const order = await this.orderOrderEntity.findOne({
      where: {
        orderNumber,
        contactPhone
      },
      relations: ['product']
    });

    if (!order) {
      throw new Error('订单不存在或联系电话不匹配');
    }

    if (order.userConfirmed) {
      throw new Error('订单已确认，无需重复确认');
    }

    if (order.orderStatus === 2) {
      throw new Error('订单已失效，无法确认');
    }

    // 更新订单状态
    await this.orderOrderEntity.update(order.id, {
      userConfirmed: true,
      updateTime: new Date()
    });

    return {
      success: true,
      message: '订单确认成功',
      orderNumber: order.orderNumber,
      orderStatus: order.orderStatus,
      userConfirmed: true
    };
  }

  /**
   * 获取订单状态键值
   * @param status 状态数字
   * @returns 
   */
  private getOrderStatusKey(status: number): string {
    const statusMap = {
      0: 'pending',    // 已预定
      1: 'confirmed',  // 已入团
      2: 'expired'     // 已失效
    };
    return statusMap[status] || 'pending';
  }

  /**
   * 检查并更新过期的拼团订单状态
   * 定时任务调用方法
   */
  async updateExpiredGroupOrders() {
    try {
      console.log('开始检查过期拼团订单...');

      // 查询所有已入团但产品已过期的订单
      const expiredOrders = await this.orderOrderEntity
        .createQueryBuilder('order')
        .leftJoinAndSelect('order.product', 'product')
        .where('order.orderType = 1') // 散拼团订单
        .andWhere('order.orderStatus = 1') // 已入团状态
        .andWhere('product.endDate < :currentDate', {
          currentDate: new Date().toISOString().split('T')[0]
        })
        .getMany();

      if (expiredOrders.length === 0) {
        console.log('没有发现过期的拼团订单');
        return { updated: 0, message: '没有过期订单需要更新' };
      }

      // 批量更新订单状态为已失效
      const orderIds = expiredOrders.map(order => order.id);
      await this.orderOrderEntity
        .createQueryBuilder()
        .update(OrderOrderEntity)
        .set({ orderStatus: 2 }) // 设置为已失效
        .where('id IN (:...ids)', { ids: orderIds })
        .execute();

      console.log(`成功更新 ${expiredOrders.length} 个过期拼团订单为已失效状态`);

      // 发送拼团失败公告通知
      if (expiredOrders.length > 0) {
        try {
          await this.sendGroupFailureNotification(expiredOrders);
        } catch (error) {
          console.error('发送拼团失败通知失败:', error);
        }
      }

      return {
        updated: expiredOrders.length,
        message: `成功更新 ${expiredOrders.length} 个过期订单`,
        expiredOrders: expiredOrders.map(order => ({
          orderNumber: order.orderNumber,
          productName: order.product?.name,
          contactName: order.contactName,
          endDate: order.product?.endDate
        }))
      };

    } catch (error) {
      console.error('更新过期拼团订单失败:', error);
      throw error;
    }
  }

  /**
   * 检查并更新未成团的过期订单状态
   * 针对拼团截止但人数不足的情况
   */
  async updateUnfulfilledGroupOrders() {
    try {
      console.log('开始检查未成团的过期拼团订单...');

      // 获取所有过期的产品ID
      const expiredProducts = await this.productProductEntity
        .createQueryBuilder('product')
        .where('product.endDate < :currentDate', {
          currentDate: new Date().toISOString().split('T')[0]
        })
        .andWhere('product.productType = 1') // 散拼团产品
        .getMany();

      if (expiredProducts.length === 0) {
        console.log('没有发现过期的拼团产品');
        return { updated: 0, message: '没有过期的拼团产品' };
      }

      let totalUpdated = 0;
      const updateResults = [];
      const failedGroups = []; // 收集未成团的拼团

      for (const product of expiredProducts) {
        // 检查该产品的拼团状态
        const groupInfo = await this.getGroupInfo(product.id);

        // 如果拼团未完成，将所有已入团的订单状态改为已失效
        if (!groupInfo.isGroupComplete) {
          const unfulfilledOrders = await this.orderOrderEntity
            .createQueryBuilder('order')
            .where('order.productId = :productId', { productId: product.id })
            .andWhere('order.orderType = 1') // 散拼团订单
            .andWhere('order.orderStatus = 1') // 已入团状态
            .getMany();

          if (unfulfilledOrders.length > 0) {
            const orderIds = unfulfilledOrders.map(order => order.id);
            await this.orderOrderEntity
              .createQueryBuilder()
              .update(OrderOrderEntity)
              .set({ orderStatus: 2 }) // 设置为已失效
              .where('id IN (:...ids)', { ids: orderIds })
              .execute();

            totalUpdated += unfulfilledOrders.length;
            updateResults.push({
              productName: product.name,
              updatedOrdersCount: unfulfilledOrders.length,
              requiredPeople: product.groupSize,
              actualPeople: unfulfilledOrders.length
            });

            // 收集未成团信息用于发送通知
            failedGroups.push({
              product,
              orders: unfulfilledOrders,
              requiredPeople: product.groupSize,
              actualPeople: unfulfilledOrders.length
            });
          }
        } else {
          // 拼团成功，发送成功通知
          try {
            await this.sendGroupSuccessNotification(product, groupInfo);
          } catch (error) {
            console.error('发送拼团成功通知失败:', error);
          }
        }
      }

      // 发送拼团失败通知
      if (failedGroups.length > 0) {
        try {
          await this.sendGroupFailureNotificationForUnfulfilled(failedGroups);
        } catch (error) {
          console.error('发送拼团失败通知失败:', error);
        }
      }

      console.log(`成功更新 ${totalUpdated} 个未成团的过期拼团订单为已失效状态`);

      return {
        updated: totalUpdated,
        message: `成功更新 ${totalUpdated} 个未成团的过期订单`,
        updateResults
      };

    } catch (error) {
      console.error('更新未成团过期拼团订单失败:', error);
      throw error;
    }
  }

  /**
   * 发送拼团成功通知
   */
  private async sendGroupSuccessNotification(product: any, groupInfo: any) {
    try {
      const title = '🎉 拼团成功通知';
      const content = `恭喜！${product.name} 拼团已成功，人数已满(${groupInfo.totalRequired}人)，行程即将开始。请注意查看行程安排和集合信息。`;

      await this.messageNotificationService.publishAnnouncement({
        title,
        content,
        senderId: 1, // 系统管理员ID
        priority: 1, // 重要
        publishTime: new Date(),
        senderRemark: `拼团成功-产品ID:${product.id}`
      });

      console.log(`已发送拼团成功通知: ${product.name}`);
    } catch (error) {
      console.error('发送拼团成功通知失败:', error);
      throw error;
    }
  }

  /**
   * 发送拼团失败通知（过期订单）
   */
  private async sendGroupFailureNotification(expiredOrders: any[]) {
    try {
      // 按产品分组
      const productGroups = expiredOrders.reduce((groups, order) => {
        const productId = order.productId;
        if (!groups[productId]) {
          groups[productId] = {
            product: order.product,
            orders: []
          };
        }
        groups[productId].orders.push(order);
        return groups;
      }, {});

      // 为每个产品发送通知
      for (const productId in productGroups) {
        const group = productGroups[productId];
        const product = group.product;
        const orderCount = group.orders.length;

        const title = '😔 拼团失败通知';
        const content = `很遗憾，${product.name} 拼团已截止，由于人数不足未能成团。已为您取消订单，如有疑问请联系客服。感谢您的理解与支持！`;

        await this.messageNotificationService.publishAnnouncement({
          title,
          content,
          senderId: 1, // 系统管理员ID
          priority: 1, // 重要
          publishTime: new Date(),
          senderRemark: `拼团失败-产品ID:${productId}-订单数:${orderCount}`
        });

        console.log(`已发送拼团失败通知: ${product.name}, 影响订单数: ${orderCount}`);
      }
    } catch (error) {
      console.error('发送拼团失败通知失败:', error);
      throw error;
    }
  }

  /**
   * 发送拼团失败通知（未成团）
   */
  private async sendGroupFailureNotificationForUnfulfilled(failedGroups: any[]) {
    try {
      for (const group of failedGroups) {
        const { product, orders, requiredPeople, actualPeople } = group;

        const title = '😔 拼团失败通知';
        const content = `很遗憾，${product.name} 拼团已截止，需要${requiredPeople}人成团，实际只有${actualPeople}人参团，未能成团。已为您取消订单，如有疑问请联系客服。感谢您的理解与支持！`;

        await this.messageNotificationService.publishAnnouncement({
          title,
          content,
          senderId: 1, // 系统管理员ID  
          priority: 1, // 重要
          publishTime: new Date(),
          senderRemark: `拼团失败-产品ID:${product.id}-需要${requiredPeople}人-实际${actualPeople}人`
        });

        console.log(`已发送拼团失败通知: ${product.name}, 需要${requiredPeople}人，实际${actualPeople}人`);
      }
    } catch (error) {
      console.error('发送拼团失败通知失败:', error);
      throw error;
    }
  }

  /**
   * 测试定时任务 - 每分钟向message_info表添加一条测试数据
   * 用于验证定时任务系统是否正常工作
   */
  async testScheduleTask() {
    try {
      const currentTime = new Date();
      const testData = {
        name: '定时任务测试',
        phone: '13800000000',
        email: '<EMAIL>',
        content: `这是一条定时任务测试数据，生成时间：${currentTime.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}`
      };

      const result = await this.messageInfoEntity.save(testData);

      console.log(`✅ 定时任务测试成功 - 时间: ${currentTime.toLocaleString('zh-CN')}, ID: ${result.id}`);

      return {
        success: true,
        message: '定时任务测试数据添加成功',
        time: currentTime.toLocaleString('zh-CN'),
        data: result
      };
    } catch (error) {
      console.error('❌ 定时任务测试失败:', error);
      throw error;
    }
  }
}
