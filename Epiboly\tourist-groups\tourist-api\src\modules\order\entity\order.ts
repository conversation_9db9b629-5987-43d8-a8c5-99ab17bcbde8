import { BaseEntity } from '../../base/entity/base';
import { Column, Entity, Index, ManyToOne, JoinColumn } from 'typeorm';
import { ProductProductEntity } from '../../product/entity/product';
import { OrderGroupEntity } from './group';

/**
 * 订单信息
 */
@Entity('order_order')
export class OrderOrderEntity extends BaseEntity {
  @Index({ unique: true })
  @Column({ comment: '订单号', length: 50 })
  orderNumber: string;

  @Index()
  @Column({ comment: '产品ID' })
  productId: number;

  // 与产品表的关联关系
  @ManyToOne(() => ProductProductEntity, { eager: true })
  @JoinColumn({ name: 'productId' })
  product: ProductProductEntity;

  @Column({ comment: '订单类型', dict: ['预定订单', '散拼团'], default: 0 })
  orderType: number;

  @Column({
    comment: '订单状态',
    dict: ['已预定', '已入团', '已失效'],
    default: 0,
  })
  orderStatus: number;

  @Column({ comment: '用户确认状态', default: false })
  userConfirmed: boolean;

  @Column({ comment: '订单总金额', type: 'decimal', precision: 10, scale: 2, nullable: true })
  totalAmount: number;

  @Column({ comment: '出行人数', type: 'int', default: 1 })
  quantity: number;

  @Column({ comment: '联系人姓名', length: 50, nullable: true })
  contactName: string;

  @Column({ comment: '联系人电话', length: 50, nullable: true })
  contactPhone: string;

  @Column({ comment: '联系人邮箱', length: 50, nullable: true })
  contactEmail: string;

  @Column({ comment: '预订日期', type: 'datetime', nullable: true })
  bookingDate: Date;

  @Column({ comment: '备注信息', type: 'text', nullable: true })
  remarks: string;

  @Column({ comment: '用户ID', type: 'int', nullable: true })
  userId: number;

  @Column({ comment: '团ID', type: 'int', nullable: true })
  groupId: number;

  // 与团表的关联关系
  @ManyToOne(() => OrderGroupEntity, group => group.orders)
  @JoinColumn({ name: 'groupId' })
  group: OrderGroupEntity;
}
