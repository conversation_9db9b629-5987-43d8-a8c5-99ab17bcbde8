<template>
  <el-dialog 
    v-model="dialogVisible" 
    :title="isGroupMode ? '加入拼团' : '立即预定'" 
    width="500px" 
    :before-close="handleClose"
    :modal="true"
    :lock-scroll="true"
  >
    <el-form 
      :model="form" 
      :rules="rules" 
      ref="bookingFormRef" 
      label-width="100px"
      label-position="left"
    >
      <el-form-item label="出行人数" prop="quantity">
        <el-input-number 
          v-model="form.quantity" 
          :min="1" 
          :max="50"
          controls-position="right"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="联系人姓名" prop="contactName">
        <el-input 
          v-model="form.contactName" 
          placeholder="请输入联系人姓名"
          maxlength="20"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="联系人电话" prop="contactPhone">
        <el-input 
          v-model="form.contactPhone" 
          placeholder="请输入联系人电话"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="联系人邮箱" prop="contactEmail">
        <el-input 
          v-model="form.contactEmail" 
          placeholder="请输入联系人邮箱"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="预定日期" prop="bookingDate">
        <el-date-picker
          v-model="form.bookingDate"
          type="date"
          placeholder="请选择预定日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :disabled-date="disabledDate"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="备注信息" prop="remarks">
        <el-input 
          v-model="form.remarks" 
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息（选填）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <!-- 价格信息 -->
      <div class="price-info">
        <div class="flex justify-between items-center mb-2">
          <span class="text-gray-600">单价：</span>
          <span class="text-lg text-red-600 font-bold">${{ productPrice }}</span>
        </div>
        <div class="flex justify-between items-center mb-2">
          <span class="text-gray-600">数量：</span>
          <span class="text-gray-800">{{ form.quantity }} 人</span>
        </div>
        <div class="flex justify-between items-center border-t pt-2">
          <span class="text-lg font-bold text-gray-800">总价：</span>
          <span class="text-xl text-red-600 font-bold">${{ totalAmount }}</span>
        </div>
      </div>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="submitting"
        >
          {{ isGroupMode ? '确认加入' : '确认预定' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { orderApi } from '@/api';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  productInfo: {
    type: Object,
    default: () => ({})
  },
  isGroupMode: {
    type: Boolean,
    default: false // false: 预定模式, true: 拼团模式
  }
});

// Emits
const emit = defineEmits(['update:visible', 'success']);

// Reactive data
const bookingFormRef = ref();
const submitting = ref(false);

// Form data
const form = reactive({
  quantity: 1,
  contactName: '',
  contactPhone: '',
  contactEmail: '',
  bookingDate: '',
  remarks: ''
});

// Form validation rules
const rules = {
  quantity: [
    { required: true, message: '请输入出行人数', trigger: 'blur' },
    { type: 'number', min: 1, max: 50, message: '出行人数必须在1-50之间', trigger: 'blur' }
  ],
  contactName: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在2-20个字符', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系人电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  contactEmail: [
    { required: true, message: '请输入联系人邮箱', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/, message: '请输入正确的邮箱', trigger: 'blur' }
  ],
  bookingDate: [
    { required: true, message: '请选择预定日期', trigger: 'change' }
  ]
};

// Computed properties
const productPrice = computed(() => {
  return props.productInfo.price || 0;
});

const totalAmount = computed(() => {
  return (productPrice.value * form.quantity).toFixed(2);
});

// 处理弹窗显示状态的计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 禁用日期函数
const disabledDate = (time) => {
  if (!props.productInfo.startDate || !props.productInfo.endDate) {
    return false;
  }
  
  const startDate = new Date(props.productInfo.startDate);
  const endDate = new Date(props.productInfo.endDate);
  const currentDate = new Date(time);
  
  // 禁用小于开始日期或大于结束日期的日期
  return currentDate < startDate || currentDate > endDate;
};

// 监听弹窗显示状态，重置表单
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm();
  }
});

// Methods
const resetForm = () => {
  form.quantity = 1;
  form.contactName = '';
  form.contactPhone = '';
  form.contactEmail = '';
  form.bookingDate = '';
  form.remarks = '';
  
  if (bookingFormRef.value) {
    bookingFormRef.value.clearValidate();
  }
};

const handleClose = () => {
  emit('update:visible', false);
};

const handleSubmit = async () => {
  if (!bookingFormRef.value) return;
  
  try {
    const valid = await bookingFormRef.value.validate();
    if (!valid) return;
    
    submitting.value = true;
    
    const orderData = {
      productId: props.productInfo.id,
      quantity: form.quantity,
      contactName: form.contactName,
      contactPhone: form.contactPhone,
      contactEmail: form.contactEmail,
      bookingDate: form.bookingDate,
      remarks: form.remarks
    };
    
    let res;
    if (props.isGroupMode) {
      // 拼团模式
      res = await orderApi.joinGroup(orderData);
      if (res && res.code === 1000) {
        ElMessage.success('成功加入拼团！');
        emit('success', res.data);
        handleClose();
      } else {
        ElMessage.error(res?.message || '加入拼团失败，请重试');
      }
    } else {
      // 预定模式
      res = await orderApi.createOrder(orderData);
      if (res && res.code === 1000) {
        ElMessage.success('预定成功！');
        emit('success', res.data);
        handleClose();
      } else {
        ElMessage.error(res?.message || '预定失败，请重试');
      }
    }
  } catch (error) {
    console.error(error);
  } finally {
    submitting.value = false;
  }
};
</script>

<style scoped>
.price-info {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-top: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__header) {
  background-color: #f5f5f5;
  padding: 20px 20px 10px 20px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style> 