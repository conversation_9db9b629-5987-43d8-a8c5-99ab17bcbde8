<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<cl-flex1 />
			<!-- 条件搜索 -->
			<cl-search ref="Search" />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
	</cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
	name: "cooperation-agreement",
});

import { useCrud, useTable, useUpsert, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useI18n } from "vue-i18n";

const { service } = useCool();
const { t } = useI18n();

// cl-upsert
const Upsert = useUpsert({
	items: [
		{
			label: t("优势一标题"),
			prop: "tab1Title",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("优势一封面"),
			prop: "advantageOnePic",
			component: { name: "cl-upload" },
		},
		{
			label: t("优势二标题"),
			prop: "tab2Title",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("优势二封面"),
			prop: "advantageTwoPic",
			component: { name: "cl-upload" },
		},
		{
			label: t("优势三标题"),
			prop: "tab3Title",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("优势三封面"),
			prop: "advantageThreePic",
			component: { name: "cl-upload" },
		},
		{
			label: t("培训支持内容"),
			prop: "trainingSupport",
			component: {
				name: "el-input",
				props: { type: "textarea", rows: 4 },
			},
		},
		{
			label: t("技术支持内容"),
			prop: "technologySupport",
			component: {
				name: "el-input",
				props: { type: "textarea", rows: 4 },
			},
		},
		{
			label: t("市场支持内容"),
			prop: "marketSupport",
			component: {
				name: "el-input",
				props: { type: "textarea", rows: 4 },
			},
		},
		{
			label: t("服务支持内容"),
			prop: "serviceSupport",
			component: {
				name: "el-input",
				props: { type: "textarea", rows: 4 },
			},
		},
	],
});

// cl-table
const Table = useTable({
	columns: [
		{ type: "selection" },
		{
			label: t("优势一封面"),
			prop: "advantageOnePic",
			minWidth: 100,
			component: { name: "cl-image", props: { size: 60 } },
		},
		{ label: t("优势二标题"), prop: "tab2Title", minWidth: 140 },
		{
			label: t("优势二封面"),
			prop: "advantageTwoPic",
			minWidth: 100,
			component: { name: "cl-image", props: { size: 60 } },
		},
		{ label: t("优势三标题"), prop: "tab3Title", minWidth: 140 },
		{
			label: t("优势三封面"),
			prop: "advantageThreePic",
			minWidth: 100,
			component: { name: "cl-image", props: { size: 60 } },
		},
		{
			label: t("培训支持内容"),
			prop: "trainingSupport",
			showOverflowTooltip: true,
			minWidth: 200,
		},
		{
			label: t("技术支持内容"),
			prop: "technologySupport",
			showOverflowTooltip: true,
			minWidth: 200,
		},
		{
			label: t("市场支持内容"),
			prop: "marketSupport",
			showOverflowTooltip: true,
			minWidth: 200,
		},
		{
			label: t("服务支持内容"),
			prop: "serviceSupport",
			showOverflowTooltip: true,
			minWidth: 200,
		},
		{
			label: t("创建时间"),
			prop: "createTime",
			minWidth: 170,
			sortable: "desc",
			component: { name: "cl-date-text" },
		},
		{
			label: t("更新时间"),
			prop: "updateTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{ type: "op", buttons: ["edit", "delete"] },
	],
});

// cl-search
const Search = useSearch();

// cl-crud
const Crud = useCrud(
	{
		service: service.cooperation.agreement,
	},
	(app) => {
		app.refresh();
	},
);

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}
</script>
