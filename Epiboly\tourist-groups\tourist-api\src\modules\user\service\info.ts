import { BaseService, CoolCommException } from '@cool-midway/core';
import { Inject, Provide } from '@midwayjs/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import * as md5 from 'md5';
import { Equal, Repository } from 'typeorm';
import { v1 as uuid } from 'uuid';
import { PluginService } from '../../plugin/service/info';
import { UserInfoEntity } from '../entity/info';
import { UserSmsService } from './sms';
import { UserWxService } from './wx';
import { ApplicationApplyEntity } from '../../application/entity/apply';

/**
 * 用户信息
 */
@Provide()
export class UserInfoService extends BaseService {
  @InjectEntityModel(UserInfoEntity)
  userInfoEntity: Repository<UserInfoEntity>;

  @InjectEntityModel(ApplicationApplyEntity)
  applicationApplyEntity: Repository<ApplicationApplyEntity>;

  @Inject()
  pluginService: PluginService;

  @Inject()
  userSmsService: UserSmsService;

  @Inject()
  userWxService: UserWxService;

  /**
   * 绑定小程序手机号
   * @param userId
   * @param code
   * @param encryptedData
   * @param iv
   */
  async miniPhone(userId: number, code: any, encryptedData: any, iv: any) {
    const phone = await this.userWxService.miniPhone(code, encryptedData, iv);
    await this.userInfoEntity.update({ id: Equal(userId) }, { phone });
    return phone;
  }

  /**
   * 获取用户信息
   * @param id
   * @returns
   */
  async person(id) {
    const info = await this.userInfoEntity.findOneBy({ id: Equal(id) });
    delete info.password;
    return info;
  }

  /**
   * 注销
   * @param userId
   */
  async logoff(userId: number) {
    await this.userInfoEntity.update(
      { id: userId },
      {
        status: 2,
        phone: null,
        unionid: null,
        nickName: `已注销-00${userId}`,
        avatarUrl: null,
      }
    );
  }

  /**
   * 更新用户信息
   * @param id
   * @param param
   * @returns
   */
  async updatePerson(id, param) {
    const info = await this.person(id);
    if (!info) throw new CoolCommException('用户不存在');
    try {
      // 修改了头像要重新处理
      if (param.avatarUrl && info.avatarUrl != param.avatarUrl) {
        const file = await this.pluginService.getInstance('upload');
        param.avatarUrl = await file.downAndUpload(
          param.avatarUrl,
          uuid() + '.png'
        );
      }
    } catch (err) { }
    try {
      return await this.userInfoEntity.update({ id }, param);
    } catch (err) {
      throw new CoolCommException('更新失败，参数错误或者手机号已存在');
    }
  }

  /**
   * 更新密码
   * @param userId
   * @param password
   * @param 验证码
   */
  async updatePassword(userId, password, code) {
    const user = await this.userInfoEntity.findOneBy({ id: userId });
    const check = await this.userSmsService.checkCode(user.phone, code);
    if (!check) {
      throw new CoolCommException('验证码错误');
    }
    await this.userInfoEntity.update(user.id, { password: md5(password) });
  }

  /**
   * 绑定手机号
   * @param userId
   * @param phone
   * @param code
   */
  async bindPhone(userId, phone, code) {
    const check = await this.userSmsService.checkCode(phone, code);
    if (!check) {
      throw new CoolCommException('验证码错误');
    }
    await this.userInfoEntity.update({ id: userId }, { phone });
  }

  /**
   * 获取用户关联的申请信息
   * @param userId 用户ID
   */
  async getUserApplyInfo(userId: number) {
    const apply = await this.applicationApplyEntity.findOne({
      where: { userId }
    });
    return apply;
  }

  /**
   * 管理员修改用户密码
   * @param userId 用户ID
   * @param password 新密码
   */
  async updateUserPassword(userId: number, password: string) {
    const user = await this.userInfoEntity.findOneBy({ id: userId });
    if (!user) {
      throw new CoolCommException('用户不存在');
    }

    // 这里应该对密码进行加密处理
    const hashedPassword = md5(password);

    await this.userInfoEntity.update(userId, { password: hashedPassword });
    return { success: true, message: '密码修改成功' };
  }

  /**
   * 更新用户状态
   * @param userId 用户ID
   * @param status 状态 0-禁用 1-正常 2-已注销
   */
  async updateUserStatus(userId: number, status: number) {
    const user = await this.userInfoEntity.findOneBy({ id: userId });
    if (!user) {
      throw new CoolCommException('用户不存在');
    }

    // 验证状态值
    if (![0, 1, 2].includes(status)) {
      throw new CoolCommException('无效的状态值');
    }

    await this.userInfoEntity.update(userId, { status });

    const statusText = status === 0 ? '禁用' : status === 1 ? '正常' : '已注销';
    return { success: true, message: `用户状态已更新为${statusText}` };
  }

  /**
   * 更新用户基本信息
   * @param userId 用户ID
   * @param updateData 更新数据
   */
  async updateBasicInfo(userId: number, updateData: {
    nickName?: string;
    avatarUrl?: string;
    status?: number;
    gender?: number;
  }) {
    const user = await this.userInfoEntity.findOneBy({ id: userId });
    if (!user) {
      throw new CoolCommException('用户不存在');
    }

    // 验证状态值
    if (updateData.status !== undefined && ![0, 1, 2].includes(updateData.status)) {
      throw new CoolCommException('无效的状态值');
    }

    // 验证性别值
    if (updateData.gender !== undefined && ![0, 1, 2].includes(updateData.gender)) {
      throw new CoolCommException('无效的性别值');
    }

    // 处理头像上传
    if (updateData.avatarUrl && user.avatarUrl !== updateData.avatarUrl) {
      try {
        const file = await this.pluginService.getInstance('upload');
        updateData.avatarUrl = await file.downAndUpload(
          updateData.avatarUrl,
          uuid() + '.png'
        );
      } catch (err) {
        // 如果头像处理失败，继续更新其他信息
        console.error('头像处理失败:', err);
      }
    }

    await this.userInfoEntity.update(userId, updateData);
    return { success: true, message: '用户信息更新成功' };
  }
}
