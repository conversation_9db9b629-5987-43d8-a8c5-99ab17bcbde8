<template>
  <div class="user-profile">
    <!-- 用户头像区域 -->
    <div class="avatar-container">
      <div class="user-avatar">
        <img 
          v-if="userInfo.avatarUrl" 
          :src="userInfo.avatarUrl" 
          :alt="userInfo.nickName || '用户头像'"
          class="avatar-image"
        />
        <div v-else class="avatar-icon"></div>
      </div>
      <div class="user-name">{{ userInfo.nickName || '未设置昵称' }}</div>
      <div class="user-id">{{ userInfo.phone || userInfo.userId || '未知用户' }}</div>
    </div>
    
    <!-- 信息资料标题 -->
    <div class="info-title">
      <h3>信息资料</h3>
    </div>
    
    <!-- 菜单列表 -->
    <div class="menu-container">
      <div 
        class="menu-item" 
        :class="{ active: activeMenu === 'orders' }" 
        @click="handleMenuSelect('orders')">
        <div class="menu-icon orders-icon"></div>
        <span class="menu-text">我的订单</span>
      </div>
      
      <div 
        class="menu-item" 
        :class="{ active: activeMenu === 'notifications' }" 
        @click="handleMenuSelect('notifications')">
        <div class="menu-icon notification-icon"></div>
        <span class="menu-text">通知</span>
      </div>
      
      <div 
        class="menu-item" 
        :class="{ active: activeMenu === 'announcements' }" 
        @click="handleMenuSelect('announcements')">
        <div class="menu-icon announcement-icon"></div>
        <span class="menu-text">公告</span>
      </div>

      <div style="height: 10px;background-color: #ffffff;"></div>
      <div style="height: 5px;background-color: #c9161c;"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';

// 定义 props
const props = defineProps({
  activeSection: {
    type: String,
    default: 'orders'
  }
});

// 声明事件 - 需要在组件顶部声明
const emit = defineEmits(['menu-select']);

// 用户信息
const userInfo = ref({
  userId: '',
  nickName: '',
  phone: '',
  avatarUrl: '',
  gender: null
});

const route = useRoute();
const type = parseInt(route.query.type) || 0;

// 激活的菜单项 - 默认为 orders（我的拼团）
const activeMenu = ref('orders');

// 监听 props 变化，同步更新本地状态
watch(() => props.activeSection, (newValue) => {
  activeMenu.value = newValue;
}, { immediate: true });

// 从localStorage获取用户信息
const getUserInfo = () => {
  try {
    const storedUserInfo = localStorage.getItem('userInfo');
    if (storedUserInfo) {
      const userData = JSON.parse(storedUserInfo);
      userInfo.value = {
        userId: userData.id || userData.userId || '',
        nickName: userData.nickName || '',
        phone: userData.phone || '',
        avatarUrl: userData.avatarUrl || '',
        gender: userData.gender
      };
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
  }
};

// 菜单选择事件
const handleMenuSelect = (menuType) => {
  console.log('UserProfile 菜单点击:', menuType); // 调试日志
  activeMenu.value = menuType;
  // 触发父组件事件，让父组件切换显示的组件
  emit('menu-select', menuType);
};

// 根据type设置默认激活菜单
const setActiveMenuByType = (type) => {
  const menuMap = {
    0: 'orders',
    1: 'notifications', 
    2: 'announcements'
  };
  activeMenu.value = menuMap[type] || 'orders';
};

// 组件挂载时获取用户信息和设置菜单
onMounted(() => {
  getUserInfo();
  setActiveMenuByType(type);
});
</script>

<style scoped>
.user-profile {
  background-color: #fff;
  overflow: hidden;
  border: 1px solid #e9e9e9;
}

/* 头像容器 */
.avatar-container {
  padding: 20px 10px 20px;
  text-align: center;
  background-color: #fff;
}

.user-avatar {
  width: 87px;
  height: 87px;
  margin: 0 auto 10px;
  position: relative;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-icon {
  width: 80px;
  height: 80px;
  background-color: #dc3545;
  border-radius: 50%;
  position: relative;
}

.avatar-icon::before {
  content: '';
  position: absolute;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 30px;
  background-color: white;
  border-radius: 50%;
}

.avatar-icon::after {
  content: '';
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 25px;
  background-color: white;
  border-radius: 25px 25px 0 0;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.user-name {
  font-size: 18px;
  color: #333;
  font-weight: bold;
  margin-bottom: 5px;
}

.user-id {
  font-size: 14px;
  color: #999;
  font-weight: normal;
}

/* 信息资料标题 */
.info-title {
  padding: 12px 15px 15px;
  border-bottom: 1px solid #f0f0f0;
}

.info-title h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
  font-weight: bold;
}

/* 菜单容器 */
.menu-container {
  padding: 0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid #f5f5f5;
  position: relative;
}

.menu-item:hover {
  background-color: #f8f9fa;
}

.menu-item.active {
  background-color: #c8161b;
  color: white;
}

.menu-item.active .menu-text {
  color: white;
  font-weight: bold;
}

.menu-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  flex-shrink: 0;
}

/* 订单图标 */
.orders-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z'/%3E%3C/svg%3E");
}

.menu-item.active .orders-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z'/%3E%3C/svg%3E");
}

/* 通知图标 */
.notification-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M21,19V20H3V19L5,17V11C5,7.9 7.03,5.17 10,4.29C10,4.19 10,4.1 10,4A2,2 0 0,1 12,2A2,2 0 0,1 14,4C14,4.1 14,4.19 14,4.29C16.97,5.17 19,7.9 19,11V17L21,19M14,21A2,2 0 0,1 12,23A2,2 0 0,1 10,21'/%3E%3C/svg%3E");
}

.menu-item.active .notification-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M21,19V20H3V19L5,17V11C5,7.9 7.03,5.17 10,4.29C10,4.19 10,4.1 10,4A2,2 0 0,1 12,2A2,2 0 0,1 14,4C14,4.1 14,4.19 14,4.29C16.97,5.17 19,7.9 19,11V17L21,19M14,21A2,2 0 0,1 12,23A2,2 0 0,1 10,21'/%3E%3C/svg%3E");
}

/* 公告图标 */
.announcement-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,17H13V19H11V17M11,5H13V15H11V5Z'/%3E%3C/svg%3E");
}

.menu-item.active .announcement-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,17H13V19H11V17M11,5H13V15H11V5Z'/%3E%3C/svg%3E");
}

.menu-text {
  font-size: 14px;
  color: #333;
  transition: color 0.3s ease;
}

/* 移除最后一个菜单项的边框 */
.menu-item:last-child {
  border-bottom: none;
}
</style> 