<template>
  <cl-crud ref="Crud">
    <cl-row>
      <!-- 刷新按钮 -->
      <cl-refresh-btn />
      <!-- 新增按钮 -->
      <cl-add-btn />
      <!-- 删除按钮 -->
      <cl-multi-delete-btn />
      <cl-flex1 />
      <!-- 条件搜索 -->
      <cl-search ref="Search" />
    </cl-row>

    <cl-row>
      <!-- 数据表格 -->
      <cl-table ref="Table" />
    </cl-row>

    <cl-row>
      <cl-flex1 />
      <!-- 分页控件 -->
      <cl-pagination />
    </cl-row>

    <!-- 新增、编辑 -->
    <cl-upsert ref="Upsert" />
  </cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
  name: "product-category",
});

import { useCrud, useTable, useUpsert, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useI18n } from "vue-i18n";
import { reactive } from "vue";

const { service } = useCool();
const { t } = useI18n();

// 选项
const options = reactive({
  status: [
    { label: "禁用", value: 0, type: "danger" },
    { label: "启用", value: 1, type: "success" },
  ],
  isShowHome: [
    { label: "否", value: 0, type: "danger" },
    { label: "是", value: 1, type: "success" },
  ],
  isShowBusinessScope: [
    { label: "否", value: 0, type: "danger" },
    { label: "是", value: 1, type: "success" },
  ]
});

// cl-search
const Search = useSearch();

// cl-table
const Table = useTable({
  columns: [
    { type: "selection" },
    { label: "ID", prop: "id", minWidth: 80 },
    { label: "分类名称", prop: "name", minWidth: 150 },
    { 
      label: "展示首页", 
      prop: "isShowHome", 
      minWidth: 80,
      dict: options.isShowHome 
    },
    { 
      label: "展示业务范围", 
      prop: "isShowBusinessScope", 
      minWidth: 80,
      dict: options.isShowBusinessScope 
    },
    { 
      label: "状态", 
      prop: "status", 
      minWidth: 80,
      dict: options.status 
    },
    { label: "排序", prop: "sort", minWidth: 80 },
    { label: "创建时间", prop: "createTime", minWidth: 160 },
    { type: "op", buttons: ["edit", "delete"], width: 160 },
  ],
});

// cl-upsert
const Upsert = useUpsert({
  items: [
    {
      label: "分类名称",
      prop: "name",
      component: { name: "el-input", props: { clearable: true, placeholder: "请输入分类名称" } },
      required: true,
    },
    {
      label: "排序",
      prop: "sort",
      hook: "number",
      component: { name: "el-input-number", props: { min: 0 } },
      value: 0,
    },
    {
      label: "展示首页",
      prop: "isShowHome",
      component: {
        name: "el-radio-group",
        options: options.isShowHome,
      },
    },
    {
      label: "展示业务范围",
      prop: "isShowBusinessScope",
      component: {
        name: "el-radio-group",
        options: options.isShowBusinessScope,
      },
    },
    {
      label: "明细文件",
      prop: "detailFile",
      component: { 
        name: "cl-upload", 
        props: { 
          text: "选择文件",
          limit: 1,
          accept: ".pdf,.doc,.docx,.xls,.xlsx,.txt,.jpg,.jpeg,.png,.gif,.bmp,.webp",
          isSpace: true
        } 
      },
    },
    {
      label: "状态",
      prop: "status",
      component: {
        name: "el-radio-group",
        options: options.status,
      },
      value: 1,
    },
  ]
});

// cl-crud
const Crud = useCrud(
  {
    service: service.product.category,
  },
  (app) => {
    app.refresh();
  }
);
</script> 