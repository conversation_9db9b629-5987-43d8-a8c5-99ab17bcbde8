import { Controller, Get, Inject, Query, Post, Body } from '@midwayjs/core';
import { CoolController, BaseController } from '@cool-midway/core';
import { OrderOrderService } from '../service/order';

/**
 * 订单前端接口
 */
@CoolController('/app/order')
export class AppOrderController extends BaseController {
    @Inject()
    orderOrderService: OrderOrderService;

    @Inject()
    ctx;

    /**
     * 获取我的订单列表
     */
    @Get('/my-orders', { summary: '获取我的订单列表' })
    async getMyOrders(@Query() query: any) {
        const { page = 1, pageSize = 10, orderType = 1 } = query;

        // 从token中获取用户信息
        const userId = this.ctx.user?.id;
        if (!userId) {
            return this.fail('用户未登录');
        }

        try {
            const result = await this.orderOrderService.getMyOrdersByUserId(userId, Number(page), Number(pageSize), Number(orderType));
            return this.ok(result);
        } catch (error) {
            return this.fail(error.message || '获取订单列表失败');
        }
    }

    /**
     * 获取我的订单统计数据
     */
    @Get('/my-orders-stats', { summary: '获取我的订单统计数据' })
    async getMyOrdersStats() {
        // 从token中获取用户信息
        const userId = this.ctx.user?.id;
        if (!userId) {
            return this.fail('用户未登录');
        }

        try {
            const result = await this.orderOrderService.getMyOrdersStats(userId);
            return this.ok(result);
        } catch (error) {
            return this.fail(error.message || '获取订单统计失败');
        }
    }

    /**
     * 确认订单
     */
    @Post('/confirm', { summary: '确认订单' })
    async confirmOrder(@Body() data: any) {
        const { orderNumber, contactPhone } = data;

        if (!orderNumber) {
            return this.fail('订单号不能为空');
        }
        if (!contactPhone) {
            return this.fail('联系人电话不能为空');
        }

        try {
            const result = await this.orderOrderService.confirmOrder(orderNumber, contactPhone);
            return this.ok(result);
        } catch (error) {
            return this.fail(error.message || '确认订单失败');
        }
    }

    /**
     * 获取产品拼团信息 - 支持多团
     */
    @Get('/group-info')
    async getGroupInfo(@Query('productId') productId: number) {
        if (!productId) {
            return this.fail('产品ID不能为空');
        }

        // 从token中获取用户ID（如果用户已登录）
        const userId = this.ctx.user?.id;

        const groupInfo = await this.orderOrderService.getGroupInfo(productId, userId);
        return this.ok(groupInfo);
    }

    /**
 * 加入拼团
 */
    @Post('/join-group')
    async joinGroup(@Body() orderData: any) {
        const { productId, quantity, contactName, contactPhone, bookingDate, remarks } = orderData;

        if (!productId) {
            return this.fail('产品ID不能为空');
        }
        if (!quantity || quantity <= 0) {
            return this.fail('出行人数必须大于0');
        }
        if (!contactName) {
            return this.fail('联系人姓名不能为空');
        }
        if (!contactPhone) {
            return this.fail('联系人电话不能为空');
        }
        if (!bookingDate) {
            return this.fail('预订日期不能为空');
        }

        try {
            // 从token中获取用户ID（如果用户已登录）
            const userId = this.ctx.user?.id;

            const result = await this.orderOrderService.createGroupOrder({
                productId,
                quantity,
                contactName,
                contactPhone,
                bookingDate,
                remarks,
                userId
            });
            return this.ok(result);
        } catch (error) {
            return this.fail(error.message || '加入拼团失败');
        }
    }

    /**
     * 创建预定订单
     */
    @Post('/create')
    async createOrder(@Body() orderData: any) {
        const { productId, quantity, contactName, contactPhone, contactEmail, bookingDate, remarks } = orderData;

        if (!productId) {
            return this.fail('产品ID不能为空');
        }
        if (!quantity || quantity <= 0) {
            return this.fail('出行人数必须大于0');
        }
        if (!contactName) {
            return this.fail('联系人姓名不能为空');
        }
        if (!contactPhone) {
            return this.fail('联系人电话不能为空');
        }
        if (!bookingDate) {
            return this.fail('预订日期不能为空');
        }

        try {
            // 从token中获取用户ID（如果用户已登录）
            const userId = this.ctx.user?.id;

            const result = await this.orderOrderService.createBookingOrder({
                productId,
                quantity,
                contactName,
                contactPhone,
                contactEmail,
                bookingDate,
                remarks,
                userId
            });
            return this.ok(result);
        } catch (error) {
            return this.fail(error.message || '创建订单失败');
        }
    }
}