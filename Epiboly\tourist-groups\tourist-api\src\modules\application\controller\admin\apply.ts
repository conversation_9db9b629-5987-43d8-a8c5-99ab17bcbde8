import { Inject, Post, Body } from '@midwayjs/core';
import { Cool<PERSON><PERSON>roller, BaseController } from '@cool-midway/core';
import { ApplicationApplyEntity } from '../../entity/apply';
import { ApplicationApplyService } from '../../service/apply';
import { Between } from 'typeorm';

/**
 * 申请信息管理
 */
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: ApplicationApplyEntity,
  service: ApplicationApplyService,
  pageQueryOp: {
    keyWordLikeFields: [
      'a.contactPerson',
      'a.contactPhone',
      'a.companyAddress',
    ],
    fieldEq: ['a.companyType', 'a.country', 'a.region', 'a.status'],
    where: async ctx => {
      const { createTime } = ctx.request.body;
      if (createTime && createTime.length > 0) {
        return [
          [
            'a.createTime BETWEEN :start AND :end',
            { start: createTime[0], end: createTime[1] },
          ],
        ];
      }
    },
  },
})
export class AdminApplicationApplyController extends BaseController {
  @Inject()
  applicationApplyService: ApplicationApplyService;

  /**
   * 分配账号
   */
  @Post('/assignAccount')
  async assignAccount(@Body() body: { id: number; username: string; password: string }) {
    const { id, username, password } = body;
    return await this.applicationApplyService.assignAccount(id, username, password);
  }
}
