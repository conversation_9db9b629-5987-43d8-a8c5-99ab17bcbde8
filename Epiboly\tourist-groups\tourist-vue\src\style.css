@tailwind base;
@tailwind components;
@tailwind utilities;

/* 基础样式 */
:root {
  font-family: system-ui, 'Helvetica Neue', Helvetica, Arial, sans-serif;
  color-scheme: light;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scroll-behavior: smooth;
}

body {
  min-height: 100vh;
  overflow-x: hidden;
  color: #333;
  background-color: #f9fafb;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 按钮点击特效 */
button:active,
a:active {
  transform: translateY(1px);
}

/* 优雅的高光特效 */
.highlight-glow {
  position: relative;
  overflow: hidden;
}

.highlight-glow::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 70%);
  opacity: 0;
  transform: scale(0.5);
  transition: opacity 0.5s, transform 0.5s;
}

.highlight-glow:hover::after {
  opacity: 1;
  transform: scale(1);
}

/* 响应式调整 */
@media (max-width: 640px) {
  h1 {
    font-size: 1.75rem;
  }

  h2 {
    font-size: 1.25rem;
  }
}

/* 加载动画 */
@keyframes float {
  0% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }

  100% {
    transform: translateY(0px);
  }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* 玻璃态效果 */
.glass-effect {
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.7);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

#app {
  width: 100%;
  margin: 0 auto;
}

/* 主题颜色 */
.text-primary {
  color: #ef4444;
}

.bg-primary {
  background-color: #ef4444;
}

.border-primary {
  border-color: #ef4444;
}

.hover-primary:hover {
  color: #ef4444;
}

.btn-primary {
  background-color: #ef4444;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: #dc2626;
}