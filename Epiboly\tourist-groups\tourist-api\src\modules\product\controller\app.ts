import { Body, Controller, Get, Inject, Post, Query } from '@midwayjs/core';
import { CoolController, BaseController } from '@cool-midway/core';
import { ProductProductService } from '../service/product';
import { ProductCategoryService } from '../service/category';

/**
 * 产品前端接口
 */
@CoolController('/app/product')
export class AppProductController extends BaseController {
    @Inject()
    productProductService: ProductProductService;

    @Inject()
    productCategoryService: ProductCategoryService;

    /**
     * 获取产品列表
     */
    @Get('/list')
    async getList(@Query() query: any) {
        const { city, categoryId, theme, page, size, isHot, dateRange } = query;
        return this.ok(await this.productProductService.getProductList({
            city,
            categoryId: categoryId ? parseInt(categoryId) : undefined,
            theme,
            page: parseInt(page) || 1,
            size: parseInt(size) || 10,
            isHot: isHot === 'true',
            dateRange
        }));
    }

    /**
     * 获取产品详情
     */
    @Get('/detail')
    async detail(@Query('id') id: number) {
        if (!id) {
            return this.fail('产品ID不能为空');
        }

        const product = await this.productProductService.getProductDetail(id);
        if (!product) {
            return this.fail('产品不存在');
        }

        return this.ok(product);
    }

    /**
     * 获取推荐产品
     */
    @Get('/recommended')
    async recommended(@Query('limit') limit?: number) {
        const products = await this.productProductService.getRecommendedProducts(
            limit ? parseInt(limit.toString()) : 6
        );
        return this.ok(products);
    }

    /**
     * 根据分类ID获取产品
     */
    @Get('/categoryProducts')
    async getByCategoryId(@Query() query: any) {
        const { categoryId, limit } = query;
        if (!categoryId) {
            return this.fail('分类ID不能为空');
        }

        const products = await this.productProductService.getProductsByCategoryId(
            parseInt(categoryId),
            limit ? parseInt(limit) : 10
        );
        return this.ok(products);
    }

    /**
     * 搜索产品
     */
    @Get('/search')
    async search(@Query() query: any) {
        const { keyword, page, size } = query;
        if (!keyword) {
            return this.fail('搜索关键词不能为空');
        }

        const result = await this.productProductService.searchProducts(
            keyword,
            parseInt(page) || 1,
            parseInt(size) || 10
        );
        return this.ok(result);
    }

    /**
     * 获取热门城市
     */
    @Get('/hot-cities')
    async hotCities() {
        const cities = await this.productProductService.getHotCityProducts();
        return this.ok(cities);
    }

    /**
     * 获取城市列表
     */
    @Get('/cities')
    async getCities() {
        const cities = await this.productProductService.getCityList();
        return this.ok(cities);
    }

    /**
     * 获取产品统计信息
     */
    @Get('/stats')
    async stats() {
        // 可以添加一些统计信息，比如总产品数、热门产品数等
        const totalCount = await this.productProductService.productProductEntity.count({
            where: { status: 1 }
        });

        const hotCount = await this.productProductService.productProductEntity.count({
            where: { status: 1, isHot: 1 }
        });

        return this.ok({
            totalCount,
            hotCount
        });
    }

    /**
     * 获取分类列表
     */
    @Get('/category/list')
    async getCategoryList(@Query() query: any) {
        const categories = await this.productCategoryService.list(query);
        return this.ok(categories);
    }
}