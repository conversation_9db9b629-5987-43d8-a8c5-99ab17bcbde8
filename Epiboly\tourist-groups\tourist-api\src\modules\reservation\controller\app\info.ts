import { Inject, Get } from '@midwayjs/core';
import { Cool<PERSON><PERSON>roller, BaseController } from '@cool-midway/core';
import { ReservationInfoEntity } from '../../entity/info';
import { ReservationInfoService } from '../../service/info';

/**
 * 预定信息app端
 */
@CoolController({
    prefix: '/app/reservation',
    api: [],
    entity: ReservationInfoEntity,
    service: ReservationInfoService,
})
export class AppReservationInfoController extends BaseController {
    @Inject()
    reservationInfoService: ReservationInfoService;

    /**
     * 获取预定信息列表
     */
    @Get('/list', { summary: '获取预定信息列表' })
    async list() {
        const data = await this.reservationInfoService.reservationInfoEntity.find({
            order: { createTime: 'DESC' }
        });

        console.log(data);

        return this.ok(data.map(item => ({
            id: item.id,
            name: item.name,
            content: item.content,
            coverImage: item.coverImage,
        })));
    }
} 