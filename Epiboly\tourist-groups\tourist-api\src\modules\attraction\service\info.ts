import { Provide } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { AttractionInfoEntity } from '../entity/info';

/**
 * 景点信息
 */
@Provide()
export class AttractionInfoService extends BaseService {
  @InjectEntityModel(AttractionInfoEntity)
  attractionInfoEntity: Repository<AttractionInfoEntity>;
}
