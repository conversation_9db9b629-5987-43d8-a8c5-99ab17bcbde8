# 消息通知模块

## 功能概述

消息通知模块提供了完整的消息管理功能，支持系统消息和公告两种类型的消息。管理员可以在后台管理系统中发送系统消息和发布公告，用户可以在前端查看和管理这些消息。

## 主要功能

### 1. 消息类型
- **系统消息（type: 0）**: 用于发送给特定用户或所有用户的通知消息
- **公告（type: 1）**: 用于发布面向所有用户的公告信息

### 2. 优先级管理
- **普通（priority: 0）**: 一般信息
- **重要（priority: 1）**: 重要信息，会有特殊标识
- **紧急（priority: 2）**: 紧急信息，会有醒目标识

### 3. 已读状态管理
- 支持消息已读/未读状态跟踪
- 支持批量标记已读
- 自动统计未读消息数量

## API接口

### 后台管理接口 (`/admin/message/notification`)

#### 基础CRUD操作
- `GET /page` - 分页查询消息列表
- `POST /add` - 新增消息
- `POST /update` - 更新消息
- `POST /delete` - 删除消息
- `GET /info` - 获取消息详情

#### 特殊功能接口
- `POST /sendSystemMessage` - 批量发送系统消息
- `POST /publishAnnouncement` - 发布公告
- `POST /markAsRead` - 批量标记已读
- `POST /statistics` - 获取消息统计

### 前端用户接口 (`/app/message/notification`)

- `GET /unreadCount` - 获取用户未读消息数量
- `POST /list` - 获取用户消息列表
- `POST /markAsRead` - 标记单条消息已读
- `POST /markMultipleAsRead` - 批量标记消息已读
- `POST /statistics` - 获取用户消息统计

## 数据表结构

### message_notification

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 主键ID |
| title | varchar | 消息标题 |
| content | text | 消息内容 |
| type | int | 消息类型：0-系统消息，1-公告 |
| receiverId | int | 接收者ID（为空表示全局消息） |
| senderId | int | 发送者ID |
| isRead | boolean | 是否已读 |
| publishTime | datetime | 发布时间 |
| status | int | 状态：0-禁用，1-启用 |
| priority | int | 优先级：0-普通，1-重要，2-紧急 |
| createTime | datetime | 创建时间 |
| updateTime | datetime | 更新时间 |

## 使用指南

### 后台管理

1. **发送系统消息**
   - 可以选择发送给特定用户或所有用户
   - 支持设置优先级
   - 立即发送或定时发送

2. **发布公告**
   - 面向所有用户的公开信息
   - 支持设置发布时间
   - 支持优先级设置

3. **消息管理**
   - 查看所有消息的发送状态
   - 统计已读/未读情况
   - 批量操作支持

### 前端使用

1. **集成组件**
```vue
<template>
  <!-- 系统消息 -->
  <Notifications type="notifications" :userId="currentUserId" />
  
  <!-- 公告 -->
  <Notifications type="announcements" :userId="currentUserId" />
</template>

<script setup>
import Notifications from '@/components/Notifications.vue';

const currentUserId = ref(1); // 当前用户ID
</script>
```

2. **API调用示例**
```javascript
// 获取未读消息数量
const unreadCount = await axios.get('/app/message/notification/unreadCount', {
  params: { userId: 1 }
});

// 获取消息列表
const messages = await axios.post('/app/message/notification/list', {
  userId: 1,
  type: 0, // 0-系统消息，1-公告
  page: 1,
  limit: 10
});

// 标记已读
await axios.post('/app/message/notification/markAsRead', {
  id: 1,
  userId: 1
});
```

## 部署说明

1. **数据库初始化**
   - 运行API服务时会自动创建表结构
   - 初始化示例数据（可选）

2. **后台管理配置**
   - 确保后台管理系统已注册消息模块
   - 配置相应的菜单权限

3. **前端集成**
   - 确保已安装必要的依赖（axios, element-plus等）
   - 配置正确的API地址

## 注意事项

1. **权限控制**
   - 后台接口需要管理员权限
   - 前端接口需要提供正确的用户ID

2. **性能优化**
   - 消息列表支持分页查询
   - 大量消息时建议定期清理历史数据

3. **安全考虑**
   - 用户只能查看属于自己的消息
   - 全局消息对所有用户可见

## 扩展功能

可以考虑添加的功能：
- 消息推送（WebSocket/SSE）
- 邮件/短信通知
- 消息分类管理
- 定时发送消息
- 消息模板管理
- 消息撤回功能 