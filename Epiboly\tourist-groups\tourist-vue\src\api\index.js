import { get, post, put, del } from '../utils/request';
import userApi from './user';
import memberApi from './member';
import cultureApi from './culture';
import reservationApi from './reservation';
import siteApi from './site';
import messageApi from './message';
import cooperationApi from './cooperation';
import applicationApi from './application';
import productApi, { categoryApi } from './product';
import orderApi from './order';
import companyApi from './company';

// Banner相关API
export const commonApi = {
  // 获取Banner列表
  getBannerList: (params) => get('/app/banner/list', params),
  
  // 获取广告列表
  getAdvertList: (params) => get('/app/advert/list', params),
  
  // 获取景点列表
  getAttractionList: (params) => get('/app/attraction/list', params),
  
  // 文件上传
  uploadFile: (file) => {
    const formData = new FormData();
    formData.append('file', file);
    return post('/app/base/comm/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },
};

// 导出cooperation API
export { cooperationApi };

// 导出application API
export { applicationApi };

// 导出product API
export { productApi };

// 导出category API
export { categoryApi };

// 导出order API
export { orderApi };

// 导出company API
export { companyApi };

export default {
  commonApi,
  userApi,
  memberApi,
  cultureApi,
  reservationApi,
  siteApi,
  messageApi,
  cooperationApi,
  applicationApi,
  productApi,
  categoryApi,
  orderApi,
  companyApi
};
