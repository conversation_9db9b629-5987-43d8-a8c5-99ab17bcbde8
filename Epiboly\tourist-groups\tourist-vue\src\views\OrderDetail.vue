<script setup>
import { ref, onMounted, reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { orderApi } from '../api';  // 确保导入 API
import TheHeader from '../components/TheHeader.vue';
import TheFooter from '../components/TheFooter.vue';

const route = useRoute();
const router = useRouter();
const orderData = ref(null);
const isLoading = ref(true);  // 添加 loading 状态

// 请求完成状态追踪
const requestStatus = reactive({
  orderDetail: false
});

onMounted(async () => {
    try {
        // 假设路由参数中有 name 和 phone
        const name = route.query.name;
        const phone = route.query.phone;

        if (name && phone) {
            const response = await orderApi.queryOrder({
                name,
                phone
            });

            if (response?.code === 1000 && response.data?.length > 0) {
                orderData.value = response.data[0];  // 获取第一条订单数据
            } else {
                // 处理查询失败的情况
                console.error('未找到订单数据');
                router.push('/');  // 可以选择跳回首页
            }
        }
    } catch (error) {
        console.error('查询订单失败:', error);
        router.push('/');  // 发生错误时跳回首页
    } finally {
        isLoading.value = false;
        requestStatus.orderDetail = true;
        checkAllRequestsComplete();
    }
});

const goBack = () => {
    router.push('/');
};

// 格式化容量显示
const formatSize = (size) => {
    const num = parseFloat(size);
    if (num < 1) {
        return (num * 1024).toFixed(0) + 'MB';
    }
    return num.toFixed(2) + 'GB';
};

// 格式化时间显示
const formatDate = (dateStr) => {
    const date = new Date(dateStr);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
};

// 检查所有请求是否完成并执行翻译
const checkAllRequestsComplete = () => {
    const allComplete = Object.values(requestStatus).every(status => status === true);
    if (allComplete) {
        setTimeout(() => {
            translate.execute();
        }, 50);
    }
};
</script>

<template>
    <div class="min-h-screen bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900 text-white">
        <!-- 顶部导航栏 -->
        <div class="sticky top-0 z-10 bg-gray-900/80 backdrop-blur-md border-b border-gray-800">
            <div class="max-w-5xl mx-auto">
                <div class="px-4 py-4 flex items-center justify-between">
                    <button @click="goBack" class="flex items-center text-gray-400 hover:text-white transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                        </svg>
                        <span class="ml-2 text-lg font-medium">返回</span>
                    </button>
                    <h1 class="text-lg sm:text-xl font-bold bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent">订单详情</h1>
                </div>
            </div>
        </div>

        <div class="max-w-5xl mx-auto px-4 py-6">
            <!-- 订单信息卡片 -->
            <div v-if="orderData" class="space-y-6">
                <!-- 基本信息卡片 -->
                <div class="bg-gray-800/60 rounded-3xl p-6 sm:p-8 shadow-lg border border-gray-700/50 hover:border-gray-600/50 transition-colors">
                    <h2 class="text-xl sm:text-2xl font-bold bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent mb-6 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-3 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                        订单信息
                    </h2>
                    <div class="grid sm:grid-cols-2 gap-4">
                        <div class="p-5 bg-gray-700/30 rounded-2xl hover:bg-gray-700/40 transition-colors">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-400">订单号</p>
                                    <p class="text-white font-medium mt-1">{{ orderData.orderNo }}</p>
                                </div>
                                <svg class="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                                </svg>
                            </div>
                        </div>
                        <div class="p-5 bg-gray-700/30 rounded-2xl hover:bg-gray-700/40 transition-colors">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-400">下单时间</p>
                                    <p class="text-white font-medium mt-1">{{ formatDate(orderData.orderTime) }}</p>
                                </div>
                                <svg class="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                        </div>
                        <div class="p-5 bg-gray-700/30 rounded-2xl hover:bg-gray-700/40 transition-colors">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-400">姓名</p>
                                    <p class="text-white font-medium mt-1">{{ orderData.name }}</p>
                                </div>
                                <svg class="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                            </div>
                        </div>
                        <div class="p-5 bg-gray-700/30 rounded-2xl hover:bg-gray-700/40 transition-colors">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-400">手机号</p>
                                    <p class="text-white font-medium mt-1">{{ orderData.phone }}</p>
                                </div>
                                <svg class="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                </svg>
                            </div>
                        </div>
                        <div class="sm:col-span-2 p-5 bg-gray-700/30 rounded-2xl hover:bg-gray-700/40 transition-colors">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-400">总容量</p>
                                    <p class="text-white font-medium mt-1">{{ orderData.totalSize }}GB</p>
                                </div>
                                <div class="flex items-center text-emerald-400 bg-emerald-400/10 px-4 py-2 rounded-xl">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span class="font-medium">已确认</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 游戏列表卡片 -->
                <div class="bg-gray-800/60 rounded-3xl p-6 sm:p-8 shadow-lg border border-gray-700/50 hover:border-gray-600/50 transition-colors">
                    <h2 class="text-xl sm:text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-6 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-3 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z" />
                        </svg>
                        游戏列表
                        <span class="ml-2 text-sm font-normal text-gray-400">({{ orderData.goodsDetails.length }}个)</span>
                    </h2>
                    <div class="grid sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div v-for="game in orderData.goodsDetails" :key="game.id" class="group p-5 bg-gray-700/30 rounded-2xl border border-gray-600/30 hover:bg-gray-700/40 hover:border-gray-500/50 transition-all duration-300">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <h3 class="text-base sm:text-lg font-medium text-white line-clamp-2 group-hover:text-green-400 transition-colors">{{ game.name }}</h3>
                                    <div class="mt-3 flex items-center">
                                        <span :class="[
                                            'inline-flex items-center px-3 py-1 rounded-full text-xs font-medium',
                                            game.isPreset ? 'bg-green-400/10 text-green-400' : 'bg-blue-400/10 text-blue-400'
                                        ]">
                                            <svg xmlns="http://www.w3.org/2000/svg" :class="['h-3.5 w-3.5 mr-1']" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            {{ game.isPreset ? '预设游戏' : '自选游戏' }}
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <span class="text-sm font-medium px-3 py-1.5 bg-gray-800/70 rounded-full text-gray-300 group-hover:bg-gray-800 transition-colors">
                                        {{ formatSize(game.size) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 加载状态 -->
            <div v-else class="flex items-center justify-center h-64">
                <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.bg-clip-text {
    -webkit-background-clip: text;
    background-clip: text;
}

/* 卡片悬停效果 */
@media (min-width: 640px) {
    .group:hover {
        transform: translateY(-2px);
    }
}
</style> 