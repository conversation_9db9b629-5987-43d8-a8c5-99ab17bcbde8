import { Provide } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { ProductInfoEntity } from '../entity/info';

/**
 * 产品信息
 */
@Provide()
export class ProductInfoService extends BaseService {
  @InjectEntityModel(ProductInfoEntity)
  productInfoEntity: Repository<ProductInfoEntity>;
}
