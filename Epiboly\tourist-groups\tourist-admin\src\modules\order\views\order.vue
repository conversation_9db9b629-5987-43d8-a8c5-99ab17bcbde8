<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 删除按钮 -->
			<!-- <cl-multi-delete-btn /> -->
			<cl-flex1 />
			<!-- 条件搜索 -->
			<cl-search ref="Search" />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 多团详情弹窗 -->
		<cl-dialog v-model="groupDialog.visible" title="产品拼团详情" width="1200px">
			<div v-if="groupDialog.loading" class="text-center">
				<el-icon class="is-loading"><Loading /></el-icon>
				<span style="margin-left: 8px;">加载中...</span>
			</div>
			<div v-else-if="groupDialog.data">
				<div class="multi-group-info">
					<div class="product-info">
						<h3>{{ groupDialog.data.product?.name }}</h3>
						<p class="product-subtitle">{{ groupDialog.data.product?.subtitle }}</p>
					</div>
					
					<div class="groups-overview">
						<el-row :gutter="20" style="margin-bottom: 20px;">
							<el-col :span="6">
								<div class="stat-item">
									<div class="stat-value">{{ groupDialog.data.groups?.length || 0 }}</div>
									<div class="stat-label">拼团数量</div>
								</div>
							</el-col>
							<el-col :span="6">
								<div class="stat-item">
									<div class="stat-value">{{ getCompletedGroupsCount() }}</div>
									<div class="stat-label">已成团</div>
								</div>
							</el-col>
							<el-col :span="6">
								<div class="stat-item">
									<div class="stat-value">{{ getOngoingGroupsCount() }}</div>
									<div class="stat-label">拼团中</div>
								</div>
							</el-col>
							<el-col :span="6">
								<div class="stat-item">
									<div class="stat-value">{{ getTotalMembers() }}</div>
									<div class="stat-label">总参团人数</div>
								</div>
							</el-col>
						</el-row>
					</div>

					<div class="groups-list" v-if="groupDialog.data.groups?.length > 0">
						<h4>团列表</h4>
						<div v-for="(group, index) in groupDialog.data.groups" :key="group.id" class="group-item">
							<div class="group-header">
								<div class="group-title">
									<span class="group-number">{{ group.groupNumber }}</span>
									<el-tag :type="getGroupStatusType(group.groupStatus)" size="small">
										{{ getGroupStatusText(group.groupStatus) }}
									</el-tag>
								</div>
								<div class="group-stats-mini">
									<span>{{ group.currentSize }}/{{ group.maxSize }}人</span>
									<span class="remaining" v-if="group.remainingSize > 0">
										还需{{ group.remainingSize }}人
									</span>
								</div>
							</div>
							
							<div class="group-members" v-if="group.members?.length > 0">
								<el-table :data="group.members" size="small" border >
									<el-table-column prop="orderNumber" label="订单号"  align="center"/>
									<el-table-column prop="contactName" label="联系人"  align="center"/>
									<el-table-column prop="contactPhone" label="联系电话"  align="center"/>
									<el-table-column prop="quantity" label="人数"  align="center"/>
									<el-table-column prop="createTime" label="加入时间"  align="center">
										<template #default="{ row }">
											{{ formatDate(row.createTime) }}
										</template>
									</el-table-column>
									<el-table-column prop="avatar" label="头像"  align="center">
										<template #default="{ row }">
											<el-avatar :src="row.avatar" size="small" />
										</template>
									</el-table-column>
								</el-table>
							</div>
							<div v-else class="empty-group">
								<el-empty description="暂无成员" :image-size="60" />
							</div>
						</div>
					</div>
					<div v-else class="no-groups">
						<el-empty description="暂无拼团信息" />
					</div>
				</div>
			</div>
			<template #footer>
				<el-button @click="groupDialog.visible = false">关闭</el-button>
			</template>
		</cl-dialog>

		<!-- 预定订单详情弹窗 -->
		<cl-dialog v-model="orderDetailDialog.visible" title="预定订单详情" width="800px">
			<div v-if="orderDetailDialog.data" class="order-detail">
				<el-descriptions :column="2" border>
					<el-descriptions-item label="订单号">
						{{ orderDetailDialog.data.orderNumber }}
					</el-descriptions-item>
					<el-descriptions-item label="订单类型">
						<el-tag :type="getOrderTypeTag(orderDetailDialog.data.orderType)">
							{{ getOrderTypeText(orderDetailDialog.data.orderType) }}
						</el-tag>
					</el-descriptions-item>
					<el-descriptions-item label="产品名称">
						{{ orderDetailDialog.data.productName }}
					</el-descriptions-item>
					<el-descriptions-item label="产品价格">
						¥{{ orderDetailDialog.data.productPrice }}
					</el-descriptions-item>
					<el-descriptions-item label="产品封面" :span="2">
						<el-image
							v-if="orderDetailDialog.data.productCover"
							:src="orderDetailDialog.data.productCover"
							style="width: 100px; height: 100px;"
							fit="cover"
						/>
						<span v-else>暂无图片</span>
					</el-descriptions-item>
					<el-descriptions-item label="联系人">
						{{ orderDetailDialog.data.contactName }}
					</el-descriptions-item>
					<el-descriptions-item label="联系电话">
						{{ orderDetailDialog.data.contactPhone }}
					</el-descriptions-item>
					<el-descriptions-item label="联系邮箱" :span="2">
						{{ orderDetailDialog.data.contactEmail }}
					</el-descriptions-item>
					<el-descriptions-item label="预订日期">
						{{ formatDate(orderDetailDialog.data.bookingDate) }}
					</el-descriptions-item>
					<el-descriptions-item label="订单状态">
						<el-tag :type="getOrderStatusTag(orderDetailDialog.data.orderStatus)">
							{{ getOrderStatusText(orderDetailDialog.data.orderStatus) }}
						</el-tag>
					</el-descriptions-item>
					<el-descriptions-item label="用户确认状态">
						<el-tag :type="orderDetailDialog.data.userConfirmed ? 'success' : 'warning'">
							{{ orderDetailDialog.data.userConfirmed ? '已确认' : '未确认' }}
						</el-tag>
					</el-descriptions-item>
					<el-descriptions-item label="创建时间">
						{{ formatDate(orderDetailDialog.data.createTime) }}
					</el-descriptions-item>
					<el-descriptions-item label="更新时间">
						{{ formatDate(orderDetailDialog.data.updateTime) }}
					</el-descriptions-item>
					<el-descriptions-item label="备注" :span="2">
						{{ orderDetailDialog.data.remarks || '无' }}
					</el-descriptions-item>
				</el-descriptions>
			</div>
			<template #footer>
				<el-button @click="orderDetailDialog.visible = false">关闭</el-button>
			</template>
		</cl-dialog>
	</cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
	name: "order-order",
});

import { useCrud, useTable, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useI18n } from "vue-i18n";
import { reactive, ref } from "vue";
import { ElMessage } from "element-plus";
import { Loading } from "@element-plus/icons-vue";

const { service } = useCool();
const { t } = useI18n();

// 拼团详情弹窗
const groupDialog = reactive({
	visible: false,
	loading: false,
	data: null as any
});

// 预定订单详情弹窗
const orderDetailDialog = reactive({
	visible: false,
	data: null as any
});

// 选项
const options = reactive({
	orderType: [
		{ label: t("预定订单"), value: 0, type: "danger" },
		{ label: t("散拼团"), value: 1, type: "success" },
	],
	orderStatus: [
		{ label: t("已预定"), value: 0 },
		{ label: t("已入团"), value: 1 },
		{ label: t("已失效"), value: 2 },
	],
});

// cl-table
const Table = useTable({
	columns: [
		{ type: "selection" },
		{ label: t("订单号"), prop: "orderNumber", minWidth: 140 },
		{ label: t("产品名称"), prop: "productName", minWidth: 200 },
		{ label: t("产品封面"), prop: "productCover", minWidth: 120, component: { name: "cl-image", props: { size: 60 } } },
		{ label: t("产品价格"), prop: "productPrice", minWidth: 100 },
		{ label: t("联系人"), prop: "contactName", minWidth: 100 },
		{ label: t("联系电话"), prop: "contactPhone", minWidth: 120 },
		{ label: t("联系邮箱"), prop: "contactEmail", minWidth: 120 },
		{ label: t("预订日期"), prop: "bookingDate", minWidth: 170, component: { name: "cl-date-text" } },
		{ label: t("备注"), prop: "remarks", minWidth: 150, showOverflowTooltip: true },
		{
			label: t("订单类型"),
			prop: "orderType",
			minWidth: 120,
			dict: options.orderType,
		},
		{
			label: t("订单状态"),
			prop: "orderStatus",
			minWidth: 120,
			dict: options.orderStatus,
		},
		{
			label: t("用户确认状态"),
			prop: "userConfirmed",
			minWidth: 100,
			component: { name: "cl-switch" },
		},
		{
			label: t("创建时间"),
			prop: "createTime",
			minWidth: 170,
			sortable: "desc",
			component: { name: "cl-date-text" },
		},
		{
			label: t("更新时间"),
			prop: "updateTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{ 
			type: "op", 
			buttons({ scope }: any) {
				const buttons: any[] = [];
				if (scope.row.orderType === 1) {
					buttons.push({
						label: "拼团详情",
						type: "primary",
						size: "small",
						onClick() {
							showGroupInfo(scope.row.productId);
						}
					});
				} else if (scope.row.orderType === 0) {
					buttons.push({
						label: "订单详情",
						type: "success",
						size: "small",
						onClick() {
							showOrderDetail(scope.row);
						}
					});
				}
				return buttons;
			}
		},
	],
});

// cl-search
const Search = useSearch();

// cl-crud
const Crud = useCrud(
	{
		service: service.order.order,
	},
	(app) => {
		app.refresh();
	},
);

// 显示拼团详情
async function showGroupInfo(productId: number) {
	groupDialog.visible = true;
	groupDialog.loading = true;
	groupDialog.data = null;

	try {
		const res = await service.order.order.request({
			url: `/group-info/${productId}`,
			method: "GET"
		});

		if (res) {
			groupDialog.data = res;
		}
		
	} catch (error) {
		console.error("获取拼团详情失败:", error);
		ElMessage.error("获取拼团详情失败");
	} finally {
		groupDialog.loading = false;
	}
}

// 显示订单详情
function showOrderDetail(orderData: any) {
	orderDetailDialog.data = orderData;
	orderDetailDialog.visible = true;
}

// 计算拼团总人数
function calculateTotalMembers(members: any[]) {
	if (!members || members.length === 0) return 0;
	return members.reduce((total, member) => total + (member.quantity || 0), 0);
}

// 格式化日期
function formatDate(date: string | Date) {
	if (!date) return '';
	const d = new Date(date);
	return d.toLocaleString('zh-CN');
}

// 获取已成团数量
function getCompletedGroupsCount() {
	if (!groupDialog.data?.groups) return 0;
	return groupDialog.data.groups.filter((group: any) => group.groupStatus === 1).length;
}

// 获取拼团中数量
function getOngoingGroupsCount() {
	if (!groupDialog.data?.groups) return 0;
	return groupDialog.data.groups.filter((group: any) => group.groupStatus === 0).length;
}

// 获取总参团人数
function getTotalMembers() {
	if (!groupDialog.data?.groups) return 0;
	return groupDialog.data.groups.reduce((total: number, group: any) => {
		return total + (group.currentSize || 0);
	}, 0);
}

// 获取团状态类型
function getGroupStatusType(status: number) {
	switch (status) {
		case 0: return 'warning'; // 组团中
		case 1: return 'success'; // 已成团
		case 2: return 'danger';  // 已失效
		default: return 'info';
	}
}

// 获取团状态文本
function getGroupStatusText(status: number) {
	switch (status) {
		case 0: return '组团中';
		case 1: return '已成团';
		case 2: return '已失效';
		default: return '未知';
	}
}

// 获取订单类型标签样式
function getOrderTypeTag(type: number) {
	switch (type) {
		case 0: return 'danger'; // 预定订单
		case 1: return 'success'; // 散拼团
		default: return 'info';
	}
}

// 获取订单类型文本
function getOrderTypeText(type: number) {
	switch (type) {
		case 0: return '预定订单';
		case 1: return '散拼团';
		default: return '未知';
	}
}

// 获取订单状态标签样式
function getOrderStatusTag(status: number) {
	switch (status) {
		case 0: return 'warning'; // 已预定
		case 1: return 'success'; // 已入团
		case 2: return 'danger';  // 已失效
		default: return 'info';
	}
}

// 获取订单状态文本
function getOrderStatusText(status: number) {
	switch (status) {
		case 0: return '已预定';
		case 1: return '已入团';
		case 2: return '已失效';
		default: return '未知';
	}
}

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}
</script>

<style scoped>
.group-info {
	padding: 20px 0;
}

.group-info h3 {
	margin: 0 0 20px 0;
	color: #303133;
	font-size: 18px;
}

.group-stats {
	margin-bottom: 20px;
}

.stat-item {
	text-align: center;
	padding: 20px;
	background: #f8f9fa;
	border-radius: 8px;
}

.stat-value {
	font-size: 24px;
	font-weight: bold;
	color: #409eff;
	margin-bottom: 8px;
}

.stat-label {
	color: #606266;
	font-size: 14px;
}

.group-status {
	margin-bottom: 20px;
	text-align: center;
}

.members-list h4 {
	margin: 20px 0 10px 0;
	color: #303133;
	font-size: 16px;
}

.no-members {
	text-align: center;
	padding: 40px 0;
}

.multi-group-info {
	padding: 20px 0;
}

.product-info {
	margin-bottom: 20px;
	padding-bottom: 20px;
	border-bottom: 1px solid #ebeef5;
}

.product-info h3 {
	margin: 0 0 8px 0;
	color: #303133;
	font-size: 20px;
}

.product-subtitle {
	color: #606266;
	margin: 0;
	font-size: 14px;
}

.groups-overview {
	margin-bottom: 30px;
}

.groups-list {
	max-height: 500px;
	overflow-y: auto;
}

.groups-list h4 {
	margin: 0 0 20px 0;
	color: #303133;
	font-size: 16px;
}

.group-item {
	margin-bottom: 20px;
	border: 1px solid #ebeef5;
	border-radius: 8px;
	overflow: hidden;
}

.group-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px 20px;
	background: #f8f9fa;
	border-bottom: 1px solid #ebeef5;
}

.group-title {
	display: flex;
	align-items: center;
	gap: 10px;
}

.group-number {
	font-weight: 600;
	color: #303133;
	font-size: 16px;
}

.group-stats-mini {
	display: flex;
	align-items: center;
	gap: 10px;
	color: #606266;
	font-size: 14px;
}

.group-stats-mini .remaining {
	color: #e6a23c;
	font-weight: 500;
}

.group-members {
	padding: 20px;
}

.empty-group {
	padding: 20px;
	text-align: center;
}

.no-groups {
	text-align: center;
	padding: 40px 0;
}

.order-detail {
	padding: 20px 0;
}
</style>
