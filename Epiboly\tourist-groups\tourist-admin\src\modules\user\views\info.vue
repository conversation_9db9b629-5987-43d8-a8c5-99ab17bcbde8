<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />

			<cl-flex1 />
			<!-- 条件搜索 -->
			<cl-search ref="Search" />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
	</cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
	name: "user-info",
});

import { useCrud, useTable, useUpsert, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useI18n } from "vue-i18n";
import { reactive } from "vue";

const { service } = useCool();
const { t } = useI18n();

// 选项
const options = reactive({
	gender: [
		{ label: t("未知"), value: 0 },
		{ label: t("男"), value: 1 },
		{ label: t("女"), value: 2 },
	],
	status: [
		{ label: t("禁用"), value: 0 },
		{ label: t("正常"), value: 1 },
		{ label: t("已注销"), value: 2 },
	],
	loginType: [
		{ label: t("小程序"), value: 0 },
		{ label: t("公众号"), value: 1 },
		{ label: t("H5"), value: 2 },
	],
});

// cl-upsert
const Upsert = useUpsert({
	items: [
		{
			label: t("登录唯一ID"),
			prop: "unionid",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("头像"),
			prop: "avatarUrl",
			component: { name: "cl-upload" },
		},
		{
			label: t("昵称"),
			prop: "nickName",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("手机号"),
			prop: "phone",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("性别"),
			prop: "gender",
			component: { name: "el-radio-group", options: options.gender },
			value: 0,
			required: true,
		},
		{
			label: t("状态"),
			prop: "status",
			component: { name: "el-radio-group", options: options.status },
			value: 1,
			required: true,
		},
		{
			label: t("登录方式"),
			prop: "loginType",
			component: { name: "el-radio-group", options: options.loginType },
			value: 0,
			required: true,
		},
		{
			label: t("密码"),
			prop: "password",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("介绍"),
			prop: "description",
			component: {
				name: "el-input",
				props: { type: "textarea", rows: 4 },
			},
		},
	],
});

// cl-table
const Table = useTable({
	columns: [
		{ label: t("#"), type: "index" },
		{ label: t("登录唯一ID"), prop: "unionid", minWidth: 140 },
		{
			label: t("头像"),
			prop: "avatarUrl",
			minWidth: 100,
			component: { name: "cl-image", props: { size: 60 } },
		},
		{ label: t("昵称"), prop: "nickName", minWidth: 140 },
		{ label: t("手机号"), prop: "phone", minWidth: 140 },
		{
			label: t("性别"),
			prop: "gender",
			minWidth: 120,
			dict: options.gender,
		},
		{
			label: t("状态"),
			prop: "status",
			minWidth: 120,
			dict: options.status,
		},
		{
			label: t("登录方式"),
			prop: "loginType",
			minWidth: 120,
			dict: options.loginType,
		},
		{ label: t("密码"), prop: "password", minWidth: 140 },
		{
			label: t("介绍"),
			prop: "description",
			showOverflowTooltip: true,
			minWidth: 200,
		},
		{
			label: t("创建时间"),
			prop: "createTime",
			minWidth: 170,
			sortable: "desc",
			component: { name: "cl-date-text" },
		},
		{
			label: t("更新时间"),
			prop: "updateTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
	],
});

// cl-search
const Search = useSearch();

// cl-crud
const Crud = useCrud(
	{
		service: service.user.info,
	},
	(app) => {
		app.refresh();
	},
);

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}
</script>
