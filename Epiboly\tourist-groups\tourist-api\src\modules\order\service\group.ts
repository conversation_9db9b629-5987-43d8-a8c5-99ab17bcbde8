import { Provide, Inject } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { OrderGroupEntity } from '../entity/group';
import { OrderOrderEntity } from '../entity/order';
import { ProductProductEntity } from '../../product/entity/product';
import { UserInfoService } from '../../user/service/info';
import { OrderOrderService } from './order';
import * as moment from 'moment';

/**
 * 拼团管理服务
 */
@Provide()
export class OrderGroupService extends BaseService {
    @InjectEntityModel(OrderGroupEntity)
    orderGroupEntity: Repository<OrderGroupEntity>;

    @InjectEntityModel(OrderOrderEntity)
    orderOrderEntity: Repository<OrderOrderEntity>;

    @InjectEntityModel(ProductProductEntity)
    productProductEntity: Repository<ProductProductEntity>;

    @Inject()
    userInfoService: UserInfoService;

    @Inject()
    orderOrderService: OrderOrderService;

    /**
     * 生成团号
     */
    async generateGroupNumber() {
        const timestamp = moment().format('YYYYMMDDHHmmss');
        const random = Math.random().toString().slice(-4);
        return 'G' + timestamp + random;
    }

    /**
     * 获取产品的可用团列表
     */
    async getAvailableGroups(productId: number) {
        const product = await this.productProductEntity.findOne({
            where: { id: productId }
        });

        if (!product) {
            throw new Error('产品不存在');
        }

        // 查询该产品下所有未过期的团
        const groups = await this.orderGroupEntity
            .createQueryBuilder('group')
            .leftJoinAndSelect('group.orders', 'orders')
            .leftJoinAndSelect('group.product', 'product')
            .where('group.productId = :productId', { productId })
            .andWhere('group.groupStatus IN (0, 1)') // 组团中或已成团
            .andWhere('group.groupEndTime > :now', { now: new Date() })
            .orderBy('group.createTime', 'ASC')
            .getMany();

        return {
            product,
            groups: await Promise.all(groups.map(async (group) => {
                const members = await this.getGroupMembers(group.id);
                return {
                    ...group,
                    members,
                    isAvailable: group.groupStatus === 0 && group.currentSize < group.maxSize
                };
            }))
        };
    }

    /**
     * 获取团成员信息
     */
    async getGroupMembers(groupId: number) {
        const orders = await this.orderOrderEntity
            .createQueryBuilder('order')
            .where('order.groupId = :groupId', { groupId })
            .andWhere('order.orderStatus = 1') // 已入团状态
            .getMany();

        return Promise.all(
            orders.map(async (order) => {
                let avatar = 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png';

                if (order.userId) {
                    try {
                        const userInfo = await this.userInfoService.person(order.userId);
                        if (userInfo && userInfo.avatarUrl) {
                            avatar = userInfo.avatarUrl;
                        }
                    } catch (error) {
                        console.error('获取用户头像失败:', error);
                    }
                }

                return {
                    id: order.id,
                    orderNumber: order.orderNumber,
                    contactName: order.contactName,
                    contactPhone: order.contactPhone,
                    quantity: order.quantity,
                    createTime: order.createTime,
                    avatar,
                    userId: order.userId
                };
            })
        );
    }

    /**
     * 找到或创建可用的团
     */
    async findOrCreateAvailableGroup(productId: number) {
        const product = await this.productProductEntity.findOne({
            where: { id: productId }
        });

        if (!product) {
            throw new Error('产品不存在');
        }

        // 查找是否有未满的团
        const availableGroup = await this.orderGroupEntity
            .createQueryBuilder('group')
            .where('group.productId = :productId', { productId })
            .andWhere('group.groupStatus = 0') // 组团中
            .andWhere('group.currentSize < group.maxSize') // 未满员
            .andWhere('group.groupEndTime > :now', { now: new Date() })
            .orderBy('group.createTime', 'ASC')
            .getOne();

        if (availableGroup) {
            return availableGroup;
        }

        // 如果没有可用的团，创建新团
        const groupData = {
            groupNumber: await this.generateGroupNumber(),
            productId,
            groupStatus: 0, // 组团中
            maxSize: product.groupSize || 5,
            currentSize: 0,
            remainingSize: product.groupSize || 5,
            groupStartTime: new Date(product.startDate),
            groupEndTime: new Date(product.endDate),
            isCompleted: false,
            remarks: `产品${product.name}的拼团`
        };

        return await this.orderGroupEntity.save(groupData);
    }

    /**
     * 加入团
     */
    async joinGroup(productId: number, userId?: number, orderData?: any) {
        // 获取产品信息
        const product = await this.productProductEntity.findOne({
            where: { id: productId }
        });

        if (!product) {
            throw new Error('产品不存在');
        }

        // 检查用户是否已经加入过任何团
        if (userId) {
            const existingOrder = await this.orderOrderEntity
                .createQueryBuilder('order')
                .leftJoin('order.group', 'group')
                .where('order.userId = :userId', { userId })
                .andWhere('group.productId = :productId', { productId })
                .andWhere('order.orderType = 1') // 散拼团订单
                .andWhere('order.orderStatus = 1') // 已入团状态
                .getOne();

            if (existingOrder) {
                throw new Error('您已经加入过这个产品的拼团，不能重复加入！');
            }
        }

        // 找到或创建可用的团
        const group = await this.findOrCreateAvailableGroup(productId);

        // 检查团是否还有空位
        if (group.currentSize >= group.maxSize) {
            throw new Error('当前团已满，正在为您分配新团...');
        }

        // 创建订单并关联到团
        const quantity = orderData?.quantity || 1;
        const newOrderData: any = {
            productId,
            groupId: group.id,
            orderType: 1, // 散拼团
            orderStatus: 1, // 已入团
            quantity,
            contactName: orderData?.contactName || '游客',
            contactPhone: orderData?.contactPhone || '13800000000',
            totalAmount: Number(product.price) * quantity, // 使用独立获取的product对象
            bookingDate: orderData?.bookingDate || new Date(),
            userConfirmed: true,
            userId: userId || null,
            remarks: orderData?.remarks || ''
        };

        // 生成订单号
        newOrderData.orderNumber = await this.orderOrderService.generateOrderNumber();

        const order = await this.orderOrderEntity.save(newOrderData);

        // 更新团的人数统计
        await this.updateGroupSize(group.id);

        return {
            success: true,
            groupId: group.id,
            groupNumber: group.groupNumber,
            orderId: order.id,
            orderNumber: order.orderNumber || ''
        };
    }

    /**
     * 更新团的人数统计
     */
    async updateGroupSize(groupId: number) {
        const group = await this.orderGroupEntity.findOne({
            where: { id: groupId }
        });

        if (!group) {
            return;
        }

        // 统计该团的总人数
        const result = await this.orderOrderEntity
            .createQueryBuilder('order')
            .select('SUM(order.quantity)', 'totalSize')
            .where('order.groupId = :groupId', { groupId })
            .andWhere('order.orderStatus = 1') // 已入团状态
            .getRawOne();

        const currentSize = parseInt(result.totalSize) || 0;
        const remainingSize = Math.max(0, group.maxSize - currentSize);
        const isCompleted = currentSize >= group.maxSize;

        await this.orderGroupEntity.update(groupId, {
            currentSize,
            remainingSize,
            isCompleted,
            groupStatus: isCompleted ? 1 : 0 // 1:已成团, 0:组团中
        });

        return { currentSize, remainingSize, isCompleted };
    }

    /**
     * 获取用户参与的团信息
     */
    async getUserGroupInfo(productId: number, userId?: number) {
        if (!userId) {
            return null;
        }

        const order = await this.orderOrderEntity
            .createQueryBuilder('order')
            .leftJoinAndSelect('order.group', 'group')
            .where('order.userId = :userId', { userId })
            .andWhere('order.productId = :productId', { productId })
            .andWhere('order.orderType = 1') // 散拼团订单
            .andWhere('order.orderStatus = 1') // 已入团状态
            .getOne();

        if (!order || !order.group) {
            return null;
        }

        const members = await this.getGroupMembers(order.group.id);

        return {
            group: order.group,
            members,
            userOrder: order
        };
    }
} 