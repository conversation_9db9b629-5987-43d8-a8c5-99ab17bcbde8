<script setup>
import { useRouter, useRoute } from 'vue-router';
import { User, SwitchButton } from '@element-plus/icons-vue';
import { ref, onMounted, onUnmounted } from 'vue';
import messageApi from '@/api/message';
import { getUserInfo } from '@/utils/auth';
import { eventBus, EVENTS } from '@/utils/eventBus';

const router = useRouter();
const route = useRoute();

// 登录状态
const isLoggedIn = ref(false);

// 未读消息数量
const unreadCount = ref(0);

// 定时器
let unreadCountTimer = null;

// 当前选择的语言
const currentLanguage = ref('chinese_simplified');

// 语言选项
const languageOptions = [
  { value: 'chinese_simplified', label: '中文' },
  { value: 'english', label: 'English' },
  { value: 'russian', label: 'Русский' },
];

// 定义导航菜单项
const navItems = [
  { title: '首页', path: '/home' },
  { title: '公司介绍', path: '/about' },
  { title: '业务范围', path: '/business' },
  { title: '合作协议', path: '/cooperation' },
  { title: '联系我们', path: '/contact' }
];

// 检查登录状态
const checkLoginStatus = () => {
  const userToken = localStorage.getItem('token');
  const isLogin = localStorage.getItem('isLogin');
  isLoggedIn.value = !!(userToken || isLogin);
};

// 获取未读消息数量
const getUnreadCount = async () => {
  try {
    const userInfo = getUserInfo();
    if (userInfo && userInfo.id) {
      const response = await messageApi.getUnreadCount(userInfo.id);
      if (response.code === 1000) {
        unreadCount.value = response.data || 0;
      }
    } else {
      unreadCount.value = 0;
    }
  } catch (error) {
    console.error('获取未读消息数量失败:', error);
    unreadCount.value = 0;
  }
};

// 开始定时获取未读消息数量
const startUnreadCountTimer = () => {
  // 立即获取一次
  getUnreadCount();

  // 每30秒更新一次
  unreadCountTimer = setInterval(() => {
    getUnreadCount();
  }, 30000);
};

// 停止定时器
const stopUnreadCountTimer = () => {
  if (unreadCountTimer) {
    clearInterval(unreadCountTimer);
    unreadCountTimer = null;
  }
};

// 监听未读数量变化事件
const handleUnreadCountChanged = () => {
  console.log('收到未读数量变化事件，立即更新');
  getUnreadCount();
};

// 跳转到个人中心
const goToUserCenter = (key) => {
  router.push('/user-center?type=' + key);
};

// 处理下拉菜单命令
const handleCommand = (command) => {
  if (command === 'profile') {
    goToUserCenter(0);
  } else if (command === 'logout') {
    logout();
  }
};

// 处理语言切换
const handleLanguageChange = (language) => {
  currentLanguage.value = language;
  if (window.translate && window.translate.changeLanguage) {
    window.translate.changeLanguage(language);
  }
};

// 退出登录
const logout = () => {
  // 清除本地存储的用户信息
  localStorage.removeItem('token');
  localStorage.removeItem('refreshToken');
  localStorage.removeItem('isLoggedIn');

  // 更新登录状态
  isLoggedIn.value = false;

  // 停止未读消息定时器
  stopUnreadCountTimer();
  unreadCount.value = 0;

  // 跳转到登录页面或首页
  router.push('/login');
};

// 组件挂载时检查登录状态
onMounted(() => {
  checkLoginStatus();

  // 如果已登录，开始获取未读消息数量
  if (isLoggedIn.value) {
    startUnreadCountTimer();
  }

  // 监听未读数量变化事件
  eventBus.on(EVENTS.UNREAD_COUNT_CHANGED, handleUnreadCountChanged);
});

// 组件卸载时清理定时器和事件监听
onUnmounted(() => {
  stopUnreadCountTimer();
  eventBus.off(EVENTS.UNREAD_COUNT_CHANGED, handleUnreadCountChanged);
});
</script>

<template>
  <header class="fixed top-0 left-0 right-0 z-50 bg-white/70 backdrop-blur-sm shadow-sm">
    <div class="container mx-auto px-4 py-3 flex items-center justify-between">
      <!-- Logo -->
      <div class="flex items-center">
        <img src="@/assets/logo.png" alt="Logo" class="logo-img mr-2" />
      </div>

      <!-- 导航菜单和右侧操作区 -->
      <div class="flex items-center space-x-8">
        <!-- 导航菜单 -->
        <nav class="hidden md:flex space-x-8">
          <a v-for="(item, index) in navItems" :key="index" :href="item.path"
            class="text-gray-700 hover:text-red-600 transition-colors duration-200"
            :class="{ 'text-red-600': route.path === item.path }">
            {{ item.title }}
          </a>
        </nav>

        <!-- 右侧操作区 -->
        <div class="flex items-center space-x-6">
          <!-- 语言选择下拉菜单 -->
          <el-dropdown @command="handleLanguageChange" trigger="hover" placement="bottom-end">
            <button class="text-gray-600 hover:text-red-600 transition-colors duration-200 focus:outline-none">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
              </svg>
            </button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-for="option in languageOptions" :key="option.value" :command="option.value"
                  :class="{ 'text-red-600': currentLanguage === option.value }">
                  {{ option.label }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <!-- 通知按钮 -->
          <div class="relative h-[20px]">
            <button class="text-gray-600 hover:text-red-600 focus:outline-none" @click="goToUserCenter(1)">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
              </svg>
            </button>
            <!-- 未读消息红点 -->
            <span v-if="unreadCount > 0"
              class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center text-[10px] font-bold">
              {{ unreadCount > 99 ? '99+' : unreadCount }}
            </span>
          </div>

          <!-- 用户中心下拉菜单 -->
          <el-dropdown @command="handleCommand" trigger="hover" placement="bottom-end">
            <button class="text-gray-600 hover:text-red-600 transition-colors duration-200 focus:outline-none">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon class="mr-2">
                    <User />
                  </el-icon>
                  个人中心
                </el-dropdown-item>
                <!-- 只有在已登录状态下才显示退出登录选项 -->
                <el-dropdown-item v-if="isLoggedIn" divided command="logout">
                  <el-icon class="mr-2">
                    <SwitchButton />
                  </el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
  </header>
</template>

<style scoped>
.logo-img {
  width: 90px;
  height: 53px;
  margin: 4px 0;
}
</style>