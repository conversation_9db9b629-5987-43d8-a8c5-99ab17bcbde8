import { Provide } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { CultureInfoEntity } from '../entity/info';

/**
 * 企业文化信息
 */
@Provide()
export class CultureInfoService extends BaseService {
  @InjectEntityModel(CultureInfoEntity)
  cultureInfoEntity: Repository<CultureInfoEntity>;
}
