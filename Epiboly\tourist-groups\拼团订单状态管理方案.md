## 测试接口

为了方便测试通知功能，提供了以下测试接口：

### 测试拼团成功通知
```bash
POST /admin/order/test-success-notification/{productId}
```

### 测试拼团失败通知  
```bash
POST /admin/order/test-failure-notification/{productId}
```

### 测试定时任务系统
```bash
# 手动执行测试定时任务
POST /admin/order/test-schedule-task

# 查询最近的测试定时任务数据
GET /admin/order/test-schedule-data

# 清理测试定时任务数据
POST /admin/order/clear-test-schedule-data
```

> **注意**: 测试定时任务每分钟会向 `message_info` 表添加一条测试数据，用于验证定时任务系统是否正常工作。测试完成后建议清理测试数据。 

## 推荐方案

建议使用**方案一（项目内置定时任务）**，因为：
1. 与项目深度集成，便于管理
2. 支持动态配置和监控
3. 已包含完整的通知功能
4. 统一的日志和错误处理

如果项目内置定时任务出现问题，可以临时使用**方案三（宝塔定时任务）**作为备选方案。

## 验证步骤

### 1. 启动服务后验证定时任务
重启 API 服务后，测试定时任务会自动每分钟运行一次，验证方式：

```bash
# 1. 等待1-2分钟后直接查询测试数据（无需手动执行）
curl -X GET "http://localhost:8001/admin/order/test-schedule-data" \
     -H "Authorization: Bearer YOUR_TOKEN"

# 2. 查看控制台日志
# 如果定时任务正常工作，每分钟都会在控制台看到类似日志：
# ✅ 定时任务测试成功 - 时间: 2024-01-15 14:30:00, ID: 123

# 3. 可选：手动执行一次测试任务（仅用于立即验证）
curl -X POST "http://localhost:8001/admin/order/test-schedule-task" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

> **自动运行**: 测试定时任务配置为每分钟自动执行，服务启动后会自动开始运行，无需人工干预。

### 2. 验证拼团订单状态更新
```bash
# 手动触发过期订单检查（可选）
curl -X POST "http://localhost:8001/admin/order/update-expired-orders" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN"

# 手动触发未成团订单检查（可选）
curl -X POST "http://localhost:8001/admin/order/update-unfulfilled-orders" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 验证通知功能
```bash
# 测试拼团成功通知（需要有产品ID）
curl -X POST "http://localhost:8001/admin/order/test-success-notification/1" \
     -H "Authorization: Bearer YOUR_TOKEN"

# 测试拼团失败通知（需要有产品ID）
curl -X POST "http://localhost:8001/admin/order/test-failure-notification/1" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

### 4. 清理测试数据
测试完成后，清理测试定时任务产生的数据：
```bash
curl -X POST "http://localhost:8001/admin/order/clear-test-schedule-data" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

### 5. 停用测试定时任务
验证完成后，可以在 `order/config.ts` 中将测试定时任务的 `enable` 设置为 `false`：
```typescript
{
  name: '定时任务测试',
  service: 'orderOrderService',
  method: 'testScheduleTask',
  cron: '0 * * * * *',
  immediate: false,
  enable: false // 设置为 false 停用测试任务
}
```