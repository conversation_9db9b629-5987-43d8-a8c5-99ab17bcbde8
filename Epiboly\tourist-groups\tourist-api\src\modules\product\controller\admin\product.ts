import { Inject, Post, Body } from '@midwayjs/core';
import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core';
import { ProductProductEntity } from '../../entity/product';
import { ProductProductService } from '../../service/product';

/**
 * 产品
 */
@CoolController({
  api: ['delete', 'list'], // 移除page，自定义实现
  entity: ProductProductEntity,
  service: ProductProductService,
  pageQueryOp: {
    keyWordLikeFields: ['a.name', 'a.productCode', 'a.subtitle', 'a.introduce'],
    fieldEq: ['a.status', 'a.productType', 'a.isHot', 'a.isPopular'],
    where: async ctx => {
      const { createTime, city, destination, priceRange, scoreRange, days } = ctx.request.body;
      const whereConditions = [];

      // 创建时间范围查询
      if (createTime && createTime.length === 2) {
        whereConditions.push([
          'a.createTime BETWEEN :start AND :end',
          { start: createTime[0], end: createTime[1] },
        ]);
      }

      // 出发城市查询（模糊匹配）
      if (city) {
        whereConditions.push([
          'a.city LIKE :city',
          { city: `%${city}%` },
        ]);
      }

      // 目的地查询（模糊匹配）
      if (destination) {
        whereConditions.push([
          'a.destination LIKE :destination',
          { destination: `%${destination}%` },
        ]);
      }

      // 价格范围查询
      if (priceRange && priceRange.length === 2) {
        const [minPrice, maxPrice] = priceRange;
        if (minPrice !== undefined && maxPrice !== undefined) {
          whereConditions.push([
            'a.price BETWEEN :minPrice AND :maxPrice',
            { minPrice, maxPrice },
          ]);
        } else if (minPrice !== undefined) {
          whereConditions.push([
            'a.price >= :minPrice',
            { minPrice },
          ]);
        } else if (maxPrice !== undefined) {
          whereConditions.push([
            'a.price <= :maxPrice',
            { maxPrice },
          ]);
        }
      }

      // 评分范围查询
      if (scoreRange && scoreRange.length === 2) {
        const [minScore, maxScore] = scoreRange;
        whereConditions.push([
          'a.score BETWEEN :minScore AND :maxScore',
          { minScore, maxScore },
        ]);
      }

      // 行程天数查询
      if (days) {
        whereConditions.push([
          'a.days = :days',
          { days },
        ]);
      }

      return whereConditions;
    },
  },
})
export class AdminProductProductController extends BaseController {
  @Inject()
  productProductService: ProductProductService;

  /**
   * 删除产品
   */
  @Post('/delete')
  async deleteProduct(@Body() body: any) {
    try {
      const { ids } = body;
      if (!ids || !Array.isArray(ids) || ids.length === 0) {
        return this.fail('请选择要删除的产品');
      }

      await this.productProductService.delete(ids);
      return this.ok('删除成功');
    } catch (error) {
      return this.fail(error.message);
    }
  }

  /**
   * 新增产品
   */
  @Post('/add')
  async addProduct(@Body() body: any) {
    try {
      const result = await this.productProductService.saveProduct(body);
      return this.ok(result);
    } catch (error) {
      return this.fail(error.message);
    }
  }

  /**
   * 更新产品
   */
  @Post('/update')
  async updateProduct(@Body() body: any) {
    try {
      const result = await this.productProductService.updateProduct(body);
      return this.ok(result);
    } catch (error) {
      return this.fail(error.message);
    }
  }

  /**
   * 获取单个产品信息（包含分类ID列表）
   */
  @Post('/info')
  async getProductInfo(@Body() body: any) {
    try {
      const { id } = body;
      if (!id) {
        return this.fail('产品ID不能为空');
      }

      // 获取产品基本信息
      const product = await this.productProductService.productProductEntity.findOne({
        where: { id }
      });

      if (!product) {
        return this.fail('产品不存在');
      }

      // 获取产品关联的分类ID列表
      const categories = await this.productProductService.productCategoryService.getByProductId(id);
      const categoryIds = categories.map(cat => cat.id);

      return this.ok({
        ...product,
        categoryIds
      });
    } catch (error) {
      return this.fail(error.message);
    }
  }

  /**
   * 自定义分页查询（包含分类信息）
   */
  @Post('/page')
  async getProductPage(@Body() body: any) {
    try {
      const { page = 1, size = 20, name, city, status, categoryId, productType } = body;

      // 构建基础查询条件
      let whereConditions = [];
      let queryParams: any[] = [];

      // 第一步：获取符合条件的产品ID（处理分类筛选）
      let productIdsQuery: string;

      if (categoryId) {
        // 如果有分类筛选，使用JOIN查询
        productIdsQuery = `
          SELECT DISTINCT p.id 
          FROM product_product p 
          LEFT JOIN product_product_category pc ON p.id = pc.productId 
          LEFT JOIN product_category c ON pc.categoryId = c.id 
          WHERE c.id = ?
        `;
        queryParams.push(categoryId);

        if (name) {
          productIdsQuery += ' AND p.name LIKE ?';
          queryParams.push(`%${name}%`);
        }
        if (city) {
          productIdsQuery += ' AND p.city = ?';
          queryParams.push(city);
        }
        if (productType !== undefined) {
          productIdsQuery += ' AND p.productType = ?';
          queryParams.push(productType);
        }
        if (status !== undefined) {
          productIdsQuery += ' AND p.status = ?';
          queryParams.push(status);
        }
      } else {
        // 如果没有分类筛选，直接查询产品表
        productIdsQuery = `SELECT p.id FROM product_product p`;

        let conditions = [];
        if (name) {
          conditions.push('p.name LIKE ?');
          queryParams.push(`%${name}%`);
        }
        if (city) {
          conditions.push('p.city = ?');
          queryParams.push(city);
        }
        if (productType !== undefined) {
          conditions.push('p.productType = ?');
          queryParams.push(productType);
        }
        if (status !== undefined) {
          conditions.push('p.status = ?');
          queryParams.push(status);
        }

        if (conditions.length > 0) {
          productIdsQuery += ' WHERE ' + conditions.join(' AND ');
        }
      }

      productIdsQuery += ' ORDER BY p.sort DESC, p.createTime DESC LIMIT ? OFFSET ?';
      queryParams.push(size, (page - 1) * size);

      // 执行查询获取ID列表
      const productIds = await this.productProductService.productProductEntity.query(
        productIdsQuery,
        queryParams
      );

      if (productIds.length === 0) {
        return this.ok({
          list: [],
          pagination: { page, size, total: 0 }
        });
      }

      const ids = productIds.map((item: any) => item.id);

      // 第二步：获取总数
      let countQuery: string;
      let countParams: any[] = [];

      if (categoryId) {
        countQuery = `
          SELECT COUNT(DISTINCT p.id) as total
          FROM product_product p 
          LEFT JOIN product_product_category pc ON p.id = pc.productId 
          LEFT JOIN product_category c ON pc.categoryId = c.id 
          WHERE c.id = ?
        `;
        countParams.push(categoryId);

        if (name) {
          countQuery += ' AND p.name LIKE ?';
          countParams.push(`%${name}%`);
        }
        if (city) {
          countQuery += ' AND p.city = ?';
          countParams.push(city);
        }
        if (productType !== undefined) {
          countQuery += ' AND p.productType = ?';
          countParams.push(productType);
        }
        if (status !== undefined) {
          countQuery += ' AND p.status = ?';
          countParams.push(status);
        }
      } else {
        countQuery = `SELECT COUNT(p.id) as total FROM product_product p`;

        let conditions = [];
        if (name) {
          conditions.push('p.name LIKE ?');
          countParams.push(`%${name}%`);
        }
        if (city) {
          conditions.push('p.city = ?');
          countParams.push(city);
        }
        if (productType !== undefined) {
          conditions.push('p.productType = ?');
          countParams.push(productType);
        }
        if (status !== undefined) {
          conditions.push('p.status = ?');
          countParams.push(status);
        }

        if (conditions.length > 0) {
          countQuery += ' WHERE ' + conditions.join(' AND ');
        }
      }

      const totalResult = await this.productProductService.productProductEntity.query(
        countQuery,
        countParams
      );
      const total = totalResult[0]?.total || 0;

      // 第三步：根据ID列表获取完整的产品信息（包含分类）
      const products = await this.productProductService.productProductEntity
        .createQueryBuilder('product')
        .leftJoinAndSelect('product.productCategories', 'pc')
        .leftJoinAndSelect('pc.category', 'category')
        .whereInIds(ids)
        .orderBy('product.sort', 'DESC')
        .addOrderBy('product.createTime', 'DESC')
        .getMany();

      // 处理分类信息
      const result = products.map(product => {
        const categoryNames = product.productCategories
          ?.map(pc => pc.category?.name)
          .filter(name => name) || [];

        return {
          ...product,
          categoryNames: categoryNames.join(', '),
          productCategories: undefined // 移除关联对象，只保留分类名称
        };
      });

      return this.ok({
        list: result,
        pagination: {
          page,
          size,
          total: parseInt(total.toString())
        }
      });
    } catch (error) {
      console.error('分页查询错误:', error);
      return this.fail(error.message);
    }
  }
}
