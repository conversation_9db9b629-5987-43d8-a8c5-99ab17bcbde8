import { Inject, Get, Query } from '@midwayjs/core';
import { Cool<PERSON><PERSON>roller, BaseController } from '@cool-midway/core';
import { AdvertSpaceEntity } from '../../entity/space';
import { AdvertSpaceService } from '../../service/space';

/**
 * 广告位app端
 */
@CoolController({
    prefix: '/app/advert',
    api: [],
    entity: AdvertSpaceEntity,
    service: AdvertSpaceService,
})
export class AppAdvertSpaceController extends BaseController {
    @Inject()
    advertSpaceService: AdvertSpaceService;

    /**
     * 获取广告位列表
     */
    @Get('/list', { summary: '获取广告位列表' })
    async list() {
        const data = await this.advertSpaceService.advertSpaceEntity.find();

        return this.ok(data);
    }
} 