#!/bin/bash

# 一键设置宝塔面板定时任务脚本
# 使用方法: bash setup_cron.sh

echo "🚀 开始设置订单模块定时任务..."

# 项目路径配置
PROJECT_PATH="/www/wwwroot/tourist-api"
SCRIPT_NAME="宝塔定时任务脚本.sh"
SCRIPT_PATH="$PROJECT_PATH/$SCRIPT_NAME"

# 检查项目路径是否存在
if [ ! -d "$PROJECT_PATH" ]; then
    echo "❌ 项目路径不存在: $PROJECT_PATH"
    echo "请修改 setup_cron.sh 中的 PROJECT_PATH 变量为正确的项目路径"
    exit 1
fi

# 检查脚本文件是否存在
if [ ! -f "$SCRIPT_PATH" ]; then
    echo "❌ 定时任务脚本不存在: $SCRIPT_PATH"
    echo "请先将 '$SCRIPT_NAME' 上传到项目目录"
    exit 1
fi

# 设置脚本执行权限
echo "📝 设置脚本执行权限..."
chmod +x "$SCRIPT_PATH"

if [ $? -eq 0 ]; then
    echo "✅ 脚本权限设置成功"
else
    echo "❌ 脚本权限设置失败"
    exit 1
fi

# 测试脚本是否可以正常执行
echo "🔍 测试脚本连接..."
$SCRIPT_PATH check >/dev/null 2>&1

# 测试数据库连接
echo "🔍 测试数据库连接..."
mysql -hlocalhost -P3306 -utour -pB8w4MzSibXzKnMb5 tour -e "SELECT 1;" >/dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 数据库连接正常"
else
    echo "❌ 数据库连接失败，请检查数据库配置"
    echo "请确认以下信息是否正确："
    echo "  数据库主机: localhost"
    echo "  数据库名称: tour"
    echo "  数据库用户: tour"
    echo "  数据库密码: B8w4MzSibXzKnMb5"
    exit 1
fi

# 创建日志目录
echo "📁 创建日志目录..."
mkdir -p /www/wwwlogs/tourist-api

# 输出宝塔面板配置信息
echo ""
echo "🎯 宝塔面板定时任务配置如下："
echo ""
echo "1️⃣ 【测试任务】(验证完成后可删除)"
echo "   任务名称: 订单模块-定时任务测试"
echo "   执行周期: 每分钟"
echo "   脚本内容: $SCRIPT_PATH test"
echo ""
echo "2️⃣ 【更新过期拼团订单】"
echo "   任务名称: 订单模块-更新过期拼团订单" 
echo "   执行周期: 每天 01:00"
echo "   脚本内容: $SCRIPT_PATH expired"
echo ""
echo "3️⃣ 【更新未成团过期订单】"
echo "   任务名称: 订单模块-更新未成团过期订单"
echo "   执行周期: 每天 01:30" 
echo "   脚本内容: $SCRIPT_PATH unfulfilled"
echo ""

# 询问是否执行测试
echo "🧪 是否现在执行测试验证？(y/n)"
read -r test_choice

if [ "$test_choice" = "y" ] || [ "$test_choice" = "Y" ]; then
    echo ""
    echo "🧪 开始执行测试..."
    
    # 执行测试任务
    echo "1. 测试定时任务..."
    $SCRIPT_PATH test
    
    echo ""
    echo "2. 测试过期订单更新..."
    $SCRIPT_PATH expired
    
    echo ""
    echo "3. 测试未成团订单更新..."
    $SCRIPT_PATH unfulfilled
    
    echo ""
    echo "📋 测试完成，查看测试结果："
    echo "   日志文件: tail -f /www/wwwlogs/tourist-api/cron.log"
    echo "   测试数据: SELECT * FROM message_info WHERE name = '定时任务测试' ORDER BY id DESC LIMIT 3;"
    echo "   通知记录: SELECT * FROM message_notification ORDER BY id DESC LIMIT 3;"
fi

echo ""
echo "📖 详细配置说明请查看: $PROJECT_PATH/宝塔定时任务配置说明.md"
echo ""
echo "✅ 定时任务设置完成！"
echo ""
echo "📌 下一步操作："
echo "   1. 登录宝塔面板 → 计划任务 → 添加任务"
echo "   2. 按照上述配置信息创建定时任务"
echo "   3. 验证任务是否正常运行"
echo "   4. 验证完成后禁用测试任务" 