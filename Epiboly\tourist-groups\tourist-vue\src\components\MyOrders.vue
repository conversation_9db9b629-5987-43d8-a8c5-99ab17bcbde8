<template>
  <div class="my-orders">
    <!-- 订单列表 -->
    <div class="orders-container">
      <!-- 表头 -->
      <div class="orders-header">
        <div class="header-item order-number">订单编号</div>
        <div class="header-item order-product">订单信息</div>
        <div class="header-item order-group">产品类型</div>
        <div class="header-item order-people">出行人数</div>
        <div class="header-item order-status">订单状态</div>
        <div class="header-item order-actions">操作</div>
      </div>

      <!-- 订单列表 -->
      <div v-if="filteredOrders.length > 0" class="orders-list">
        <div v-if="loading" class="loading-container">
          <el-loading text="正在加载订单列表..." />
        </div>
        <div v-else>
          <div v-for="order in paginatedOrders" :key="order.id" class="order-row">
            <div class="order-item order-number">
              {{ order.orderNumber }}
            </div>
            <div class="order-item order-product">
              <div class="product-info">
                <img :src="order.productImage" alt="产品图片" class="product-image" @error="handleImageError" />
                <div class="product-details">
                  <div class="product-title">{{ order.productTitle }}</div>
                  <div class="product-desc">{{ order.description }}</div>
                  <div class="contact-info">联系人：{{ order.contactPerson }}（{{ order.contactPhone }}）</div>
                </div>
              </div>
            </div>
            <div class="order-item order-group">
              {{ order.groupType }}
            </div>
            <div class="order-item order-people">
              {{ order.peopleCount }}
            </div>
            <div class="order-item order-status">
              <span class="status-tag" :class="order.status">{{ getStatusText(order.status) }}</span>
            </div>
            <div class="order-item order-actions">
              <el-button 
                v-if="!order.userConfirmed && order.status !== 'expired'" 
                type="danger" 
                size="small" 
                class="action-btn confirm-btn"
                @click="confirmOrder(order.id)">
                确认订单
              </el-button>
              <span v-else-if="order.userConfirmed" class="confirmed-text">已确认</span>
              <span v-else class="expired-text">已失效</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else-if="!loading" class="empty-state">
        <el-empty description="暂无拼团订单"></el-empty>
      </div>

      <!-- 加载状态 -->
      <div v-else class="loading-state">
        <el-loading text="正在加载订单列表..." />
      </div>

      <!-- 分页 -->
      <div v-if="filteredOrders.length > 0" class="pagination-container">
        <div class="custom-pagination">
          <button 
            class="pagination-btn prev-btn" 
            :disabled="currentPage === 1"
            @click="handlePageChange(currentPage - 1)">
            <span class="btn-icon">‹</span>
            <span class="btn-text">上一页</span>
          </button>
          
          <div class="page-numbers">
            <button 
              v-for="page in visiblePages" 
              :key="page"
              class="page-number"
              :class="{ active: page === currentPage }"
              @click="handlePageChange(page)">
              {{ page }}
            </button>
          </div>
          
          <button 
            class="pagination-btn next-btn"
            :disabled="currentPage === totalPages"
            @click="handlePageChange(currentPage + 1)">
            <span class="btn-text">下一页</span>
            <span class="btn-icon">›</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { orderApi } from '../api/order';

// 接收父组件传递的activeTab和orderType
const props = defineProps({
  activeTab: {
    type: String,
    default: 'all'
  },
  orderType: {
    type: Number,
    default: 1
  }
});

// 分页相关
const currentPage = ref(1);
const pageSize = ref(6);
const totalPages = ref(0);
const total = ref(0);

// 订单数据
const orders = ref([]);
const loading = ref(false);

// 获取我的订单列表
const loadMyOrders = async () => {
  loading.value = true;
  try {
    const response = await orderApi.getMyOrders(currentPage.value, pageSize.value, props.orderType);
    if (response.code === 1000) {
      orders.value = response.data.list || [];
      total.value = response.data.total || 0;
      totalPages.value = response.data.totalPages || 0;
    } else {
      ElMessage.error(response.message || '获取订单列表失败');
      orders.value = [];
    }
  } catch (error) {
    console.error('获取订单列表失败:', error);
    ElMessage.error('获取订单列表失败');
    orders.value = [];
  } finally {
    loading.value = false;
  }
};

// 过滤后的订单
const filteredOrders = computed(() => {
  if (props.activeTab === 'all') {
    return orders.value;
  } else if (props.activeTab === 'pending') {
    return orders.value.filter(order => order.status === 'completed');
  } else if (props.activeTab === 'expired') {
    return orders.value.filter(order => order.status === 'expired');
  }
  return orders.value;
});

// 分页后的订单（由于后端已经分页，直接使用filteredOrders）
const paginatedOrders = computed(() => {
  return filteredOrders.value;
});

// 计算可见的页码
const visiblePages = computed(() => {
  const total = totalPages.value;
  const current = currentPage.value;
  const pages = [];
  
  if (total <= 7) {
    // 如果总页数小于等于7，显示所有页码
    for (let i = 1; i <= total; i++) {
      pages.push(i);
    }
  } else {
    // 如果总页数大于7，显示部分页码
    if (current <= 4) {
      // 当前页在前面
      for (let i = 1; i <= 5; i++) {
        pages.push(i);
      }
    } else if (current >= total - 3) {
      // 当前页在后面
      for (let i = total - 4; i <= total; i++) {
        pages.push(i);
      }
    } else {
      // 当前页在中间
      for (let i = current - 2; i <= current + 2; i++) {
        pages.push(i);
      }
    }
  }
  
  return pages;
});

// 订单状态文字映射
const getStatusText = (status) => {
  const statusMap = {
    'pending': '已报名',
    'confirmed': '已入团',
    'completed': '已成团',
    'paid': '已付款',
    'cancelled': '已取消',
    'expired': '已失效'
  };
  return statusMap[status] || '未知状态';
};

// 处理分页变化
const handlePageChange = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
    loadMyOrders();
  }
};

// 确认订单
const confirmOrder = async (orderId) => {
  // 找到对应的订单
  const order = orders.value.find(o => o.id === orderId);
  if (!order) {
    ElMessage.error('找不到对应的订单');
    return;
  }

  try {
    const response = await orderApi.confirmOrder(order.orderNumber, order.contactPhone);
    if (response.code === 1000) {
      ElMessage.success('确认订单成功');
      // 更新本地订单状态
      const orderIndex = orders.value.findIndex(o => o.id === orderId);
      if (orderIndex !== -1) {
        orders.value[orderIndex].userConfirmed = true;
      }
    } else {
      ElMessage.error(response.message || '确认订单失败');
    }
  } catch (error) {
    console.error('确认订单失败:', error);
    ElMessage.error('确认订单失败');
  }
};

// 处理图片加载错误
const handleImageError = (event) => {
  event.target.src = 'https://via.placeholder.com/80x60/CCCCCC/FFFFFF?text=暂无图片';
};

// 监听activeTab和orderType变化，重新加载数据
watch(() => [props.activeTab, props.orderType], () => {
  currentPage.value = 1;
  loadMyOrders();
});

// 组件挂载时获取订单数据
onMounted(() => {
  loadMyOrders();
});
</script>

<style scoped>
.my-orders {
  background: transparent;
  border-radius: 0;
  overflow: visible;
}

/* 订单容器 */
.orders-container {
  padding: 0;
  background: transparent;
}

/* 表头样式 */
.orders-header {
  display: flex;
  background-color: #f2f2f2;
  border-bottom: 1px solid #c3c3c3;
  font-weight: bold;
  color: #333;
  border-radius: 8px 8px 0 0;
}

.header-item {
  padding: 15px 10px;
  text-align: center;
  font-size: 14px;
}

/* 订单列表 */
.orders-list {
  min-height: 400px;
  background-color: #f2f2f2;
  border-radius: 0 0 8px 8px;
}

.order-row {
  display: flex;
  border-bottom: 1px solid #c3c3c3;
  transition: background-color 0.2s ease;
  background-color: #f2f2f2;
}

.order-row:hover {
  background-color: #fafafa;
}

.order-item {
  padding: 20px 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

/* 列宽设置 */
.order-number {
  flex: 0 0 120px;
}

.order-product {
  flex: 1;
  min-width: 300px;
}

.order-group {
  flex: 0 0 100px;
}

.order-people {
  flex: 0 0 80px;
}

.order-status {
  flex: 0 0 100px;
}

.order-actions {
  flex: 0 0 120px;
}

/* 产品信息样式 */
.product-info {
  display: flex;
  align-items: center;
  text-align: left;
  width: 100%;
}

.product-image {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  margin-right: 15px;
  flex-shrink: 0;
}

.product-details {
  flex: 1;
  min-width: 0;
}

.product-title {
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
  font-size: 14px;
}

.product-desc {
  color: #666;
  font-size: 12px;
  margin-bottom: 3px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.contact-info {
  color: #999;
  font-size: 12px;
}

/* 状态标签 */
.status-tag {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
}

/* 操作按钮 */
.action-btn {
  font-size: 12px;
  padding: 5px 15px;
}

.confirm-btn {
  background-color: #c8161b !important;
  border-color: #c8161b !important;
  border-radius: 8px !important;
}

.confirmed-text {
  color: #67c23a;
  font-size: 12px;
  font-weight: bold;
}

.expired-text {
  color: #909399;
  font-size: 12px;
}

/* 空状态 */
.empty-state {
  padding: 60px 0;
  text-align: center;
  background-color: #f2f2f2;
  border-radius: 0 0 8px 8px;
}

/* 加载状态 */
.loading-state {
  padding: 60px 0;
  text-align: center;
  background-color: #f2f2f2;
  border-radius: 0 0 8px 8px;
}

.loading-container {
  padding: 40px 0;
  text-align: center;
}

/* 分页样式 */
.pagination-container {
  padding: 20px;
  text-align: center;
  border-top: 1px solid #f0f0f0;
  background-color: #f2f2f2;
  border-radius: 0 0 8px 8px;
}

.custom-pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.pagination-btn {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  height: 26px;
  min-width: 68px;
  background-color: #fff;
  border: 1px solid #BEBDBD;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #BEBDBD;
  transition: all 0.3s ease;
  justify-content: center;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #f5f5f5;
  border-color: #808080;
  color: #808080;
}

.pagination-btn:disabled {
  background-color: #f9f9f9;
  color: #BEBDBD;
  cursor: not-allowed;
  border-color: #BEBDBD;
}

.pagination-btn .btn-icon {
  font-size: 14px;
  font-weight: bold;
}

.pagination-btn .btn-text {
  margin: 0 2px;
}

.prev-btn .btn-icon {
  margin-right: 2px;
}

.next-btn .btn-icon {
  margin-left: 2px;
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 2px;
  margin: 0 8px;
}

.page-number {
  width: 26px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #BEBDBD;
  transition: all 0.3s ease;
}

.page-number:hover {
  background-color: #f5f5f5;
  border-color: #808080;
  color: #808080;
}

.page-number.active {
  border-color: #808080;
  color: #808080;
  font-weight: bold;
}

.page-number.active:hover {
  background-color: #f5f5f5;
  border-color: #808080;
  color: #808080;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .order-product {
    min-width: 250px;
  }
  
  .product-image {
    width: 60px;
    height: 45px;
  }
}

@media (max-width: 992px) {
  .orders-header,
  .order-row {
    flex-direction: column;
  }
  
  .header-item,
  .order-item {
    padding: 10px;
    text-align: left;
  }
  
  .order-item {
    border-bottom: 1px solid #f5f5f5;
  }
  
  .order-item:last-child {
    border-bottom: none;
  }
  
  .product-info {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .product-image {
    margin-bottom: 10px;
    margin-right: 0;
  }
}

@media (max-width: 768px) {
  .header-item,
  .order-item {
    padding: 8px;
    font-size: 12px;
  }
  
  .product-image {
    width: 50px;
    height: 38px;
  }
  
  .action-btn {
    font-size: 11px;
    padding: 4px 12px;
  }
  
  .custom-pagination {
    flex-wrap: wrap;
    gap: 2px;
  }
  
  .pagination-btn {
    padding: 3px 8px;
    font-size: 11px;
    height: 24px;
    min-width: 60px;
  }
  
  .pagination-btn .btn-text {
    display: none;
  }
  
  .page-numbers {
    margin: 0 4px;
  }
  
  .page-number {
    width: 24px;
    height: 24px;
    font-size: 11px;
  }
}
</style> 