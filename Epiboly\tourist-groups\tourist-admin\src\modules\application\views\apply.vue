<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<cl-flex1 />
			<!-- 条件搜索 -->
			<cl-search ref="Search" />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />

		<!-- 分配账号弹窗 -->
		<el-dialog 
			v-model="assignDialog.visible" 
			title="分配账号" 
			width="500px"
		>
			<el-form 
				ref="assignForm" 
				:model="assignDialog.form" 
				:rules="assignDialog.rules"
				label-width="100px"
			>
				<el-form-item label="账号" prop="username">
					<el-input 
						v-model="assignDialog.form.username" 
						placeholder="请输入账号" 
						clearable 
					/>
				</el-form-item>
				<el-form-item label="密码" prop="password">
					<el-input 
						v-model="assignDialog.form.password" 
						type="password" 
						placeholder="请输入密码" 
						clearable 
					/>
				</el-form-item>
			</el-form>
			<template #footer>
				<el-button @click="assignDialog.visible = false">取消</el-button>
				<el-button type="primary" @click="handleAssignAccount" :loading="assignDialog.loading">
					确定
				</el-button>
			</template>
		</el-dialog>
	</cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
	name: "application-apply",
});

import { useCrud, useTable, useUpsert, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useI18n } from "vue-i18n";
import { reactive, ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";

const { service } = useCool();
const { t } = useI18n();

// 选项
const options = reactive({
	companyType: [
		{ label: t("企业"), value: 0 },
		{ label: t("个体工商户"), value: 1 },
		{ label: t("个人"), value: 2 },
	],
	status: [
		{ label: t("未分配账号"), value: 0, type: "warning" },
		{ label: t("已分配账号"), value: 1, type: "success" },
	],
});

// 分配账号弹窗
const assignDialog = reactive({
	visible: false,
	loading: false,
	currentId: null,
	form: {
		username: '',
		password: ''
	},
	rules: {
		username: [
			{ required: true, message: '请输入账号', trigger: 'blur' }
		],
		password: [
			{ required: true, message: '请输入密码', trigger: 'blur' },
			{ min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
		]
	}
});

const assignForm = ref();

// cl-upsert
const Upsert = useUpsert({
	items: [
		{
			label: t("公司性质"),
			prop: "companyType",
			component: { name: "el-radio-group", options: options.companyType },
			value: 0,
			required: true,
		},
		{
			label: t("公司"),
			prop: "companyAddress",
			component: {
				name: "el-input",
				props: { type: "textarea", rows: 4 },
			},
		},
		{
			label: t("国家"),
			prop: "country",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("地区"),
			prop: "region",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("联系人"),
			prop: "contactPerson",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("联系电话"),
			prop: "contactPhone",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("电子邮箱"),
			prop: "email",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("WhatsApp账号"),
			prop: "whatsApp",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("营业执照"),
			prop: "businessLicense",
			component: { name: "cl-upload" },
		},
		{
			label: t("状态"),
			prop: "status",
			component: { name: "el-radio-group", options: options.status },
			value: 0,
		},
	],
});

// cl-table
const Table = useTable({
	columns: [
		{ type: "selection" },
		{
			label: t("公司性质"),
			prop: "companyType",
			minWidth: 120,
			dict: options.companyType,
		},
		{
			label: t("公司"),
			prop: "companyAddress",
			showOverflowTooltip: true,
			minWidth: 200,
		},
		{ label: t("国家"), prop: "country", minWidth: 120 },
		{ label: t("地区"), prop: "region", minWidth: 120 },
		{ label: t("联系人"), prop: "contactPerson", minWidth: 140 },
		{ label: t("联系电话"), prop: "contactPhone", minWidth: 140 },
		{ label: t("电子邮箱"), prop: "email", minWidth: 140 },
		{ label: t("WhatsApp账号"), prop: "whatsApp", minWidth: 140 },
		{
			label: t("营业执照"),
			prop: "businessLicense",
			minWidth: 100,
			component: { name: "cl-image", props: { size: 60 } },
		},
		{
			label: t("状态"),
			prop: "status",
			minWidth: 120,
			dict: options.status,
		},
		{
			label: t("创建时间"),
			prop: "createTime",
			minWidth: 170,
			sortable: "desc",
			component: { name: "cl-date-text" },
		},
		{
			label: t("更新时间"),
			prop: "updateTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{ 
			type: "op", 
			buttons: [
				"edit", 
				"delete",
				{
					label: "分配账号",
					type: "primary",
					onClick: (row: any) => {
						console.log(row.scope.row);
						if (row.status !== 1) {
							openAssignDialog(row.scope.row);
						}
					}
				}
			]
		},
	],
});

// cl-search
const Search = useSearch();

// cl-crud
const Crud = useCrud(
	{
		service: service.application.apply,
	},
	(app) => {
		app.refresh();
	},
);

// 打开分配账号弹窗
function openAssignDialog(row: any) {
	console.log('打开分配账号弹窗，行数据:', row);
	assignDialog.currentId = row.id;
	assignDialog.form.username = row.contactPhone || '';
	assignDialog.form.password = '';
	assignDialog.visible = true;
}

// 处理分配账号
async function handleAssignAccount() {
	try {
		await assignForm.value.validate();
		assignDialog.loading = true;
		
		const requestData = {
			id: assignDialog.currentId,
			username: assignDialog.form.username,
			password: assignDialog.form.password
		};
		console.log('发送分配账号请求:', requestData);
		
		await service.application.apply.request({
			url: '/assignAccount',
			method: 'POST',
			data: requestData
		});
		
		ElMessage.success('账号分配成功');
		assignDialog.visible = false;
		refresh();
	} catch (error: any) {
		console.error('分配账号错误:', error);
		ElMessage.error(error.message || '分配失败');
	} finally {
		assignDialog.loading = false;
	}
}

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}
</script>
