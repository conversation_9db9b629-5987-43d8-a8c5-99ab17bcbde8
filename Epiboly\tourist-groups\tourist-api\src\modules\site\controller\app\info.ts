import { Inject } from '@midwayjs/core';
import { Cool<PERSON><PERSON>roller, BaseController } from '@cool-midway/core';
import { SiteInfoEntity } from '../../entity/info';
import { SiteInfoService } from '../../service/info';
import { Get, Query } from '@midwayjs/core';
@CoolController({
    prefix: '/app/site',
    api: [],
    entity: SiteInfoEntity,
    service: SiteInfoService,
})

export class AppSiteInfoController extends BaseController {
    @Inject()
    siteInfoService: SiteInfoService;

    /**
     * 站点信息
     */
    @Get('/info', { summary: '站点信息' })
    async info() {
        const siteInfoList = await this.siteInfoService.siteInfoEntity.find({
            order: {
                createTime: 'DESC', // 按创建时间降序排序
            },
            take: 1, // 只返回一条记录
        });

        const siteInfo = siteInfoList.length > 0 ? siteInfoList[0] : null;

        return this.ok(siteInfo);
    }
}