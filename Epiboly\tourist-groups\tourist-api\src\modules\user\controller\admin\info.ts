import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core';
import { UserInfoEntity } from '../../entity/info';
import { Inject, Post, Body } from '@midwayjs/core';
import { UserInfoService } from '../../service/info';

/**
 * 用户信息
 */
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: UserInfoEntity,
  pageQueryOp: {
    fieldEq: ['a.status', 'a.gender', 'a.loginType'],
    keyWordLikeFields: ['a.nickName', 'a.phone'],
  },
})
export class AdminUserInfoController extends BaseController {
  @Inject()
  userInfoService: UserInfoService;

  /**
   * 获取用户关联的申请信息
   */
  @Post('/getApplyInfo')
  async getApplyInfo(@Body() body: { userId: number }) {
    const { userId } = body;
    return await this.userInfoService.getUserApplyInfo(userId);
  }

  /**
   * 修改用户密码
   */
  @Post('/updatePassword')
  async updatePassword(@Body() body: { id: number; password: string }) {
    const { id, password } = body;
    return await this.userInfoService.updateUserPassword(id, password);
  }

  /**
   * 更新用户状态
   */
  @Post('/updateStatus')
  async updateStatus(@Body() body: { id: number; status: number }) {
    const { id, status } = body;
    return await this.userInfoService.updateUserStatus(id, status);
  }

  /**
   * 更新用户基本信息
   */
  @Post('/updateBasicInfo')
  async updateBasicInfo(@Body() body: {
    id: number;
    nickName?: string;
    avatarUrl?: string;
    status?: number;
    gender?: number;
  }) {
    const { id, ...updateData } = body;
    return await this.userInfoService.updateBasicInfo(id, updateData);
  }
}
