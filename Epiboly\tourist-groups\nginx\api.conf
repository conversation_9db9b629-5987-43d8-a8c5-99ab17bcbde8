server {
    listen 8080;
    server_name 47.79.17.13;
    
    # 处理所有/app/下的请求
    location /app/ {
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE' always;
        add_header 'Access-Control-Allow-Headers' 'Origin, X-Requested-With, Content-Type, Accept, Authorization, *' always;
    
        # 对于OPTIONS请求特殊处理
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE' always;
            add_header 'Access-Control-Allow-Headers' 'Origin, X-Requested-With, Content-Type, Accept, Authorization, *' always;
            add_header 'Content-Type' 'text/plain' always;
            add_header 'Content-Length' '0' always;
            return 204;
        }
        # 代理配置
        proxy_pass http://127.0.0.1:8001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        
        # 日志记录，便于调试
        access_log /www/wwwlogs/47.79.17.13_8080.query.log;
    }
    
    
    root /www/wwwroot/47.79.17.13_8080;
    
    # 设置客户端请求体大小限制为200MB
    client_max_body_size 200m;
    
    #ERROR-PAGE-START  错误页配置，可以注释、删除或修改
    error_page 404 /404.html;
    #error_page 502 /502.html;
    #ERROR-PAGE-END

    #PHP-INFO-START  PHP引用配置，可以注释或修改
    include enable-php-00.conf;
    #PHP-INFO-END


    #禁止访问的文件或目录
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE|README.md)
    {
        return 404;
    }
    
    # 修改上传目录配置，禁止直接访问.exe文件
    location ^~ /upload/ {
        alias /www/wwwroot/47.79.17.13_8080/public/upload/;
        
        # 禁止执行PHP等脚本
        location ~ \.(php|jsp|py|js|lua|ts|go)$ {
            deny all;
        }
        
        # 允许访问其他静态资源
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
        add_header Access-Control-Allow-Headers "*";
        try_files $uri =404;

        # 增加超时设置，适合大文件下载
        proxy_connect_timeout 300;
        proxy_send_timeout 300;
        proxy_read_timeout 300;
        
        # 增加缓冲区设置
        proxy_buffer_size 64k;
        proxy_buffers 8 64k;
        
        proxy_pass http://127.0.0.1:8001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # 静态文件代理
    location / {
        try_files $uri $uri/ /index.html;
    }

    #一键申请SSL证书验证目录相关设置
    location ~ \.well-known{
        allow all;
    }

    #禁止在证书验证目录放入敏感文件
    if ( $uri ~ "^/\.well-known/.*\.(php|jsp|py|js|css|lua|ts|go|zip|tar\.gz|rar|7z|sql|bak)$" ) {
        return 403;
    }

    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$
    {
        expires      30d;
        error_log /dev/null;
        access_log /dev/null;
    }

    location ~ .*\.(js|css)?$
    {
        expires      12h;
        error_log /dev/null;
        access_log /dev/null;
    }
    
    # 日志配置
    access_log  /www/wwwlogs/47.79.17.13_8080.log;
    error_log  /www/wwwlogs/47.79.17.13_8080.log;
}

server {
    listen 8080;
    server_name api.haiyuetravel.cn;
    
    # 处理所有/app/下的请求
    location /app/ {
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE' always;
        add_header 'Access-Control-Allow-Headers' 'Origin, X-Requested-With, Content-Type, Accept, Authorization, *' always;
    
        # 对于OPTIONS请求特殊处理
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE' always;
            add_header 'Access-Control-Allow-Headers' 'Origin, X-Requested-With, Content-Type, Accept, Authorization, *' always;
            add_header 'Content-Type' 'text/plain' always;
            add_header 'Content-Length' '0' always;
            return 204;
        }
        # 代理配置
        proxy_pass http://127.0.0.1:8001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        
        # 日志记录，便于调试
        access_log /www/wwwlogs/47.79.17.13_8080.query.log;
    }
    
    
    root /www/wwwroot/47.79.17.13_8080;
    
    # 设置客户端请求体大小限制为200MB
    client_max_body_size 200m;
    
    #ERROR-PAGE-START  错误页配置，可以注释、删除或修改
    error_page 404 /404.html;
    #error_page 502 /502.html;
    #ERROR-PAGE-END

    #PHP-INFO-START  PHP引用配置，可以注释或修改
    include enable-php-00.conf;
    #PHP-INFO-END


    #禁止访问的文件或目录
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE|README.md)
    {
        return 404;
    }
    
    # 修改上传目录配置，禁止直接访问.exe文件
    location ^~ /upload/ {
        alias /www/wwwroot/47.79.17.13_8080/public/upload/;
        
        # 禁止执行PHP等脚本
        location ~ \.(php|jsp|py|js|lua|ts|go)$ {
            deny all;
        }
        
        # 允许访问其他静态资源
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
        add_header Access-Control-Allow-Headers "*";
        try_files $uri =404;

        # 增加超时设置，适合大文件下载
        proxy_connect_timeout 300;
        proxy_send_timeout 300;
        proxy_read_timeout 300;
        
        # 增加缓冲区设置
        proxy_buffer_size 64k;
        proxy_buffers 8 64k;
        
        proxy_pass http://127.0.0.1:8001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # 静态文件代理
    location / {
        try_files $uri $uri/ /index.html;
    }

    #一键申请SSL证书验证目录相关设置
    location ~ \.well-known{
        allow all;
    }

    #禁止在证书验证目录放入敏感文件
    if ( $uri ~ "^/\.well-known/.*\.(php|jsp|py|js|css|lua|ts|go|zip|tar\.gz|rar|7z|sql|bak)$" ) {
        return 403;
    }

    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$
    {
        expires      30d;
        error_log /dev/null;
        access_log /dev/null;
    }

    location ~ .*\.(js|css)?$
    {
        expires      12h;
        error_log /dev/null;
        access_log /dev/null;
    }
    
    # 日志配置
    access_log  /www/wwwlogs/47.79.17.13_8080.log;
    error_log  /www/wwwlogs/47.79.17.13_8080.log;
}
