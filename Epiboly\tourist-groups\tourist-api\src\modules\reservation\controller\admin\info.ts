import { Inject } from '@midwayjs/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BaseController } from '@cool-midway/core';
import { ReservationInfoEntity } from '../../entity/info';
import { ReservationInfoService } from '../../service/info';

/**
 * 预定信息
 */
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: ReservationInfoEntity,
  service: ReservationInfoService,
  pageQueryOp: {
    keyWordLikeFields: ['a.name'],
    select: ['a.*'],
  },
})
export class AdminReservationInfoController extends BaseController {
  @Inject()
  reservationInfoService: ReservationInfoService;
}
