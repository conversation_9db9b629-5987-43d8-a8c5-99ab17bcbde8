import { Provide, InjectClient } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { ApplicationApplyEntity } from '../entity/apply';
import { UserInfoEntity } from '../../user/entity/info';
import { CoolCommException } from '@cool-midway/core';
import { CachingFactory, MidwayCache } from '@midwayjs/cache-manager';
import * as md5 from 'md5';

/**
 * 申请信息
 */
@Provide()
export class ApplicationApplyService extends BaseService {
  @InjectEntityModel(ApplicationApplyEntity)
  applicationApplyEntity: Repository<ApplicationApplyEntity>;

  @InjectEntityModel(UserInfoEntity)
  userInfoEntity: Repository<UserInfoEntity>;

  @InjectClient(CachingFactory, 'default')
  midwayCache: MidwayCache;

  /**
 * 删除申请 - 重写删除方法以处理外键约束和token失效
 * @param ids 申请ID数组或逗号分隔的字符串
 */
  async delete(ids: any): Promise<void> {
    let idArr: number[];
    if (ids instanceof Array) {
      idArr = ids;
    } else {
      idArr = ids.split(',').map(id => parseInt(id));
    }

    // 查询每个申请记录，并删除相关联的用户账号
    for (const id of idArr) {
      const apply = await this.applicationApplyEntity.findOne({ where: { id } });
      if (apply) {
        const userIdsToInvalidate: number[] = [];

        // 如果该申请已分配账号，记录需要失效的用户ID
        if (apply.status === 1 && apply.userId) {
          userIdsToInvalidate.push(apply.userId);
        }

        // 查找通过 applyId 关联的用户账号
        const relatedUsers = await this.userInfoEntity.find({ where: { applyId: id } });
        if (relatedUsers.length > 0) {
          const relatedUserIds = relatedUsers.map(user => user.id);
          userIdsToInvalidate.push(...relatedUserIds);
        }

        // 先使所有相关用户的token失效
        for (const userId of userIdsToInvalidate) {
          await this.invalidateUserToken(userId);
        }

        // 然后删除用户账号记录
        if (apply.userId) {
          await this.userInfoEntity.delete({ id: apply.userId });
        }
        if (relatedUsers.length > 0) {
          const userIds = relatedUsers.map(user => user.id);
          await this.userInfoEntity.delete(userIds);
        }
      }
    }

    // 最后删除申请记录
    await this.applicationApplyEntity.delete(idArr);
  }

  /**
 * 使用户token失效
 * @param userId 用户ID
 */
  private async invalidateUserToken(userId: number): Promise<void> {
    try {
      // 清除管理员的token缓存（如果存在）
      await this.midwayCache.del(`admin:token:${userId}`);
      await this.midwayCache.del(`admin:token:refresh:${userId}`);
      await this.midwayCache.del(`admin:perms:${userId}`);
      await this.midwayCache.del(`admin:department:${userId}`);
      await this.midwayCache.del(`admin:passwordVersion:${userId}`);

      // 清除用户的token缓存（app端）
      await this.midwayCache.del(`user:token:${userId}`);
      await this.midwayCache.del(`user:token:refresh:${userId}`);

      // 设置用户禁用标记，强制用户重新登录
      await this.midwayCache.set(`user:disabled:${userId}`, true, 60 * 60 * 24); // 24小时后自动清除

      console.log(`已清除用户 ${userId} 的token缓存，强制用户重新登录`);
    } catch (error) {
      console.error(`清除用户 ${userId} token缓存失败:`, error);
    }
  }

  /**
   * 分配账号
   * @param id 申请ID
   * @param username 用户名
   * @param password 密码
   */
  async assignAccount(id: number, username: string, password: string) {
    // 检查申请是否存在
    const apply = await this.applicationApplyEntity.findOne({ where: { id } });
    if (!apply) {
      throw new CoolCommException('申请不存在');
    }

    // 检查是否已分配账号
    if (apply.status === 1) {
      throw new CoolCommException('该申请已分配账号');
    }

    // 检查用户名是否重复
    const existUser = await this.userInfoEntity.findOne({ where: { phone: username } });
    if (existUser) {
      throw new CoolCommException('该账号已存在，请更换');
    }

    // 创建用户
    const user = new UserInfoEntity();
    user.phone = username;
    user.password = md5(password); // 密码加密
    user.nickName = apply.contactPerson || username;
    user.status = 1; // 正常状态
    const savedUser = await this.userInfoEntity.save(user);

    // 更新申请状态
    await this.applicationApplyEntity.update(id, {
      status: 1, // 已分配账号
      userId: savedUser.id
    });

    // 更新用户关联申请信息
    await this.userInfoEntity.update(savedUser.id, {
      applyId: id
    });

    return { success: true, message: '账号分配成功' };
  }

  /**
   * 根据用户ID获取申请信息
   * @param userId 用户ID
   */
  async getApplyByUserId(userId: number) {
    return await this.applicationApplyEntity.findOne({ where: { userId } });
  }
}
