import { Inject } from '@midwayjs/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BaseController } from '@cool-midway/core';
import { CompanyInfoEntity } from '../../entity/info';
import { CompanyInfoService } from '../../service/info';

/**
 * 公司信息
 */
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: CompanyInfoEntity,
  service: CompanyInfoService,
  pageQueryOp: {
    keyWordLikeFields: ['a.founderName'],
    where: async ctx => {
      const { createTime } = ctx.request.body;
      return [
        createTime && [
          'a.createTime BETWEEN :start AND :end',
          { start: createTime[0], end: createTime[1] },
        ],
      ];
    },
  },
})
export class AdminCompanyInfoController extends BaseController {
  @Inject()
  companyInfoService: CompanyInfoService;
}
