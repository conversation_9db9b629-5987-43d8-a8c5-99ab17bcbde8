import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 通知消息
 */
@Entity('message_notification')
export class MessageNotificationEntity extends BaseEntity {
    @Column({ comment: '标题' })
    title: string;

    @Column({ comment: '内容', type: 'text' })
    content: string;

    @Index()
    @Column({ comment: '类型', dict: ['系统消息', '公告'], default: 0 })
    type: number;

    @Index()
    @Column({ comment: '接收者ID', nullable: true })
    receiverId: number;

    @Index()
    @Column({ comment: '发送者ID', nullable: true })
    senderId: number;

    @Column({ comment: '是否已读', default: false })
    isRead: boolean;

    @Index()
    @Column({ comment: '发布时间', nullable: true })
    publishTime: Date;

    @Column({ comment: '状态', dict: ['禁用', '启用'], default: 1 })
    status: number;

    @Column({ comment: '优先级', dict: ['普通', '重要', '紧急'], default: 0 })
    priority: number;

    @Column({ comment: '发送状态', dict: ['待发送', '已发送', '发送失败'], default: 1 })
    sendStatus: number;

    @Column({ comment: '发送给所有用户', default: false })
    sendToAll: boolean;

    @Column({ comment: '发送给管理员', default: false })
    sendToAdmin: boolean;

    @Column({ comment: '接收人数量', default: 0 })
    receiverCount: number;

    @Column({ comment: '发送人备注', nullable: true })
    senderRemark: string;

    // 虚拟字段
    senderName?: string;
    receiverName?: string;
} 