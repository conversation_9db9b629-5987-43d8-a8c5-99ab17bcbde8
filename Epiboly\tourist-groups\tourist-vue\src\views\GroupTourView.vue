<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 头部导航 -->
    <TheHeader />

    <div class="group-tour-container">
      <div class="text-[40px] font-bold text-[#c9161c] ml-[-100px] mb-[40px]">行程说明</div>

      <div class="process-steps">
        <div class="step cursor-pointer" :class="{ 'active': stepStatus.step1 }" @click="handleStep1">
          <div class="step-icon-wrapper">
            <img src="../assets/group/step1.png" alt="查看行程" class="step-icon" />
            <div v-if="stepStatus.step1" class="step-check">✓</div>
          </div>
          <div class="step-text">查看行程</div>
        </div>

        <div class="arrow">
          <img src="../assets/group/arrow.png" alt="箭头" class="arrow-icon w-[68px] h-[42px]" />
        </div>

        <div class="step" :class="{ 'active': stepStatus.step2 }">
          <div class="step-icon-wrapper">
            <img src="../assets/group/step3.png" alt="加入行程" class="step-icon" />
            <div v-if="stepStatus.step2" class="step-check">✓</div>
          </div>
          <div class="step-text">加入行程</div>
        </div>

        <div class="arrow">
          <img src="../assets/group/arrow.png" alt="箭头" class="arrow-icon w-[68px] h-[42px]" />
        </div>

        <div class="step" :class="{ 'active': stepStatus.step3 }">
          <div class="step-icon-wrapper">
            <img src="../assets/group/step3.png" alt="人数足够行程开始" class="step-icon" />
            <div v-if="stepStatus.step3" class="step-check">✓</div>
          </div>
          <div class="step-text">人数足够行程开始</div>
        </div>
      </div>

      <div class="process-section">
        <!-- 成团成功状态 -->
        <div v-if="groupInfo.isGroupComplete && userHasJoined" class="group-complete-section">
          <div class="success-title">🎉 拼团成功！</div>
          <div class="success-subtitle">当前已有 {{ groupInfo.currentCount || currentUsers.length }} 人参团，已达到成团要求</div>
          <div class="success-notice">
            <div class="notice-title">📞 后续安排</div>
            <div class="notice-content">
              我们的工作人员将在24小时内与您联系，为您安排具体的行程事宜。<br />
              请保持手机畅通，感谢您的耐心等待！
            </div>
          </div>
        </div>

        <!-- 拼团未成功状态 -->
        <div v-else-if="isGroupFailed" class="group-failed-section">
          <div class="failed-title">未成团</div>
          <div class="failed-subtitle">拼团时间已结束，当前只有 {{ currentUsers.length }} 人参团，未达到成团要求</div>
          <div class="failed-notice">
            <div class="notice-title">很遗憾</div>
            <div class="notice-content">
              此次拼团未能达到最低成团人数要求。<br />
              感谢您的参与，期待下次与您同行！
            </div>
          </div>
        </div>

        <!-- 拼团进行中状态 -->
        <div v-else>
          <!-- 显示团号信息 -->
          <div v-if="groupInfo.groupNumber" class="group-info-section">
            <div class="group-number">团号：{{ groupInfo.groupNumber }}</div>
          </div>

          <!-- 如果拼团已成功但用户未参与 -->
          <div v-if="groupInfo.isGroupComplete && !userHasJoined" class="group-complete-tip">
            <div class="complete-message">该团已经成团</div>
            <div class="complete-subtitle">当前拼团已达到人数要求，系统将为您分配新团</div>
          </div>

          <!-- 拼团进行中 -->
          <div v-else-if="!isGroupFailed">
            <div class="remaining-time">
              <div class="time-label">剩余时间</div>
              <div class="time-counter">
                <div class="time-box">{{ countDown.days }}</div>
                <div class="time-text">天</div>
                <div class="time-box">{{ countDown.hours }}</div>
                <div class="time-text">时</div>
                <div class="time-box">{{ countDown.minutes }}</div>
                <div class="time-text">分</div>
              </div>
            </div>

            <!-- 开始行程提示 -->
            <div class="start-tip">
              再有<span class="highlight">{{ remainingPeople }}</span>人即可开始行程
            </div>
          </div>
        </div>

        <!-- 用户头像列表 -->
        <div class="avatars-list">
          <!-- 当人数<=10时，显示所有头像 -->
          <template v-if="currentUsers.length <= 10">
            <div v-for="(user, index) in currentUsers" :key="index" class="avatar-item">
              <div class="avatar-wrapper">
                <img :src="user.avatar" alt="用户头像" class="avatar-img" />
              </div>
            </div>
          </template>

          <!-- 当人数>10时，显示前4个+省略号+后4个 -->
          <template v-else>
            <!-- 前4个头像 -->
            <div v-for="(user, index) in currentUsers.slice(0, 4)" :key="index" class="avatar-item">
              <div class="avatar-wrapper">
                <img :src="user.avatar" alt="用户头像" class="avatar-img" />
              </div>
            </div>

            <div class="avatar-item ellipsis">
              <div class="ellipsis-wrapper">
                <span class="ellipsis-text">...</span>
              </div>
            </div>
            <div class="avatar-item ellipsis">
              <div class="ellipsis-wrapper">
                <span class="ellipsis-text">...</span>
              </div>
            </div>

            <!-- 后4个头像 -->
            <div v-for="(user, index) in currentUsers.slice(-4)" :key="`last-${index}`" class="avatar-item">
              <div class="avatar-wrapper">
                <img :src="user.avatar" alt="用户头像" class="avatar-img" />
              </div>
            </div>
          </template>

          <!-- 空位占位符 - 只在未成团且未失败时显示 -->
          <div v-if="!groupInfo.isGroupComplete && !isGroupFailed" v-for="i in emptySlots" :key="`empty-${i}`"
            class="avatar-item empty">
            <div class="add-icon">+</div>
          </div>
        </div>

        <!-- 加入按钮 -->
        <el-button v-if="!groupInfo.isGroupComplete && !isGroupFailed" class="join-button"
          :type="userHasJoined ? 'success' : 'danger'" @click="handleJoin" :loading="loading"
          :disabled="userHasJoined || groupInfo.isGroupComplete || isGroupFailed">
          {{ userHasJoined ? '已加入拼团' : (groupInfo.isGroupComplete ? '拼团已满' : '立即加入') }}
        </el-button>

        <!-- 成团后的状态按钮 -->
        <div v-else-if="groupInfo.isGroupComplete" class="group-complete-button">
          <el-button type="danger" size="large" disabled>
            ✓ 已成团
          </el-button>
        </div>

        <!-- 拼团失败后的状态按钮 -->
        <div v-else-if="isGroupFailed" class="group-failed-button">
          <el-button type="info" size="large" disabled>
            ✗ 拼团失败
          </el-button>
        </div>
      </div>
    </div>
    <div class="h-[290px]"></div>
    <!-- 底部组件 -->
    <TheFooter />

    <!-- 预定弹窗组件 -->
    <BookingModal v-model:visible="showBookingModal" :product-info="product" :is-group-mode="true"
      @success="handleBookingSuccess" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import TheHeader from '../components/TheHeader.vue'
import TheFooter from '../components/TheFooter.vue'
import BookingModal from '@/components/BookingModal.vue'
import { productApi, orderApi } from '@/api'

const router = useRouter()
const route = useRoute()
const loading = ref(false)

// 产品信息
const product = ref(null)

// 倒计时数据
const countDown = ref({
  days: '00',
  hours: '00',
  minutes: '00'
})

// 拼团信息
const groupInfo = ref({
  currentMembers: [],
  totalRequired: 5,
  remainingSlots: 5,
  isGroupComplete: false,
  currentCount: 0
})

// 计算还需要多少人
const remainingPeople = computed(() => {
  const totalCurrentPeople = currentUsers.value.length
  const totalRequired = groupInfo.value.totalRequired || 5
  return Math.max(0, totalRequired - totalCurrentPeople)
})

// 计算空位数量
const emptySlots = computed(() => {
  return remainingPeople.value
})

// 判断拼团是否失败
const isGroupFailed = computed(() => {
  // 如果拼团已经成功，则不是失败状态
  if (groupInfo.value.isGroupComplete) {
    return false
  }

  // 如果没有结束时间，则不能判断失败
  if (!product.value?.endDate) {
    return false
  }

  // 检查是否超过了结束时间
  const now = new Date()
  const endDate = new Date(product.value.endDate)
  const isExpired = now.getTime() > endDate.getTime()

  // 如果时间到了，且拼团未成功，且当前有参与者，则判断为失败
  return isExpired && !groupInfo.value.isGroupComplete && currentUsers.value.length > 0
})

// 步骤状态
const stepStatus = computed(() => {
  // 只有用户参与了拼团，步骤才会亮起
  if (!userHasJoined.value) {
    // 用户未参与拼团：所有步骤都不亮
    return {
      step1: false,
      step2: false,
      step3: false
    }
  }

  if (groupInfo.value.isGroupComplete && userHasJoined.value) {
    // 拼团成功且用户已参与：三个步骤都亮起
    return {
      step1: true,
      step2: true,
      step3: true
    }
  } else if (userHasJoined.value) {
    // 已加入行程：前两个步骤亮起
    return {
      step1: true,
      step2: true,
      step3: false
    }
  }

  // 默认状态：所有步骤都不亮
  return {
    step1: false,
    step2: false,
    step3: false
  }
})

// 当前用户数据
const currentUsers = computed(() => {
  const users = []
  groupInfo.value.currentMembers.forEach(member => {
    const quantity = member.quantity || 1
    // 根据数量渲染对应数量的头像
    for (let i = 0; i < quantity; i++) {
      users.push({
        id: `${member.id}_${i}`,
        name: member.contactName,
        avatar: member.avatar,
        originalMember: member
      })
    }
  })
  return users
})

// 倒计时定时器
let timer = null

// 计算倒计时
const calculateCountDown = (endDate) => {
  if (!endDate) return

  const now = new Date()
  const end = new Date(endDate)
  const diff = end.getTime() - now.getTime()

  if (diff <= 0) {
    countDown.value = { days: '00', hours: '00', minutes: '00' }
    return
  }

  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

  countDown.value = {
    days: days.toString().padStart(2, '0'),
    hours: hours.toString().padStart(2, '0'),
    minutes: minutes.toString().padStart(2, '0')
  }
}

// 启动倒计时
const startCountDown = () => {
  if (!product.value?.endDate || groupInfo.value.isGroupComplete) return

  calculateCountDown(product.value.endDate)

  timer = setInterval(() => {
    calculateCountDown(product.value.endDate)
  }, 60000) // 每分钟更新一次
}

// 获取产品详情
const fetchProductDetail = async () => {
  const productId = route.query.id
  if (!productId) {
    ElMessage.error('产品ID不能为空')
    router.push('/')
    return
  }

  try {
    const res = await productApi.getProductDetail(productId)
    if (res && res.data) {
      product.value = res.data
      startCountDown() // 启动倒计时
    }
  } catch (error) {
    console.error('获取产品详情失败:', error)
    ElMessage.error('获取产品详情失败')
  } finally {
    requestStatus.productDetail = true;
    checkAllRequestsComplete();
  }
}

// 检查用户是否已加入拼团
const userHasJoined = ref(false)

// 预定弹窗相关
const showBookingModal = ref(false)

// 检查用户是否已经加入过拼团 - 现在通过API返回
const checkUserJoinStatus = () => {
  // 新的多团逻辑中，用户加入状态由后端API直接返回
  // userHasJoined.value 会在 fetchGroupInfo 中根据API返回设置
  userHasJoined.value = groupInfo.value.userHasJoined || false
}

// 获取拼团信息
const fetchGroupInfo = async () => {
  const productId = route.query.id
  if (!productId) return

  try {
    const res = await orderApi.getGroupInfo(productId)
    if (res && res.data) {
      groupInfo.value = {
        currentMembers: res.data.currentMembers || [],
        totalRequired: res.data.totalRequired || 5,
        remainingSlots: res.data.remainingSlots || 5,
        isGroupComplete: res.data.isGroupComplete || false,
        currentCount: res.data.currentCount || 0,
        userHasJoined: res.data.userHasJoined || false,
        groupId: res.data.groupId,
        groupNumber: res.data.groupNumber
      }

      // 设置用户加入状态
      userHasJoined.value = groupInfo.value.userHasJoined
    }
  } catch (error) {
    console.error('获取拼团信息失败:', error)
    // 使用默认值，不显示错误信息
  } finally {
    requestStatus.groupTours = true;
    checkAllRequestsComplete();
  }
}

// 处理加入按钮点击 - 显示预定弹窗
const handleJoin = () => {
  const productId = route.query.id
  if (!productId) {
    ElMessage.error('产品ID不能为空')
    return
  }

  if (userHasJoined.value) {
    ElMessage.warning('您已经加入过这个拼团了')
    return
  }

  if (groupInfo.value.isGroupComplete) {
    ElMessage.warning('拼团人数已满，无法加入')
    return
  }

  if (!product.value || !product.value.id) {
    ElMessage.error('产品信息加载中，请稍后再试')
    return
  }

  showBookingModal.value = true
}

// 处理加入拼团成功事件
const handleBookingSuccess = async (orderData) => {
  const productId = route.query.id

  try {
    // 保存预定信息到localStorage，供后续使用
    localStorage.setItem(`group_tour_booking_${productId}`, JSON.stringify(orderData))

    // 更新用户加入状态
    userHasJoined.value = true

    // 刷新拼团信息显示
    await fetchGroupInfo()

    // 如果已达到所需人数，显示启动行程的消息
    if (groupInfo.value.isGroupComplete) {
      setTimeout(() => {
        ElMessage.success('拼团人数已满！行程即将开始，请留意您的行程消息。')
      }, 1000)
    }
  } catch (error) {
    console.error('处理拼团成功后续操作失败:', error)
  }
}

// 生命周期钩子
onMounted(async () => {
  await fetchProductDetail()
  await fetchGroupInfo()
})

onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer)
  }
})

// 请求完成状态追踪
const requestStatus = reactive({
  productDetail: false,
  groupTours: false
});

// 检查所有请求是否完成并执行翻译
const checkAllRequestsComplete = () => {
  const allComplete = Object.values(requestStatus).every(status => status === true);
  if (allComplete) {
    setTimeout(() => {
      if (window.translate && window.translate.execute) {
        window.translate.execute();
      }
    }, 50);
  }
};

const handleStep1 = () => {
  router.push('/details?id=' + route.query.id)
}
</script>

<style scoped>
.group-tour-container {
  width: 100%;
  max-width: 960px;
  margin: 40px auto;
  padding: 30px 20px;
  padding-top: 80px;
}

.section-title {
  font-size: 24px;
  font-weight: bold;
  color: #c9161c;
  text-align: center;
  margin-bottom: 40px;
}

.process-section {
  background-color: #fff;
  border-radius: 20px;
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.process-steps {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40px;
  gap: 75px;
  width: 100%;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
}

.step.active {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.02);
  }

  100% {
    transform: scale(1);
  }
}

.step-icon-wrapper {
  position: relative;
  display: inline-block;
}

.step-icon {
  width: 60px;
  height: 60px;
  transition: all 0.3s ease;
}

.step-check {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 24px;
  height: 24px;
  background-color: #c9161c;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  box-shadow: 0 2px 6px rgba(201, 22, 28, 0.4);
  animation: checkmark-appear 0.3s ease-in-out;
}

@keyframes checkmark-appear {
  0% {
    opacity: 0;
    transform: scale(0);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.step-text {
  font-size: 16px;
  color: #333;
  transition: color 0.3s ease;
}

.step.active .step-text {
  color: #c9161c;
  font-weight: bold;
}

.arrow {
  margin: 0 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remaining-time {
  margin-bottom: 30px;
  text-align: center;
}

.time-label {
  font-size: 18px;
  color: #333;
  margin-bottom: 15px;
}

.time-counter {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.time-box {
  width: 50px;
  height: 50px;
  background-color: #c9161c;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  font-weight: bold;
  border-radius: 5px;
}

.time-box:nth-child(5) {
  background-color: #ffa317 !important;
}

.time-text {
  font-size: 18px;
  margin: 0 5px;
}

.start-tip {
  font-size: 20px;
  color: #c9161c;
  text-align: center;
  margin-bottom: 30px;
}

.highlight {
  color: #c9161c;
  font-size: 24px;
  font-weight: bold;
  margin: 0 5px;
}

.avatars-list {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 50px;
  width: 100%;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  padding: 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.avatar-item {
  position: relative;
  width: 72px;
  height: 72px;
  transition: transform 0.3s ease;
}

.avatar-item:hover {
  transform: scale(1.05);
}

.avatar-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-wrapper::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: url('../assets/group/avatar-border.png');
  background-size: cover;
  background-position: center;
  z-index: 1;
  filter: drop-shadow(0 4px 8px rgba(201, 22, 28, 0.2));
}

.avatar-img {
  width: 90%;
  height: 90%;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-item.empty {
  border: 2px dashed #ddd;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-item.ellipsis {
  display: flex;
  justify-content: center;
  align-items: center;
}

.ellipsis-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(201, 22, 28, 0.1);
  border-radius: 50%;
  border: 2px solid rgba(201, 22, 28, 0.2);
}

.ellipsis-text {
  font-size: 20px;
  font-weight: bold;
  color: #c9161c;
  letter-spacing: 2px;
}

.add-icon {
  font-size: 30px;
  color: #ddd;
}

.join-button {
  width: 300px;
  height: 50px;
  font-size: 18px;
  font-weight: bold;
  border-radius: 25px;
  background-color: #c9161c;
  border: none;
}

.join-button:hover {
  background-color: #a61016;
}

.tour-details {
  margin-top: 40px;
  background-color: #fff;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.sub-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.tour-info {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.tour-info-item {
  display: flex;
  align-items: flex-start;
}

.info-label {
  font-weight: bold;
  color: #666;
  min-width: 100px;
}

.info-content {
  color: #333;
}

.info-desc {
  color: #333;
  line-height: 1.6;
  margin-top: 5px;
}

.group-complete-section {
  text-align: center;
  padding: 30px 30px;
  background: linear-gradient(135deg, #fff5f5 0%, #fef2f2 50%, #fdf2f2 100%);
  border-radius: 16px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(201, 22, 28, 0.08);
  border: 1px solid rgba(201, 22, 28, 0.1);
  position: relative;
  overflow: hidden;
}

.group-complete-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #c9161c 0%, #e6383e 50%, #c9161c 100%);
}

.success-icon {
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
}

.success-icon .el-icon {
  background: linear-gradient(135deg, #c9161c 0%, #e6383e 100%);
  border-radius: 50%;
  padding: 20px;
  box-shadow: 0 8px 24px rgba(201, 22, 28, 0.3);
  color: white !important;
}

.success-title {
  font-size: 26px;
  font-weight: 700;
  color: #c9161c;
  background-clip: text;
  margin-bottom: 12px;
  letter-spacing: 0.5px;
  position: relative;
  z-index: 1;
}

.success-subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
  font-weight: 500;
  line-height: 1.5;
  position: relative;
  z-index: 1;
}

.success-notice {
  background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
  padding: 20px 24px;
  border-radius: 12px;
  text-align: left;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(201, 22, 28, 0.05);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
}

.notice-title {
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #c9161c 0%, #e6383e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.notice-content {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  font-weight: 400;
}

.group-complete-button {
  margin-top: 20px;
  text-align: center;
}

.group-complete-button .el-button {
  width: 320px;
  height: 56px;
  font-size: 18px;
  font-weight: 600;
  border-radius: 28px;
  background: linear-gradient(135deg, #c9161c 0%, #e6383e 100%);
  border: none;
  box-shadow: 0 8px 24px rgba(201, 22, 28, 0.3);
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
}

.group-complete-button .el-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shine 2s infinite;
}

@keyframes shine {
  0% {
    left: -100%;
  }

  100% {
    left: 100%;
  }
}

.group-complete-tip {
  text-align: center;
  padding: 30px 20px;
  background: linear-gradient(135deg, #f5f5f5 0%, #eeeeee 50%, #f0f0f0 100%);
  border-radius: 16px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.complete-message {
  font-size: 24px;
  font-weight: 600;
  color: #666;
  margin-bottom: 12px;
  letter-spacing: 0.5px;
}

.complete-subtitle {
  font-size: 16px;
  color: #999;
  line-height: 1.5;
  font-weight: 400;
}

.group-failed-section {
  text-align: center;
  padding: 30px 30px;
  background: linear-gradient(135deg, #fdf2f2 0%, #fef2f2 50%, #fff5f5 100%);
  border-radius: 16px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(156, 163, 175, 0.08);
  border: 1px solid rgba(156, 163, 175, 0.1);
  position: relative;
  overflow: hidden;
}

.group-failed-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #9ca3af 0%, #6b7280 50%, #9ca3af 100%);
}

.failed-title {
  font-size: 26px;
  font-weight: 700;
  color: #6b7280;
  margin-bottom: 12px;
  letter-spacing: 0.5px;
  position: relative;
  z-index: 1;
}

.failed-subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
  font-weight: 500;
  line-height: 1.5;
  position: relative;
  z-index: 1;
}

.failed-notice {
  background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
  padding: 20px 24px;
  border-radius: 12px;
  text-align: left;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(156, 163, 175, 0.05);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
}

.failed-notice .notice-title {
  font-size: 16px;
  font-weight: 600;
  color: #6b7280;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.group-failed-button {
  margin-top: 20px;
  text-align: center;
}

.group-failed-button .el-button {
  width: 320px;
  height: 56px;
  font-size: 18px;
  font-weight: 600;
  border-radius: 28px;
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
  border: none;
  box-shadow: 0 8px 24px rgba(156, 163, 175, 0.3);
  letter-spacing: 1px;
  color: white;
}

.group-info-section {
  text-align: center;
  margin-bottom: 20px;
  padding: 15px 20px;
  background: linear-gradient(135deg, #fff5f5 0%, #fef2f2 50%, #fdf2f2 100%);
  border-radius: 12px;
  border: 1px solid rgba(201, 22, 28, 0.1);
}

.group-number {
  font-size: 18px;
  font-weight: 600;
  color: #c9161c;
  letter-spacing: 1px;
}
</style>