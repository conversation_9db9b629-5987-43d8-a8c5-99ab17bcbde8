#!/bin/bash

# 宝塔面板定时任务脚本 - 订单模块
# 数据库配置
DB_NAME="tour"
DB_USER="tour"
DB_PASSWORD="B8w4MzSibXzKnMb5"
DB_HOST="localhost"
DB_PORT="3306"

# API配置
API_BASE_URL="http://localhost:8001"
LOG_DIR="/www/wwwlogs/tourist-api"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# 创建日志目录
mkdir -p $LOG_DIR

# 颜色输出函数
log_info() {
    echo "[$DATE] [INFO] $1" | tee -a $LOG_DIR/cron.log
}

log_error() {
    echo "[$DATE] [ERROR] $1" | tee -a $LOG_DIR/cron.log
}

log_success() {
    echo "[$DATE] [SUCCESS] $1" | tee -a $LOG_DIR/cron.log
}

# 检查服务是否运行
check_service() {
    local response=$(curl -s -o /dev/null -w "%{http_code}" $API_BASE_URL/app/base/comm/captcha)
    if [ "$response" = "200" ]; then
        return 0
    else
        return 1
    fi
}

# 执行定时任务测试 - 每分钟执行
test_schedule_task() {
    log_info "开始执行定时任务测试..."
    
    if ! check_service; then
        log_error "服务未运行，跳过定时任务测试"
        return 1
    fi
    
    # 方法1: 直接调用API接口
    local response=$(curl -s -X POST "$API_BASE_URL/admin/order/order/test-schedule-task" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer test-token" 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        log_success "定时任务测试完成"
    else
        # 方法2: 直接操作数据库
        log_info "API调用失败，使用数据库直接插入测试数据"
        local current_time=$(date '+%Y-%m-%d %H:%M:%S')
        local sql="INSERT INTO message_info (name, phone, email, content, createTime, updateTime) VALUES ('定时任务测试', '13800000000', '<EMAIL>', '这是一条定时任务测试数据，生成时间：$current_time', NOW(), NOW());"
        
        mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD $DB_NAME -e "$sql" 2>/dev/null
        if [ $? -eq 0 ]; then
            log_success "定时任务测试数据写入成功"
        else
            log_error "定时任务测试数据写入失败"
        fi
    fi
}

# 更新过期拼团订单 - 每天凌晨1点执行
update_expired_orders() {
    log_info "开始检查并更新过期拼团订单..."
    
    # SQL: 查找过期的拼团订单并更新状态
    local current_date=$(date '+%Y-%m-%d')
    local sql="
    UPDATE order_order o 
    LEFT JOIN product_product p ON o.productId = p.id 
    SET o.orderStatus = 2, o.updateTime = NOW()
    WHERE o.orderType = 1 
    AND o.orderStatus = 1 
    AND p.endDate < '$current_date';
    
    SELECT ROW_COUNT() as affected_rows;
    "
    
    local result=$(mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD $DB_NAME -e "$sql" 2>/dev/null | tail -n 1)
    
    if [ $? -eq 0 ]; then
        log_success "过期拼团订单更新完成，影响行数: $result"
        
        # 发送通知公告
        local notification_sql="
        INSERT INTO message_notification (title, content, senderId, priority, publishTime, createTime, updateTime, senderRemark) 
        VALUES ('😔 拼团失败通知', '很遗憾，部分拼团产品已截止，由于人数不足未能成团。已为您取消相关订单，如有疑问请联系客服。', 1, 1, NOW(), NOW(), NOW(), '系统自动-过期拼团订单处理');
        "
        mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD $DB_NAME -e "$notification_sql" 2>/dev/null
        log_info "已发送拼团失败通知"
    else
        log_error "过期拼团订单更新失败"
    fi
}

# 更新未成团过期订单 - 每天凌晨1点30分执行
update_unfulfilled_orders() {
    log_info "开始检查并更新未成团的过期拼团订单..."
    
    # 查询过期的拼团产品
    local current_date=$(date '+%Y-%m-%d')
    local products_sql="
    SELECT p.id, p.name, p.groupSize,
           COUNT(o.id) as current_count
    FROM product_product p
    LEFT JOIN order_order o ON p.id = o.productId AND o.orderType = 1 AND o.orderStatus = 1
    WHERE p.productType = 1 
    AND p.endDate < '$current_date'
    GROUP BY p.id, p.name, p.groupSize
    HAVING current_count < p.groupSize AND current_count > 0;
    "
    
    # 获取未成团的产品
    local products=$(mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD $DB_NAME -e "$products_sql" 2>/dev/null | tail -n +2)
    
    if [ -n "$products" ]; then
        local total_updated=0
        
        while IFS=$'\t' read -r product_id product_name group_size current_count; do
            if [ -n "$product_id" ]; then
                # 更新该产品的未成团订单状态
                local update_sql="
                UPDATE order_order 
                SET orderStatus = 2, updateTime = NOW()
                WHERE productId = $product_id 
                AND orderType = 1 
                AND orderStatus = 1;
                
                SELECT ROW_COUNT() as affected_rows;
                "
                
                local affected=$(mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD $DB_NAME -e "$update_sql" 2>/dev/null | tail -n 1)
                total_updated=$((total_updated + affected))
                
                log_info "产品【$product_name】未成团，需要${group_size}人，实际${current_count}人，已更新${affected}个订单"
                
                # 发送针对性通知
                local notification_sql="
                INSERT INTO message_notification (title, content, senderId, priority, publishTime, createTime, updateTime, senderRemark) 
                VALUES ('😔 拼团失败通知', '很遗憾，${product_name} 拼团已截止，需要${group_size}人成团，实际只有${current_count}人参团，未能成团。已为您取消订单，如有疑问请联系客服。', 1, 1, NOW(), NOW(), NOW(), '系统自动-未成团订单处理-产品ID:${product_id}');
                "
                mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD $DB_NAME -e "$notification_sql" 2>/dev/null
            fi
        done <<< "$products"
        
        log_success "未成团过期订单处理完成，总计更新 $total_updated 个订单"
    else
        log_info "没有发现未成团的过期拼团订单"
    fi
}

# 清理测试数据 - 手动执行
clean_test_data() {
    log_info "开始清理定时任务测试数据..."
    
    local sql="DELETE FROM message_info WHERE name = '定时任务测试';"
    local result=$(mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD $DB_NAME -e "$sql; SELECT ROW_COUNT() as deleted_rows;" 2>/dev/null | tail -n 1)
    
    if [ $? -eq 0 ]; then
        log_success "清理测试数据完成，删除 $result 条记录"
    else
        log_error "清理测试数据失败"
    fi
}

# 检查数据库连接
check_database() {
    mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD $DB_NAME -e "SELECT 1;" >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        log_success "数据库连接正常"
        return 0
    else
        log_error "数据库连接失败"
        return 1
    fi
}

# 主函数
main() {
    local task_type=$1
    
    # 检查数据库连接
    if ! check_database; then
        exit 1
    fi
    
    case $task_type in
        "test")
            test_schedule_task
            ;;
        "expired")
            update_expired_orders
            ;;
        "unfulfilled")
            update_unfulfilled_orders
            ;;
        "clean")
            clean_test_data
            ;;
        *)
            echo "用法: $0 {test|expired|unfulfilled|clean}"
            echo "  test        - 执行定时任务测试"
            echo "  expired     - 更新过期拼团订单"
            echo "  unfulfilled - 更新未成团过期订单"
            echo "  clean       - 清理测试数据"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@" 