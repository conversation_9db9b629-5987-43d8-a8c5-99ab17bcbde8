declare const _default: import('vue').DefineComponent<import('vue').ExtractPropTypes<{
    label: StringConstructor;
    expand: {
        type: BooleanConstructor;
        default: boolean;
    };
    isExpand: {
        type: BooleanConstructor;
        default: boolean;
    };
}>, () => any, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {}, string, import('vue').PublicProps, Readonly<import('vue').ExtractPropTypes<{
    label: StringConstructor;
    expand: {
        type: BooleanConstructor;
        default: boolean;
    };
    isExpand: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{}>, {
    expand: boolean;
    isExpand: boolean;
}, {}, {
    ArrowDown: import('vue').DefineComponent<{}, {}, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {}, string, import('vue').VNodeProps & import('vue').AllowedComponentProps & import('vue').ComponentCustomProps, Readonly<import('vue').ExtractPropTypes<{}>>, {}, {}, {}, {}, string, import('vue').ComponentProvideOptions, true, {}, any>;
    ArrowUp: import('vue').DefineComponent<{}, {}, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {}, string, import('vue').VNodeProps & import('vue').AllowedComponentProps & import('vue').ComponentCustomProps, Readonly<import('vue').ExtractPropTypes<{}>>, {}, {}, {}, {}, string, import('vue').ComponentProvideOptions, true, {}, any>;
}, {}, string, import('vue').ComponentProvideOptions, true, {}, any>;
export default _default;
