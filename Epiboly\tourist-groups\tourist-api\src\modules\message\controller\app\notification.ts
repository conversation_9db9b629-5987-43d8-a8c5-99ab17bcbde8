import { Inject, Get, Post, Body, Provide, Query } from '@midwayjs/core';
import { CoolController, BaseController } from '@cool-midway/core';
import { MessageNotificationService } from '../../service/notification';
import { MessageNotificationEntity } from '../../entity/notification';

/**
 * 前端通知消息接口
 */
@CoolController({
    prefix: '/app/notification',
    api: [],
    entity: MessageNotificationEntity,
    service: MessageNotificationService,
})
@Provide()
export class AppMessageNotificationController extends BaseController {
    @Inject()
    messageNotificationService: MessageNotificationService;

    /**
     * 获取用户未读消息数量
     */
    @Get('/unreadCount')
    async getUnreadCount(@Query() query: any) {
        const { userId } = query;
        if (!userId) {
            return this.ok(0);
        }

        const count = await this.messageNotificationService.getUnreadCount(parseInt(userId));
        return this.ok(count);
    }

    /**
     * 获取用户消息列表
     */
    @Post('/list')
    async getUserMessages(@Body() body: any) {
        const { userId, type, page = 1, limit = 10 } = body;

        if (!userId) {
            return this.ok({ list: [], total: 0 });
        }

        const messages = await this.messageNotificationService.getUserMessages(
            parseInt(userId),
            type !== undefined ? parseInt(type) : undefined,
            parseInt(page),
            parseInt(limit)
        );

        // 格式化消息数据
        const formattedMessages = messages.map(msg => ({
            id: msg.id,
            title: msg.title,
            content: msg.content,
            type: msg.type,
            isRead: msg.isRead,
            publishTime: msg.publishTime,
            priority: msg.priority,
            senderName: msg.senderName || '系统',
            time: this.formatTime(msg.publishTime),
        }));

        return this.ok({
            list: formattedMessages,
            total: formattedMessages.length,
        });
    }

    /**
     * 标记消息为已读
     */
    @Post('/markAsRead')
    async markAsRead(@Body() body: any) {
        const { id, userId } = body;

        if (!id || !userId) {
            return this.fail('参数错误');
        }

        const result = await this.messageNotificationService.markAsRead(parseInt(id), parseInt(userId));
        return this.ok(result);
    }

    /**
     * 批量标记消息为已读
     */
    @Post('/markMultipleAsRead')
    async markMultipleAsRead(@Body() body: any) {
        const { ids, userId } = body;

        if (!ids || !Array.isArray(ids) || !userId) {
            return this.fail('参数错误');
        }

        const result = await this.messageNotificationService.markMultipleAsRead(
            ids.map(id => parseInt(id)),
            parseInt(userId)
        );
        return this.ok(result);
    }

    /**
     * 获取用户消息统计
     */
    @Post('/statistics')
    async getUserStatistics(@Body() body: any) {
        const { userId } = body;

        if (!userId) {
            return this.ok({
                total: 0,
                unreadCount: 0,
                systemMessageCount: 0,
                announcementCount: 0,
            });
        }

        const unreadCount = await this.messageNotificationService.getUnreadCount(parseInt(userId));

        // 获取用户的系统消息数量
        const systemMessages = await this.messageNotificationService.getUserMessages(parseInt(userId), 0);
        const announcements = await this.messageNotificationService.getUserMessages(parseInt(userId), 1);

        return this.ok({
            total: systemMessages.length + announcements.length,
            unreadCount,
            systemMessageCount: systemMessages.length,
            announcementCount: announcements.length,
        });
    }

    /**
     * 格式化时间
     */
    private formatTime(time: Date | string): string {
        if (!time) return '';

        const date = new Date(time);
        const now = new Date();
        const diff = now.getTime() - date.getTime();

        // 小于1分钟
        if (diff < 60000) {
            return '刚刚';
        }

        // 小于1小时
        if (diff < 3600000) {
            return `${Math.floor(diff / 60000)}分钟前`;
        }

        // 小于1天
        if (diff < 86400000) {
            return `${Math.floor(diff / 3600000)}小时前`;
        }

        // 小于7天
        if (diff < 604800000) {
            return `${Math.floor(diff / 86400000)}天前`;
        }

        // 超过7天显示具体日期
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
} 