import { Inject, Post } from '@midwayjs/core';
import { <PERSON><PERSON><PERSON>roll<PERSON>, BaseController } from '@cool-midway/core';
import { CooperationAgreementEntity } from '../../entity/agreement';
import { CooperationAgreementService } from '../../service/agreement';

/**
 * 协议信息
 */
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: CooperationAgreementEntity,
  service: CooperationAgreementService,
  pageQueryOp: {
    keyWordLikeFields: ['a.title'],
    where: async ctx => {
      const { createTime } = ctx.request.body;
      let sql = '';
      if (createTime && createTime.length > 0) {
        sql += ` a.createTime BETWEEN '${createTime[0]}' AND '${createTime[1]}' `;
      }
      return sql;
    },
  },
})
export class AdminCooperationAgreementController extends BaseController {
  @Inject()
  cooperationAgreementService: CooperationAgreementService;
}
