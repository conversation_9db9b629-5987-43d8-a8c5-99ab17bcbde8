import { createRouter, createWebHistory } from 'vue-router'
import { getToken } from '../utils/auth'

const routes = [
  {
    path: '/',
    redirect: '/home'
  },
  {
    path: '/home',
    name: 'Home',
    component: () => import('../views/HomeView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/list',
    name: 'List',
    component: () => import('../views/ListView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/detail/:id',
    name: 'Detail',
    component: () => import('../views/DetailView.vue'),
    props: true,
    meta: { requiresAuth: true }
  },
  {
    path: '/group-tour',
    name: 'GroupTour',
    component: () => import('../views/GroupTourView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/apply',
    name: 'Apply',
    component: () => import('../views/FormView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('../views/AboutView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/user-center',
    name: 'UserCenter',
    component: () => import('../views/UserCenterView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/contact',
    name: 'Contact',
    component: () => import('../views/ContactView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/cooperation',
    name: 'Cooperation',
    component: () => import('../views/CooperationView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/business',
    name: 'Business',
    component: () => import('../views/BusinessView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/details',
    name: 'ProductDetail',
    component: () => import('../views/ProductDetail.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/LoginView.vue')
  },
  {
    path: '/order-detail',
    name: 'OrderDetail',
    component: () => import('../views/OrderDetail.vue'),
    meta: { requiresAuth: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 检查需要认证的路由
  if (to.matched.some(record => record.meta.requiresAuth)) {
    const token = getToken()
    if (!token) {
      next('/login')
      return
    }
  }
  next()
})

import { executeTranslate } from '../utils/translate'

// 路由切换后的处理
router.afterEach((to, from) => {
  // 页面切换后延迟执行翻译，确保页面渲染完成
  executeTranslate(50); // 延迟500ms执行，确保页面完全渲染
})

export default router 