import { get, post } from '../utils/request';

// 订单相关API
export const orderApi = {
  // 获取产品拼团信息
  getGroupInfo: (productId) => get('/app/order/group-info', { productId }),
  
  // 加入拼团
  joinGroup: (orderData) => post('/app/order/join-group', orderData),

  // 创建预定订单
  createOrder: (orderData) => post('/app/order/create', orderData),

  // 获取我的订单列表
  getMyOrders: (page = 1, pageSize = 10, orderType = 1) => get('/app/order/my-orders', { 
    page, 
    pageSize,
    orderType
  }),

  // 获取我的订单统计数据
  getMyOrdersStats: () => get('/app/order/my-orders-stats'),

  // 确认订单
  confirmOrder: (orderNumber, contactPhone) => post('/app/order/confirm', { 
    orderNumber, 
    contactPhone 
  }),
};

export default orderApi; 