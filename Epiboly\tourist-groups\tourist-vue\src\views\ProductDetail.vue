<script setup>
import { ref, onMounted, watch, reactive } from 'vue';
import TheHeader from '@/components/TheHeader.vue';
import TheFooter from '@/components/TheFooter.vue';
import BookingModal from '@/components/BookingModal.vue';
import productDetailImg from '@/assets/down.png';
import hotIcon from '@/assets/hot.png';
import { productApi } from '@/api';
import { ElMessage } from 'element-plus';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();

// 当前选中的标签
const activeSection = ref('prebooking');

// 监听滚动，更新当前选中的标签
onMounted(() => {
    window.addEventListener('scroll', handleScroll);
    setupLanguageWatch();
});

const handleScroll = () => {
    const sections = ['prebooking', 'pricing', 'details'];

    for (const section of sections) {
        const element = document.getElementById(section);
        if (!element) continue;

        const rect = element.getBoundingClientRect();
        if (rect.top <= 150 && rect.bottom >= 150) {
            activeSection.value = section;
            break;
        }
    }
};

// 产品数据
const product = ref({
    id: '',
    productCode: '',
    title: '',
    subtitle: '',
    productType: 0, // 0: 预定, 1: 拼团
    details: {
        city: '',
        days: '',
        transport: '',
        meetPlace: '',
        advanceBooking: '',
        pickup: '',
        bookingNote: ''
    },
    contactInfo: '',
    price: '0',
    startDate: '',
    endDate: '',
    originalPrice: '0',
    images: [],
    cover: '',
    itineraryAttachment: '', // 新增行程附件字段
    isHot: false // 新增isHot字段
});

// 加载状态
const loading = ref(false);

// 预定须知类型和自定义字段
const bookingNoticeType = ref(0);
const customBookingNoticeFields = ref([]);

// 行程类型和自定义字段
const itineraryType = ref(0);
const customItineraryFields = ref([]);

// 预订须知数据
const bookingInfo = ref({
    tips: '',
    bookingTime: '',
    playDuration: '',
    transport: '',
    route: '',
    recommended: [],
    includes: [],
    excludes: [],
    selfPayItems: '',
    refundRules: '',
    ticketLocation: '',
    ticketMethod: '',
    invoiceInfo: '',
    serviceGuarantee: [],
    braceletInstructions: ''
});

// 行程内容
const itinerary = ref({
    overview: '',
    days: [],
    shopping: ''
});

// 价格表格数据
const priceTable = ref([]);

// 产品详情数据
const productDetails = ref({
    title: '',
    content: '',
    travelMethods: [],
    otherWays: ''
});

// 推荐产品
const recommendedTours = ref([]);

// 预定弹窗相关
const showBookingModal = ref(false);

// 请求完成状态追踪
const requestStatus = reactive({
    productDetail: false,
    recommend: false
});

// 检查所有请求是否完成并执行翻译
const checkAllRequestsComplete = () => {
    const allComplete = Object.values(requestStatus).every(status => status === true);
    if (allComplete) {
        setTimeout(() => {
            translate.execute();
        }, 50);
    }
};

// 检测当前语言是否为俄语
const isRussian = ref(false);

// 检测当前语言
const detectCurrentLanguage = () => {
    try {
        if (window.translate && window.translate.language) {
            const currentLang = window.translate.language.getCurrent();
            isRussian.value = currentLang === 'russian';
        }
    } catch (error) {
        console.warn('语言检测失败:', error);
        isRussian.value = false;
    }
};

// 监听语言变化
const setupLanguageWatch = () => {
    // 初始检测
    detectCurrentLanguage();

    // 定期检测语言变化
    setInterval(() => {
        detectCurrentLanguage();
    }, 1000);
};

// 获取产品详情
const fetchProductDetail = async () => {
    const productId = route.query.id;
    if (!productId) {
        ElMessage.error('产品ID不能为空');
        return;
    }

    loading.value = true;
    try {
        const res = await productApi.getProductDetail(productId);
        if (res && res.data) {
            const data = res.data;

            // 更新产品基本信息
            product.value = {
                id: data.id,
                productCode: data.productCode,
                title: data.name,
                subtitle: data.subtitle,
                productType: data.productType || 0, // 默认为0(预定)
                details: {
                    city: data.city,
                    days: data.days ? `${data.days}天` : '',
                    transport: data.transport,
                    meetPlace: data.destination,
                    advanceBooking: data.startDate && data.endDate ? `${data.startDate} 至 ${data.endDate}` : '',
                    pickup: data.activityInfo || '活动信息',
                    bookingNote: data.braceletInstructions || ''
                },
                contactInfo: data.teamConsult || '',
                price: data.price,
                startDate: data.startDate,
                endDate: data.endDate,
                originalPrice: data.originalPrice,
                images: data.images || [],
                cover: data.cover,
                itineraryAttachment: data.itineraryAttachment || '', // 获取行程附件字段
                isHot: data.isHot || false // 获取isHot字段
            };

            // 保存预定须知类型和自定义字段
            bookingNoticeType.value = parseInt(data.bookingNoticeType) || 0;
            customBookingNoticeFields.value = data.customBookingNoticeFields || [];

            // 保存行程类型和自定义字段
            itineraryType.value = parseInt(data.itineraryType) || 0;
            customItineraryFields.value = data.customItineraryFields || [];

            // 根据预定须知类型选择数据源
            if (data.bookingNoticeType === 1 && data.customBookingNoticeFields) {
                // 使用自定义字段
                const customFields = data.customBookingNoticeFields;
                bookingInfo.value = {
                    tips: data.introduce,
                    bookingTime: customFields.find(f => f.key === '营业时间')?.value || data.businessHours,
                    playDuration: customFields.find(f => f.key === '游玩时长')?.value || '',
                    transport: data.transport,
                    route: customFields.find(f => f.key === '路线')?.value || '',
                    recommended: customFields.filter(f => f.key.includes('推荐')) || [],
                    includes: customFields.filter(f => f.key.includes('包含')) || [],
                    excludes: customFields.filter(f => f.key.includes('不包含')) || [],
                    selfPayItems: data.selfPayItems || [],
                    refundRules: data.refundRules,
                    ticketLocation: data.ticketLocation,
                    ticketMethod: data.ticketMethod,
                    invoiceInfo: data.invoiceInfo,
                    serviceGuarantee: data.serviceGuarantee || [],
                    braceletInstructions: data.braceletInstructions || ''
                };
            } else {
                // 使用预设字段
                bookingInfo.value = {
                    tips: data.introduce,
                    bookingTime: data.businessHours,
                    playDuration: '',
                    transport: data.transport,
                    route: '',
                    recommended: [],
                    includes: [],
                    excludes: [],
                    selfPayItems: data.selfPayItems || [],
                    refundRules: data.refundRules,
                    ticketLocation: data.ticketLocation,
                    ticketMethod: data.ticketMethod,
                    invoiceInfo: data.invoiceInfo,
                    serviceGuarantee: data.serviceGuarantee || [],
                    braceletInstructions: data.braceletInstructions || ''
                };
            }

            // 根据行程信息类型选择数据源
            if (parseInt(data.itineraryType) === 1 && data.customItineraryFields) {
                // 使用自定义行程字段
                const customItinerary = data.customItineraryFields;
                itinerary.value = {
                    overview: customItinerary.find(f => f.key === '行程概述')?.value || '',
                    days: customItinerary.filter(f => f.key.includes('第') && f.key.includes('天')) || [],
                    shopping: customItinerary.find(f => f.key === '购物说明')?.value || ''
                };
            } else {
                // 使用预设字段（保持原有逻辑）
                itinerary.value = {
                    overview: data.itineraryOverview || '',
                    days: data.itineraryDetails || [],
                    shopping: data.shoppingSummary || ''
                };
            }

            // 更新价格表
            priceTable.value = data.priceTable || [];

            // 更新产品详情
            productDetails.value = {
                title: data.name,
                content: data.productDetail || data.introduce,
                travelMethods: data.travelRoute ? [data.travelRoute] : [],
                otherWays: data.otherWays || ''
            };
        }
    } catch (error) {
        console.error('获取产品详情失败:', error);
        ElMessage.error('获取产品详情失败');
    } finally {
        loading.value = false;
        requestStatus.productDetail = true;
        checkAllRequestsComplete();
    }
};

// 获取推荐产品
const fetchRecommendedProducts = async () => {
    try {
        const res = await productApi.getRecommendedProducts(6);
        if (res && res.data) {
            recommendedTours.value = res.data.map(item => ({
                id: item.id,
                title: item.name,
                price: item.price,
                originalPrice: item.originalPrice,
                bookingCount: item.bookingCount ? `J${item.bookingCount}` : '',
                image: item.cover,
                views: item.views || 0
            }));
        }
    } catch (error) {
        console.error('获取推荐产品失败:', error);
    } finally {
        requestStatus.recommend = true;
        checkAllRequestsComplete();
    }
};

// 处理拼团点击事件
const handleGroupClick = () => {
    router.push('/group-tour?id=' + route.query.id);
};

// 处理立即预定点击事件
const handleBookingClick = () => {
    if (!product.value.id) {
        ElMessage.error('产品信息加载中，请稍后再试');
        return;
    }
    showBookingModal.value = true;
};

// 处理预定成功事件
const handleBookingSuccess = (orderData) => {
    ElMessage({
        message: `预定成功！订单号：${orderData.orderNumber}，请保存好订单信息。`,
        type: 'success',
        duration: 5000,
        showClose: true
    });
    // 可以在这里添加其他成功后的处理逻辑，比如跳转到订单页面
};

// 处理推荐产品点击事件
const handleTourClick = (id) => {
    console.log(id);
    if (id === route.query.id) {
        // 如果是同一个产品，不进行跳转
        return;
    }
    router.push(`/details?id=${id}`);
};

// 处理咨询点击事件
const handleConsultClick = () => {
    router.push('/contact');
};

// 格式化表格数据
const formatTableCell = (value, type) => {
    if (value === null || value === undefined || value === '') {
        return '-';
    }

    return value;
};

// 处理行程下载点击事件
const handleItineraryDownload = async () => {
    if (!product.value.itineraryAttachment) {
        ElMessage.error('行程附件不存在');
        return;
    }

    try {
        // 通过fetch获取文件内容，这样可以强制下载而不是打开
        const response = await fetch(product.value.itineraryAttachment.replace('8001', '8080'));
        if (!response.ok) {
            throw new Error('文件下载失败');
        }

        const blob = await response.blob();

        // 从URL中提取文件名，如果没有则使用默认名称
        const url = new URL(product.value.itineraryAttachment.replace('8001', '8080'));
        let fileName = url.pathname.split('/').pop() || `行程详情_${product.value.productCode}`;

        // 如果文件名没有扩展名，根据Content-Type添加适当的扩展名
        if (!fileName.includes('.')) {
            const contentType = response.headers.get('content-type');
            if (contentType) {
                // 图片格式
                if (contentType.includes('image/jpeg')) fileName += '.jpg';
                else if (contentType.includes('image/png')) fileName += '.png';
                else if (contentType.includes('image/gif')) fileName += '.gif';
                else if (contentType.includes('image/webp')) fileName += '.webp';
                else if (contentType.includes('image/bmp')) fileName += '.bmp';
                else if (contentType.includes('image/tiff')) fileName += '.tiff';
                else if (contentType.includes('image/svg')) fileName += '.svg';
                // 文档格式
                else if (contentType.includes('application/pdf')) fileName += '.pdf';
                else if (contentType.includes('application/msword')) fileName += '.doc';
                else if (contentType.includes('application/vnd.openxmlformats-officedocument.wordprocessingml.document')) fileName += '.docx';
                // Excel格式
                else if (contentType.includes('application/vnd.ms-excel')) fileName += '.xls';
                else if (contentType.includes('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')) fileName += '.xlsx';
                // PowerPoint格式
                else if (contentType.includes('application/vnd.ms-powerpoint')) fileName += '.ppt';
                else if (contentType.includes('application/vnd.openxmlformats-officedocument.presentationml.presentation')) fileName += '.pptx';
                else if (contentType.includes('application/vnd.ms-powerpoint.presentation.macroEnabled.12')) fileName += '.pptm';
                else if (contentType.includes('application/vnd.openxmlformats-officedocument.presentationml.slideshow')) fileName += '.ppsx';
                else if (contentType.includes('application/vnd.ms-powerpoint.slideshow.macroEnabled.12')) fileName += '.ppsm';
                // 压缩格式
                else if (contentType.includes('application/zip')) fileName += '.zip';
                else if (contentType.includes('application/x-rar-compressed')) fileName += '.rar';
                else if (contentType.includes('application/x-7z-compressed')) fileName += '.7z';
                else if (contentType.includes('application/gzip')) fileName += '.gz';
                else if (contentType.includes('application/x-tar')) fileName += '.tar';
                // 可执行文件
                else if (contentType.includes('application/vnd.android.package-archive')) fileName += '.apk';
                else if (contentType.includes('application/x-msdownload')) fileName += '.exe';
                // 文本格式
                else if (contentType.includes('text/plain')) fileName += '.txt';
                else if (contentType.includes('text/csv')) fileName += '.csv';
                else if (contentType.includes('application/json')) fileName += '.json';
                else if (contentType.includes('application/xml')) fileName += '.xml';
                // 视频格式
                else if (contentType.includes('video/mp4')) fileName += '.mp4';
                else if (contentType.includes('video/avi')) fileName += '.avi';
                else if (contentType.includes('video/quicktime')) fileName += '.mov';
                else if (contentType.includes('video/x-msvideo')) fileName += '.avi';
                // 音频格式
                else if (contentType.includes('audio/mpeg')) fileName += '.mp3';
                else if (contentType.includes('audio/wav')) fileName += '.wav';
                else if (contentType.includes('audio/ogg')) fileName += '.ogg';
            }
        }

        // 创建下载链接
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = fileName;
        link.style.display = 'none';

        // 添加到DOM，触发下载，然后清理
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 释放URL对象
        window.URL.revokeObjectURL(downloadUrl);

        ElMessage.success('开始下载行程文件');
    } catch (error) {
        console.error('下载失败:', error);
        ElMessage.error('文件下载失败，请稍后重试');
    }
};

// 处理城市点击事件
const handleCityClick = () => {
    // 根据城市名称跳转到相应的城市页面或产品列表页面
    // 这里可以根据实际需求调整跳转逻辑
    router.push(`/business?city=${encodeURIComponent(product.value.details.city)}`);
};

// 监听路由变化，重新加载数据
watch(() => route.query.id, (newId, oldId) => {
    if (newId && newId !== oldId) {
        fetchProductDetail();
        fetchRecommendedProducts();
    }
}, { immediate: false });

onMounted(() => {
    fetchProductDetail();
    fetchRecommendedProducts();
});
</script>

<template>
    <div class="min-h-screen bg-gray-50" v-loading="loading">
        <!-- 使用 TheHeader 组件 -->
        <TheHeader />

        <!-- 主体内容 - 添加背景图片 -->
        <div class="main-content-bg">
            <div class="container mx-auto px-4 pt-20">
                <!-- 面包屑导航 -->
                <div class="text-sm px-2 py-3 text-gray-500" v-if="product.details.city || product.details.days">
                    <a href="/" class="hover:text-blue-800 hover:underline cursor-pointer">旅游度假首页</a>
                    <span class="mx-1" v-if="product.details.city">></span>
                    <span v-if="product.details.city" class="hover:text-blue-800 hover:underline cursor-pointer"
                        @click="handleCityClick">{{ product.details.city }}</span>
                    <span class="mx-1" v-if="product.details.days">></span>
                    <span v-if="product.details.days">{{ product.details.days }}</span>
                </div>

                <!-- 产品主内容区 -->
                <div class="bg-white border border-gray-200 mb-4 relative">
                    <div class="absolute top-[16px] right-[-8px] z-10">
                        <div class="relative">
                            <div class="bg-red-600 text-white px-4 py-3">
                                <div class="text-2xl font-bold" v-if="!isRussian">${{ product.price }}<span
                                        class="text-sm font-normal ml-1">起</span></div>
                                <div class="text-[16px] text-center" v-else>Цена от $<span class="text-2xl font-bold">{{
                                    product.price
                                        }}</span></div>
                                <div class="text-xs mt-1">票面价：${{ product.originalPrice }}</div>
                            </div>
                            <!-- 向外突出的三角形 -->
                            <div
                                class="absolute bottom-[-8px] right-0 w-0 h-0 border-l-[8px] border-l-[#a9691a] border-b-[8px] border-b-transparent">
                            </div>
                        </div>
                    </div>

                    <!-- 产品顶部信息 -->
                    <div class="p-4 border-b border-gray-200">
                        <!-- 产品编号和热门标签 -->
                        <div class="flex items-center justify-between mb-2">
                            <div class="text-gray-500 text-sm">产品编号：{{ product.productCode }}</div>
                        </div>

                        <!-- 产品标题 -->
                        <h1 class="text-xl font-bold text-gray-800 mb-2 flex items-center" style="width: 92%;">
                            {{ product.title }}
                            <span v-if="product.isHot"
                                class="inline-flex items-center bg-[#de5454] text-white px-3 rounded-[6px] text-[14px] ml-2 leading-[1] py-1.5">
                                <img :src="hotIcon" alt="热门" class="w-[15px] h-[15px] mr-1 filter brightness-0 invert">
                                热门
                            </span>
                        </h1>

                        <!-- 产品副标题 -->
                        <div class="text-sm text-gray-500">
                            {{ product.subtitle }}
                        </div>
                    </div>

                    <!-- 产品主体内容 -->
                    <div class="flex px-4 pb-4 relative mt-4">

                        <!-- 左侧产品图片区 -->
                        <div class="w-3/5 pr-4">
                            <div class="flex">
                                <div class="w-4/5 pr-2">
                                    <img :src="product.cover" alt="产品主图"
                                        class="w-full h-[420px] object-cover border border-gray-200">
                                </div>
                                <div class="w-1/5 flex flex-col space-y-2">
                                    <img v-for="(image, index) in product.images.slice(0, 4)" :key="index" :src="image"
                                        :alt="`缩略图${index + 1}`"
                                        class="w-full h-[99px] object-cover border border-gray-200 cursor-pointer hover:border-red-500">
                                    <!-- 如果图片不足4张，显示默认图片 -->
                                    <img v-for="i in Math.max(0, 4 - product.images.length)" :key="`default-${i}`"
                                        :src="product.cover" :alt="`默认图${i}`"
                                        class="w-full h-[99px] object-cover border border-gray-200 cursor-pointer hover:border-red-500">
                                </div>
                            </div>
                        </div>

                        <!-- 右侧产品信息区 -->
                        <div class="w-2/5">
                            <!-- 产品信息列表 -->
                            <div class="p-3 pt-0 pb-0 bg-white h-full flex flex-col">
                                <!-- 产品信息表格 - 占据剩余空间 -->
                                <div class="flex-1">
                                    <!-- 当 itineraryType 为 1 时，使用自定义字段渲染 -->
                                    <table v-if="itineraryType === 1 && customItineraryFields.length > 0"
                                        class="w-full text-sm">
                                        <tr v-for="(field, index) in customItineraryFields" :key="field.key"
                                            class="label-item">
                                            <td class="py-2 pt-1 text-gray-600 w-20">{{ field.key }}:</td>
                                            <td class="py-2 pt-1 text-gray-800">{{ field.value }}</td>
                                        </tr>
                                        <tr>
                                            <td class="py-2 text-gray-600">团队/咨询:</td>
                                            <td class="py-2 text-gray-800">{{ product.contactInfo }}</td>
                                        </tr>
                                    </table>

                                    <!-- 当 itineraryType 不为 1 时，使用预设字段 -->
                                    <table v-else class="w-full text-sm">
                                        <tr class="label-item">
                                            <td class="py-2 pt-1 text-gray-600 w-20">出发城市:</td>
                                            <td class="py-2 pt-1 text-gray-800">{{ product.details.city || '-' }}</td>
                                        </tr>
                                        <tr class="label-item">
                                            <td class="py-2 text-gray-600">目的地:</td>
                                            <td class="py-2 text-gray-800">{{ product.details.meetPlace || '-' }}</td>
                                        </tr>
                                        <tr class="label-item">
                                            <td class="py-2 text-gray-600">行程天数:</td>
                                            <td class="py-2 text-red-600">{{ product.details.days || '-' }}</td>
                                        </tr>
                                        <tr class="label-item">
                                            <td class="py-2 text-gray-600">出行日期:</td>
                                            <td class="py-2">
                                                <div class="flex items-center" style="width: 260px;">
                                                    <el-date-picker :model-value="[product.startDate, product.endDate]"
                                                        type="daterange" range-separator="至" start-placeholder="开始日期"
                                                        end-placeholder="结束日期" size="small" format="YYYY-MM-DD"
                                                        value-format="YYYY-MM-DD" :clearable="false" :editable="false"
                                                        @update:model-value="() => false" @change="() => false" />
                                                </div>
                                            </td>
                                        </tr>
                                        <tr class="label-item">
                                            <td class="py-2 text-gray-600">交通信息:</td>
                                            <td class="py-2 text-gray-800">{{ product.details.transport || '-' }}</td>
                                        </tr>
                                        <tr>
                                            <td class="py-2 text-gray-600">活动信息:</td>
                                            <td class="py-2 text-gray-800">{{ product.details.pickup || '-' }}</td>
                                        </tr>
                                        <tr>
                                            <td class="py-2 text-gray-600">团队/咨询:</td>
                                            <td class="py-2 text-gray-800">{{ product.contactInfo || '-' }}</td>
                                        </tr>
                                    </table>

                                    <!-- 服务保障标签 -->
                                    <div class="mt-1">
                                        <div class="flex items-center gap-2 mb-2">
                                            <span class="text-sm text-gray-600">服务保障:</span>
                                            <div class="flex flex-wrap gap-2">
                                                <span v-for="guarantee in bookingInfo.serviceGuarantee" :key="guarantee"
                                                    class="bg-red-50 text-red-600 border border-red-200 rounded px-2 py-1 text-xs">
                                                    {{ guarantee }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>


                                </div>

                                <!-- 按钮区域 - 固定在底部 -->
                                <div class="mt-auto pt-6 flex items-center justify-between">
                                    <div class="flex gap-4">
                                        <!-- productType 为 0 时显示预定按钮 -->
                                        <button v-if="product.productType === 0" @click="handleBookingClick"
                                            class="bg-red-600 text-white rounded py-3 px-12 text-base font-medium hover:bg-red-700 transition">
                                            立即预定
                                        </button>
                                        <!-- productType 为 1 时显示拼团按钮 -->
                                        <button @click="handleGroupClick" v-if="product.productType === 1"
                                            class="bg-orange-500 text-white rounded py-3 px-12 text-base font-medium hover:bg-orange-600 transition">
                                            发起拼团
                                        </button>
                                    </div>
                                    <button v-if="product.itineraryAttachment" @click="handleItineraryDownload"
                                        class="text-[#c7161b] text-sm hover:underline flex items-center gap-1 bg-transparent border-none cursor-pointer p-0">
                                        行程下载
                                        <img :src="productDetailImg" alt="产品明细" class="w-[14px] h-[14px]">
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 内容区域 -->
                <div class="flex gap-4">
                    <!-- 左侧内容区域 -->
                    <div class="w-4/5">
                        <!-- 导航栏 -->
                        <div class="bg-gray-700 flex mb-4 sticky top-20 z-10 px-[4px] py-[4px]">
                            <a href="#prebooking"
                                :class="['px-5 py-3 text-sm font-medium transition',
                                    activeSection === 'prebooking' ? 'bg-white text-gray-700' : 'text-white hover:bg-gray-600']">
                                预订须知
                            </a>
                            <a href="#pricing"
                                :class="['px-5 py-3 text-sm font-medium transition',
                                    activeSection === 'pricing' ? 'bg-white text-gray-700' : 'text-white hover:bg-gray-600']">
                                产品价格
                            </a>
                            <a href="#details"
                                :class="['px-5 py-3 text-sm font-medium transition',
                                    activeSection === 'details' ? 'bg-white text-gray-700' : 'text-white hover:bg-gray-600']">
                                产品详情
                            </a>
                            <div class="flex-grow"></div>
                            <a @click="handleConsultClick" href="#"
                                class="px-8 py-3 text-sm text-white font-bold bg-red-600 hover:bg-red-700 transition shadow-lg">咨询</a>
                        </div>

                        <!-- 预订须知 -->
                        <div id="prebooking" class="bg-white border border-gray-200 mb-4 p-4">
                            <div class="flex items-center mb-4">
                                <div class="bg-red-600 w-5 h-5 flex-shrink-0 mr-2 flex items-center justify-center">
                                    <span class="text-white text-xs">!</span>
                                </div>
                                <span class="text-lg font-bold text-gray-700">预订须知</span>
                            </div>

                            <div class="mb-4">
                                <table v-if="bookingNoticeType === 1 && customBookingNoticeFields.length > 0"
                                    class="w-full border-collapse">
                                    <tr v-for="(field, index) in customBookingNoticeFields" :key="field.key"
                                        :class="index % 2 === 0 ? 'bg-gray-50 border-b border-gray-100' : 'bg-white border-b border-gray-100'">
                                        <td class="py-2 pr-2 align-top w-24">
                                            <span class="text-sm font-medium text-gray-700">{{ field.key }}</span>
                                        </td>
                                        <td class="py-2 text-sm text-gray-600">
                                            {{ field.value }}
                                        </td>
                                    </tr>
                                </table>

                                <!-- 当 bookingNoticeType 不为 1 时，渲染预设字段 -->
                                <table v-else class="w-full border-collapse">
                                    <tr class="bg-gray-50 border-b border-gray-100">
                                        <td class="py-2 pr-2 align-top w-24">
                                            <span class="text-sm font-medium text-gray-700">本单详情</span>
                                        </td>
                                        <td class="py-2 text-sm text-gray-600">
                                            {{ bookingInfo.tips }}
                                        </td>
                                    </tr>
                                    <tr class="bg-white border-b border-gray-100" v-if="bookingInfo.bookingTime">
                                        <td class="py-2 pr-2 align-top w-24">
                                            <span class="text-sm font-medium text-gray-700">营业时间</span>
                                        </td>
                                        <td class="py-2 text-sm text-gray-600">
                                            {{ bookingInfo.bookingTime }}
                                        </td>
                                    </tr>
                                    <tr class="bg-gray-50 border-b border-gray-100" v-if="bookingInfo.ticketLocation">
                                        <td class="py-2 pr-2 align-top w-24">
                                            <span class="text-sm font-medium text-gray-700">取票地点</span>
                                        </td>
                                        <td class="py-2 text-sm text-gray-600">
                                            {{ bookingInfo.ticketLocation }}
                                        </td>
                                    </tr>
                                    <tr class="bg-white border-b border-gray-100" v-if="bookingInfo.ticketMethod">
                                        <td class="py-2 pr-2 align-top w-24">
                                            <span class="text-sm font-medium text-gray-700">取票方式</span>
                                        </td>
                                        <td class="py-2 text-sm text-gray-600">
                                            {{ bookingInfo.ticketMethod }}
                                        </td>
                                    </tr>
                                    <tr class="bg-gray-50 border-b border-gray-100" v-if="bookingInfo.refundRules">
                                        <td class="py-2 pr-2 align-top w-24">
                                            <span class="text-sm font-medium text-gray-700">退改规则</span>
                                        </td>
                                        <td class="py-2 text-sm text-gray-600">
                                            {{ bookingInfo.refundRules }}
                                        </td>
                                    </tr>
                                    <tr class="bg-white border-b border-gray-100" v-if="bookingInfo.invoiceInfo">
                                        <td class="py-2 pr-2 align-top w-24">
                                            <span class="text-sm font-medium text-gray-700">发票说明</span>
                                        </td>
                                        <td class="py-2 text-sm text-gray-600">
                                            {{ bookingInfo.invoiceInfo }}
                                        </td>
                                    </tr>
                                    <tr class="bg-white border-b border-gray-100" v-if="bookingInfo.selfPayItems">
                                        <td class="py-2 pr-2 align-top w-24">
                                            <span class="text-sm font-medium text-gray-700">自费项目</span>
                                        </td>
                                        <td class="py-2 text-sm text-gray-600">
                                            {{ bookingInfo.selfPayItems }}
                                        </td>
                                    </tr>
                                    <tr class="bg-white border-b border-gray-100"
                                        v-if="bookingInfo.braceletInstructions">
                                        <td class="py-2 pr-2 align-top w-24">
                                            <span class="text-sm font-medium text-gray-700">腕带说明</span>
                                        </td>
                                        <td class="py-2 text-sm text-gray-600">
                                            {{ bookingInfo.braceletInstructions }}
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- 产品价格 -->
                        <div id="pricing" class="bg-white border border-gray-200 mb-4 p-4">
                            <div class="mb-4">
                                <div class="flex items-center mb-2">
                                    <div
                                        class="bg-red-600 w-6 h-6 rounded-full flex-shrink-0 mr-2 flex items-center justify-center">
                                        <span class="text-white text-sm font-bold">$</span>
                                    </div>
                                    <span class="text-lg font-bold text-gray-700">产品价格</span>
                                </div>
                                <div class="w-24 h-1 bg-red-600"></div>
                            </div>
                            <!-- 新的动态价格表格式 -->
                            <div class="overflow-x-auto"
                                v-if="priceTable && priceTable.headers && priceTable.rows && priceTable.rows.length > 0">
                                <table class="w-full border-collapse">
                                    <thead>
                                        <tr class="bg-gray-100">
                                            <th v-for="header in priceTable.headers" :key="header.key"
                                                class="border border-gray-200 py-3 px-4 text-center text-sm font-medium text-gray-700">
                                                {{ header.label }}
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="(row, index) in priceTable.rows" :key="index"
                                            :class="index % 2 === 0 ? 'bg-gray-50' : 'bg-white'">
                                            <td v-for="header in priceTable.headers" :key="header.key"
                                                class="border border-gray-200 py-3 px-4 text-center text-sm">
                                                {{ formatTableCell(row[header.key], header.type) }}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <!-- 兼容旧格式 -->
                            <div class="overflow-x-auto"
                                v-else-if="priceTable && Array.isArray(priceTable) && priceTable.length > 0">
                                <table class="w-full border-collapse">
                                    <thead>
                                        <tr class="bg-gray-100">
                                            <th
                                                class="border border-gray-200 py-3 px-4 text-center text-sm font-medium text-gray-700">
                                                人数</th>
                                            <th
                                                class="border border-gray-200 py-3 px-4 text-center text-sm font-medium text-gray-700">
                                                双人间</th>
                                            <th
                                                class="border border-gray-200 py-3 px-4 text-center text-sm font-medium text-gray-700">
                                                单人间</th>
                                            <th
                                                class="border border-gray-200 py-3 px-4 text-center text-sm font-medium text-gray-700">
                                                加床</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="(row, index) in priceTable" :key="index"
                                            :class="index % 2 === 0 ? 'bg-gray-50' : 'bg-white'">
                                            <td class="border border-gray-200 py-3 px-4 text-center text-sm">{{
                                                row.peopleCount }}</td>
                                            <td class="border border-gray-200 py-3 px-4 text-center text-sm">{{
                                                row.doubleRoom }}</td>
                                            <td class="border border-gray-200 py-3 px-4 text-center text-sm">{{
                                                row.singleRoom }}</td>
                                            <td class="border border-gray-200 py-3 px-4 text-center text-sm">{{
                                                row.extraRoom }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <!-- 价格表为空时的提示 -->
                            <div v-else class="text-center py-8 text-gray-500">
                                <p>暂无价格信息</p>
                            </div>
                        </div>

                        <!-- 产品详情 -->
                        <div id="details" class="bg-white border border-gray-200 mb-4 p-4">
                            <div class="flex items-center mb-4">
                                <span class="text-lg font-bold text-gray-700">产品详情</span>
                            </div>

                            <div class="mb-5">
                                <h3 class="text-lg font-bold mb-4">{{ productDetails.title }}</h3>
                                <div class="text-sm text-gray-700 leading-relaxed mb-4" v-if="productDetails.content"
                                    v-html="productDetails.content"></div>
                                <!-- <img :src="product.cover" :alt="productDetails.title" class="w-full h-auto mb-4" v-if="product.cover"> -->
                            </div>


                            <div v-if="productDetails.travelMethods && productDetails.travelMethods.length > 0">
                                <div class="flex items-center mb-4">
                                    <span class="text-lg font-bold text-gray-700">旅行路线</span>
                                </div>
                                <div class="mb-4">
                                    <div v-for="(method, index) in productDetails.travelMethods" :key="index"
                                        class="text-sm mb-2 p-2 border-b border-gray-100 last:border-0">
                                        <div v-html="method"></div>
                                    </div>
                                </div>
                            </div>

                            <div v-if="productDetails.otherWays">
                                <div class="flex items-center mb-4">
                                    <div class="bg-red-600 w-5 h-5 flex-shrink-0 mr-2 flex items-center justify-center">
                                        <span class="text-white text-xs">其</span>
                                    </div>
                                    <span class="text-lg font-bold text-gray-700">其他方式</span>
                                </div>
                                <div class="mb-4">
                                    <div class="text-sm text-gray-700 leading-relaxed"
                                        v-html="productDetails.otherWays"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧推荐区域 -->
                    <div class="w-1/5">
                        <div class="py-0 px-4">
                            <h3 class="font-bold text-gray-800 pb-2 mb-4 border-b border-gray-200">热门推荐</h3>
                            <div class="space-y-4">
                                <div v-for="tour in recommendedTours" :key="tour.id"
                                    class="pb-4 border-b border-gray-100 last:border-0"
                                    @click="handleTourClick(tour.id)">
                                    <img :src="tour.image" :alt="tour.title"
                                        class="w-full h-[142px] object-cover mb-2 cursor-pointer">
                                    <h4 class="text-gray-700 text-sm font-medium mb-2 cursor-pointer">{{ tour.title }}
                                    </h4>
                                    <div class="flex justify-between items-center">
                                        <div>
                                            <span class="text-red-600 font-bold text-base">${{ tour.price }}</span>
                                            <span class="text-gray-400 line-through text-xs ml-1">${{ tour.originalPrice
                                                }}</span>
                                        </div>
                                        <div class="text-xs text-gray-500">
                                            <span class="text-gray-500">预定：</span>
                                            {{ tour.views || 0 }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用 TheFooter 组件 -->
        <TheFooter />

        <!-- 预定弹窗组件 -->
        <BookingModal v-model:visible="showBookingModal" :product-info="product" @success="handleBookingSuccess" />
    </div>
</template>

<style scoped>
/* 主体内容背景图片 */
.main-content-bg {
    background-image: url('@/assets/business/bg.PNG');
    background-size: 120% 180%;
    background-position: -200px;
    background-repeat: no-repeat;
    background-attachment: fixed;
    min-height: calc(100vh - 85px);
    /* 减去header的高度 */
}

.line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 产品图片悬停效果 */
img {
    transition: transform 0.2s ease, border-color 0.2s ease;
}

img:hover {
    transform: scale(1.02);
}

/* 自定义表格样式 */
table td {
    vertical-align: top;
}
</style>