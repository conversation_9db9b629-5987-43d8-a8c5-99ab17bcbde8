import { ModuleConfig } from '@cool-midway/core';

/**
 * 模块配置
 */
export default () => {
  return {
    name: '订单管理',
    description: '订单管理模块，包含预定订单和拼团订单的管理',
    middlewares: [],
    globalMiddlewares: [],
    order: 0,
    tasks: [
      {
        name: '定时任务测试',
        service: 'orderOrderService',
        method: 'testScheduleTask',
        cron: '0 * * * * *', // 每分钟的第0秒执行
        immediate: false,
        enable: true
      },
      {
        name: '更新过期拼团订单',
        service: 'orderOrderService',
        method: 'updateExpiredGroupOrders',
        cron: '0 0 1 * * *', // 每天凌晨1点执行
        immediate: false,
        enable: true
      },
      {
        name: '更新未成团过期订单',
        service: 'orderOrderService',
        method: 'updateUnfulfilledGroupOrders',
        cron: '0 30 1 * * *', // 每天凌晨1点30分执行
        immediate: false,
        enable: true
      }
    ]
  } as ModuleConfig;
};
