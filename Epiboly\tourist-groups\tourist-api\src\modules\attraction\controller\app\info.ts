import { Inject, Get, Query } from '@midwayjs/core';
import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core';
import { AttractionInfoEntity } from '../../entity/info';
import { AttractionInfoService } from '../../service/info';

/**
 * 成员信息app端
 */
@CoolController({
    prefix: '/app/attraction',
    api: [],
    entity: AttractionInfoEntity,
    service: AttractionInfoService,
})
export class AttractionInfoController extends BaseController {
    @Inject()
    attractionInfoService: AttractionInfoService;

    /**
     * 获取景点列表
     */
    @Get('/list', { summary: '获取景点列表' })
    async list() {
        const data = await this.attractionInfoService.attractionInfoEntity.find();

        return this.ok({
            attractions: data.map(item => ({
                id: item.id,
                name: item.name,
                pic: item.pic,
                introduce: item.introduce,
                isFeatured: item.isFeatured
            })),
            featureds: data.filter(item => item.isFeatured === 1).map(item => ({
                id: item.id,
                name: item.name,
                pic: item.pic,
                introduce: item.introduce,
                isFeatured: item.isFeatured
            }))
        });
    }
} 