<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const props = defineProps({
    isOpen: {
        type: Boolean,
        required: true
    }
});

const emit = defineEmits(['close']);

const name = ref('');
const phone = ref('');
const errorMessage = ref('');

const handleQuery = () => {
    if (!name.value || !phone.value) {
        errorMessage.value = '请输入姓名和手机号';
        return;
    }

    // 直接跳转到详情页，带上查询参数
    router.push({
        path: '/order-detail',
        query: {
            name: name.value,
            phone: phone.value
        }
    });

    // 关闭弹窗
    closeModal();
};

const closeModal = () => {
    name.value = '';
    phone.value = '';
    errorMessage.value = '';
    emit('close');
};
</script>

<template>
    <div v-if="isOpen" class="fixed inset-0 z-50 overflow-hidden" @click.self="closeModal">
        <!-- 背景遮罩 -->
        <div class="fixed inset-0 bg-black/70 backdrop-blur-sm transition-opacity"></div>

        <!-- 移动端样式 -->
        <div class="sm:hidden fixed inset-x-0 bottom-0 transform transition-transform duration-300 ease-out">
            <div class="bg-gray-900 rounded-t-[32px] shadow-xl">
                <!-- 顶部把手 -->
                <div class="flex justify-center pt-3 pb-2">
                    <div class="w-12 h-1.5 bg-gray-600 rounded-full"></div>
                </div>

                <!-- 标题和关闭按钮 -->
                <div class="px-6 pb-4 flex items-center justify-between">
                    <h3 class="text-2xl font-bold bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent">
                        查询订单
                    </h3>
                    <button @click="closeModal" class="p-2 text-gray-400 hover:text-white">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <!-- 错误信息 -->
                <div v-if="errorMessage" class="mx-6 mb-4">
                    <div class="flex items-center p-4 bg-red-500/10 border border-red-500/20 rounded-2xl">
                        <svg class="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span class="text-red-400">{{ errorMessage }}</span>
                    </div>
                </div>

                <!-- 表单 -->
                <div class="px-6 space-y-4">
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-300">姓名</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                            </div>
                            <input v-model="name" type="text" class="block w-full pl-12 pr-4 py-4 bg-gray-800/50 border border-gray-700 rounded-2xl text-white placeholder-gray-500 focus:ring-2 focus:border-transparent transition-colors text-base" placeholder="请输入姓名">
                        </div>
                    </div>

                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-300">手机号</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                </svg>
                            </div>
                            <input v-model="phone" type="tel" class="block w-full pl-12 pr-4 py-4 bg-gray-800/50 border border-gray-700 rounded-2xl text-white placeholder-gray-500 focus:ring-2 focus:border-transparent transition-colors text-base" placeholder="请输入手机号">
                        </div>
                    </div>
                </div>

                <!-- 查询按钮 -->
                <div class="p-6 pt-8">
                    <button @click="handleQuery"
                            class="w-full flex items-center justify-center py-4 px-6 rounded-2xl bg-gradient-to-r from-green-500 to-emerald-500 text-white font-semibold text-lg shadow-lg shadow-green-500/25 disabled:opacity-50 disabled:cursor-not-allowed transition-transform active:scale-[0.98]">
                        <span>立即查询</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 桌面端样式 -->
        <div class="hidden sm:flex items-center justify-center min-h-screen p-4">
            <div class="bg-gray-800/95 backdrop-blur-lg rounded-3xl w-full max-w-lg shadow-2xl border border-gray-700/50 transform transition-all duration-300 scale-95 opacity-0 animate-modal-in">
                <!-- 桌面端内容 -->
                <div class="p-8">
                    <div class="flex items-center justify-between mb-8">
                        <h3 class="text-2xl font-bold bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent">
                            查询订单
                        </h3>
                        <button @click="closeModal" class="p-2 text-gray-400 hover:text-white transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <!-- 错误信息 -->
                    <div v-if="errorMessage" class="mb-6">
                        <div class="flex items-center p-4 bg-red-500/10 border border-red-500/20 rounded-2xl">
                            <svg class="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span class="text-red-400">{{ errorMessage }}</span>
                        </div>
                    </div>

                    <!-- 表单 -->
                    <div class="space-y-6">
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-300">姓名</label>
                            <div class="relative group">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400 group-hover:text-green-400 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                                <input v-model="name" type="text" class="block w-full pl-12 pr-4 py-3.5 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-500 focus:ring-2 focus:border-transparent hover:border-green-500/50 transition-colors" placeholder="请输入姓名">
                            </div>
                        </div>
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-300">手机号</label>
                            <div class="relative group">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400 group-hover:text-green-400 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                    </svg>
                                </div>
                                <input v-model="phone" type="tel" class="block w-full pl-12 pr-4 py-3.5 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-500 focus:ring-2 focus:border-transparent hover:border-green-500/50 transition-colors" placeholder="请输入手机号">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 底部按钮 -->
                <div class="p-8 pt-4 flex justify-end space-x-4">
                    <button @click="closeModal" class="px-6 py-2.5 border border-gray-600 rounded-xl text-gray-300 hover:bg-gray-700/50 hover:border-gray-500 hover:text-white transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:ring-offset-gray-800">
                        取消
                    </button>
                    <button @click="handleQuery" class="px-6 py-2.5 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl hover:from-green-600 hover:to-emerald-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-gray-800">
                        <span>立即查询</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* 移动端输入框激活时的样式 */
@media (max-width: 640px) {
    input:focus {
        font-size: 16px; /* 防止 iOS 自动缩放 */
    }
}

/* 移动端动画 */
.transform {
    transform: translateY(100%);
}

.transform.transition-transform {
    transform: translateY(0);
}

/* 渐变文字 */
.bg-clip-text {
    -webkit-background-clip: text;
    background-clip: text;
}

/* 桌面端动画 */
@keyframes modalIn {
    from {
        transform: scale(0.95);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

.animate-modal-in {
    animation: modalIn 0.3s ease-out forwards;
}

/* 输入框组悬停效果 */
.group:hover .group-hover\:text-green-400 {
    color: rgb(74 222 128);
}
</style> 