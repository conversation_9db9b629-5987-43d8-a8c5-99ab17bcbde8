<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/logo.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"> 
    <title>海跃旅行</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>
  <script src="./translate.js"></script>
  <script>
    translate.selectLanguageTag.show = false;
    translate.language.setLocal('chinese_simplified'); //设置本地语种为中文（当前网页的语种）
    translate.service.use('client.edge'); //设置机器翻译服务通道，直接客户端本身，不依赖服务端
    
    // 启用本地语种翻译，这样中文环境下也会翻译页面中的其他语言
    translate.language.translateLocal = true;
    
    // 设置默认翻译为俄语
    translate.language.setDefaultTo('russian');
    
    // setTimeout(() => {
    //   translate.execute();
    // },50);
  </script>
</html>
