<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />
			<cl-flex1 />
			<!-- 条件搜索 -->
			<cl-search ref="Search" />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
	</cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
	name: "site-company",
});

import { useCrud, useTable, useUpsert, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useI18n } from "vue-i18n";
import { ElMessage, ElMessageBox } from "element-plus";

const { service } = useCool();
const { t } = useI18n();

// cl-upsert
const Upsert = useUpsert({
	items: [
		{
			label: t("公司名称"),
			prop: "name",
			component: { name: "el-input", props: { clearable: true } },
			required: true,
		},
		{
			label: t("公司照片"),
			prop: "image",
			component: { name: "cl-upload" },
		},
		{
			label: t("公司地址"),
			prop: "address",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("公司电话"),
			prop: "phone",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("公司邮箱"),
			prop: "email",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("联系人"),
			prop: "contactPerson",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("公司描述"),
			prop: "description",
			component: { name: "el-input", props: { type: "textarea", rows: 4 } },
		},
		{
			label: t("状态"),
			prop: "status",
			component: {
				name: "el-radio-group",
				options: [
					{ label: t("启用"), value: 1 },
					{ label: t("禁用"), value: 0 },
				]
			},
			value: 1,
			span: 12,
		},
		{
			label: t("排序"),
			prop: "sort",
			component: { name: "el-input-number", props: { min: 0 } },
			value: 0,
			span: 12,
		},
	],
});

// cl-table
const Table = useTable({
	columns: [
		{ type: "selection" },
		{
			label: t("公司名称"),
			prop: "name",
			minWidth: 150,
		},
		{
			label: t("公司照片"),
			prop: "image",
			minWidth: 100,
			component: { name: "cl-image", props: { size: 60 } },
		},
		{ label: t("公司地址"), prop: "address", minWidth: 200 },
		{ label: t("公司电话"), prop: "phone", minWidth: 140 },
		{ label: t("公司邮箱"), prop: "email", minWidth: 180 },
		{ label: t("联系人"), prop: "contactPerson", minWidth: 120 },
		{
			label: t("总公司"),
			prop: "isHeadOffice",
			minWidth: 100,
			dict: [
				{ label: t("是"), value: 1, color: "success" },
				{ label: t("否"), value: 0, color: "info" },
			],
		},
		{
			label: t("状态"),
			prop: "status",
			minWidth: 100,
			dict: [
				{ label: t("启用"), value: 1, color: "success" },
				{ label: t("禁用"), value: 0, color: "danger" },
			],
		},
		{ label: t("排序"), prop: "sort", minWidth: 80 },
		{
			label: t("创建时间"),
			prop: "createTime",
			minWidth: 170,
			sortable: "desc",
			component: { name: "cl-date-text" },
		},
		{
			type: "op",
			buttons({ scope }: any) {
				return [
					"edit", 
					"delete", 
                    {
                        hidden: scope.row.isHeadOffice === 1,
						label: scope.row.isHeadOffice ? t("已是总公司") : t("设为总公司"),
						type: "warning",
						disabled: scope.row.isHeadOffice,
						onClick() {
							setHeadOffice(scope.row);
						}
					}
				];
			},
		},
	],
});

// cl-search
const Search = useSearch({
	items: [
	],
});

// cl-crud
const Crud = useCrud(
	{
		service: service.site.company,
	},
	(app) => {
		app.refresh();
	},
);

// 设置总公司
async function setHeadOffice(row: any) {
	try {
		await ElMessageBox.confirm(
			t("确定要将该公司设为总公司吗？设置后其他公司的总公司标识将被取消。"),
			t("提示"),
			{
				confirmButtonText: t("确定"),
				cancelButtonText: t("取消"),
				type: "warning",
			}
		);

		await service.site.company.request({
			url: "/setHeadOffice",
			method: "POST",
			data: { id: row.id },
		});

		ElMessage.success(t("设置成功"));
		refresh();
	} catch (error) {
		// 用户取消操作
	}
}

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}
</script> 