import { Provide } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { AdvertSpaceEntity } from '../entity/space';

/**
 * 广告位信息
 */
@Provide()
export class AdvertSpaceService extends BaseService {
  @InjectEntityModel(AdvertSpaceEntity)
  advertSpaceEntity: Repository<AdvertSpaceEntity>;
}
