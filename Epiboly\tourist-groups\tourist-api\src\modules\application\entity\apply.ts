import { BaseEntity } from '../../base/entity/base';
import { Column, Entity, Index } from 'typeorm';

/**
 * 申请信息
 */
@Entity('application_apply')
export class ApplicationApplyEntity extends BaseEntity {
  @Column({
    comment: '公司性质',
    dict: ['企业', '个体工商户', '个人'],
    default: 0,
  })
  companyType: number;

  @Column({ comment: '公司名称', nullable: true })
  companyAddress: string;

  @Column({ comment: '国家', nullable: true })
  country: string;

  @Column({ comment: '地区', nullable: true })
  region: string;

  @Column({ comment: '联系人', nullable: true })
  contactPerson: string;

  @Column({ comment: '联系电话', nullable: true })
  contactPhone: string;

  @Column({ comment: '电子邮箱', nullable: true })
  email: string;

  @Column({ comment: 'WhatsApp账号', nullable: true })
  whatsApp: string;

  @Column({ comment: '营业执照', nullable: true })
  businessLicense: string;

  @Column({
    comment: '状态',
    dict: ['未分配账号', '已分配账号'],
    default: 0
  })
  status: number;

  @Column({ comment: '关联用户ID', nullable: true })
  userId: number;
}
