import { ref } from 'vue';

// 事件总线，用于组件间通信
class EventBus {
  constructor() {
    this.events = {};
  }

  // 监听事件
  on(event, callback) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  }

  // 取消监听事件
  off(event, callback) {
    if (!this.events[event]) return;
    
    if (callback) {
      const index = this.events[event].indexOf(callback);
      if (index > -1) {
        this.events[event].splice(index, 1);
      }
    } else {
      this.events[event] = [];
    }
  }

  // 触发事件
  emit(event, ...args) {
    if (!this.events[event]) return;
    
    this.events[event].forEach(callback => {
      callback(...args);
    });
  }
}

// 创建全局事件总线实例
export const eventBus = new EventBus();

// 事件名称常量
export const EVENTS = {
  UNREAD_COUNT_CHANGED: 'unread-count-changed'
}; 