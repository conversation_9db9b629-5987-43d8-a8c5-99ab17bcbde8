{"搜索插件名称": "Search Plugin Name", "后端": "Backend", "未知": "Unknown", "暂无描述": "No description yet", "示例": "Example", "预览": "Preview", "文档": "Documentation", "图片预览": "Image Preview", "说明文档": "Instruction Document", "作者": "Author", "更新时间": "Update Time", "格式化": "Format", "插入文件链接": "Insert File Link", "已安装": "Installed", "全部插件": "All Plugins", "插件开发": "Plugin Development", "插件安装成功": "Plugin Installed Successfully", "提示": "Tip", "继续": "Continue", "确定要卸载插件【{name}】吗？": "Are you sure you want to uninstall the plugin [{name}]?", "卸载成功": "Uninstall success", "启用成功": "Enable success", "禁用成功": "Disable success", "参数格式错误": "Parameter format error", "设置": "Settings", "参数": "Parameter", "修改成功": "Modify success", "检测到插件，是否安装": "Plugin detected. Install?", "安装": "Install", "确定要退出编码吗？": "Are you sure you want to exit encoding?", "创建目录：{name}": "Create directory: {name}", "创建菜单：{name}": "Create menu: {name}", "创建 Node 文件": "Create Node file", "正在重启服务": "Restarting service", "创建 Vue 文件": "Create Vue file", "自动添加": "Auto add", "权限名称": "Permission name", "实体数据": "Entity data", "自动添加权限": "Auto add permission", "一键添加": "Add in one click", "权限列表": "Permission List", "请选择实体数据": "Please Select Entity Data", "请填写权限名称": "Please Fill in Permission Name", "请至少选择一个权限": "Please Select at Least One Permission", "添加权限成功": "Permission Added Successfully", "快速创建": "Quick Create", "请选择数据结构": "Please Select Data Structure", "数据结构": "Data Structure", "上级节点": "<PERSON><PERSON>", "请选择上级节点": "Please Select Parent Node", "菜单名称": "<PERSON>u Name", "请输入菜单名称": "Please Enter Menu Name", "菜单路由": "Menu Route", "请输入菜单路由，如：/test": "Please Enter Menu Route, e.g.: /test", "必须以 / 开头": "Must Start with /", "菜单排序": "<PERSON>u Sort", "请填写菜单排序": "Please Fill in Menu Sort", "菜单图标": "Menu Icon", "请选择图标": "Please Select Icon", "路由缓存": "Route Cache", "开始创建": "Start Creation", "极速编码": "Fast Encoding"}