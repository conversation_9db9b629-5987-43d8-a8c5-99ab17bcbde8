import { Provide, Inject } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { MessageNotificationEntity } from '../entity/notification';
import { BaseSysUserService } from '../../base/service/sys/user';
import { UserInfoEntity } from '../../user/entity/info';

/**
 * 通知消息服务
 */
@Provide()
export class MessageNotificationService extends BaseService {
    @InjectEntityModel(MessageNotificationEntity)
    messageNotificationEntity: Repository<MessageNotificationEntity>;

    @InjectEntityModel(UserInfoEntity)
    userInfoEntity: Repository<UserInfoEntity>;

    @Inject()
    baseSysUserService: BaseSysUserService;

    /**
     * 获取用户的未读消息数量
     */
    async getUnreadCount(userId: number) {
        return await this.messageNotificationEntity.count({
            where: {
                receiverId: userId,
                isRead: false,
                status: 1
            }
        });
    }

    /**
     * 标记消息为已读
     */
    async markAsRead(id: number, userId?: number) {
        const where: any = { id };
        if (userId) {
            where.receiverId = userId;
        }
        return await this.messageNotificationEntity.update(where, {
            isRead: true
        });
    }

    /**
     * 批量标记消息为已读
     */
    async markMultipleAsRead(ids: number[], userId?: number) {
        const where: any = { id: { $in: ids } };
        if (userId) {
            where.receiverId = userId;
        }
        return await this.messageNotificationEntity.update(where, {
            isRead: true
        });
    }

    /**
     * 发送系统消息
     */
    async sendSystemMessage(data: {
        title: string;
        content: string;
        receiverId?: number;
        senderId?: number;
        priority?: number;
        sendToAll?: boolean;
        sendToAdmin?: boolean;
        receiverCount?: number;
        senderRemark?: string;
    }) {
        console.log('MessageNotificationService.sendSystemMessage 被调用:', data);

        const results = [];

        if (data.sendToAll) {
            // 发送给所有前端用户 - 查询user_info表
            console.log('获取所有前端用户列表...');
            const allUsers = await this.userInfoEntity.find({
                where: { status: 1 } // 只获取正常状态的用户
            });

            console.log(`找到 ${allUsers.length} 个前端用户`);

            for (const user of allUsers) {
                const notification = new MessageNotificationEntity();
                notification.title = data.title;
                notification.content = data.content;
                notification.type = 0; // 系统消息
                notification.receiverId = user.id;
                notification.senderId = data.senderId;
                notification.priority = data.priority || 0;
                notification.publishTime = new Date();
                notification.status = 1;
                notification.isRead = false;
                notification.sendStatus = 1; // 已发送
                notification.sendToAll = true;
                notification.sendToAdmin = false;
                notification.receiverCount = allUsers.length;
                notification.senderRemark = data.senderRemark;

                const result = await this.messageNotificationEntity.save(notification);
                results.push(result);
            }

            console.log(`为 ${results.length} 个前端用户创建了消息记录`);

            // 返回第一条记录，但设置正确的接收人数
            if (results.length > 0) {
                results[0].receiverCount = results.length;
                return results[0];
            }
        } else if (data.sendToAdmin) {
            // 发送给所有管理员 - 查询base_sys_user表
            console.log('获取所有管理员列表...');
            const allAdmins = await this.baseSysUserService.page({
                page: 1,
                size: 1000,
                status: 1
            });

            const adminList = allAdmins.list || [];
            console.log(`找到 ${adminList.length} 个管理员`);

            for (const admin of adminList) {
                const notification = new MessageNotificationEntity();
                notification.title = data.title;
                notification.content = data.content;
                notification.type = 0; // 系统消息
                notification.receiverId = admin.id;
                notification.senderId = data.senderId;
                notification.priority = data.priority || 0;
                notification.publishTime = new Date();
                notification.status = 1;
                notification.isRead = false;
                notification.sendStatus = 1; // 已发送
                notification.sendToAll = false;
                notification.sendToAdmin = true;
                notification.receiverCount = adminList.length;
                notification.senderRemark = data.senderRemark;

                const result = await this.messageNotificationEntity.save(notification);
                results.push(result);
            }

            console.log(`为 ${results.length} 个管理员创建了消息记录`);

            if (results.length > 0) {
                results[0].receiverCount = results.length;
                return results[0];
            }
        } else {
            // 发送给指定用户
            console.log('发送给指定用户:', data.receiverId);
            const notification = new MessageNotificationEntity();
            notification.title = data.title;
            notification.content = data.content;
            notification.type = 0; // 系统消息
            notification.receiverId = data.receiverId;
            notification.senderId = data.senderId;
            notification.priority = data.priority || 0;
            notification.publishTime = new Date();
            notification.status = 1;
            notification.isRead = false;
            notification.sendStatus = 1; // 已发送
            notification.sendToAll = false;
            notification.sendToAdmin = false;
            notification.receiverCount = 1;
            notification.senderRemark = data.senderRemark;

            return await this.messageNotificationEntity.save(notification);
        }

        // 如果没有用户，返回一个默认记录
        const notification = new MessageNotificationEntity();
        notification.title = data.title;
        notification.content = data.content;
        notification.type = 0;
        notification.receiverId = null;
        notification.senderId = data.senderId;
        notification.priority = data.priority || 0;
        notification.publishTime = new Date();
        notification.status = 1;
        notification.isRead = false;
        notification.sendStatus = 1;
        notification.sendToAll = data.sendToAll || false;
        notification.sendToAdmin = data.sendToAdmin || false;
        notification.receiverCount = 0;
        notification.senderRemark = data.senderRemark;

        return await this.messageNotificationEntity.save(notification);
    }

    /**
     * 发布公告
     */
    async publishAnnouncement(data: {
        title: string;
        content: string;
        senderId?: number;
        priority?: number;
        publishTime?: Date;
        senderRemark?: string;
    }) {
        console.log('发布公告给所有前端用户...');

        // 获取所有前端用户
        const allUsers = await this.userInfoEntity.find({
            where: { status: 1 } // 只获取正常状态的用户
        });

        console.log(`找到 ${allUsers.length} 个前端用户`);

        const results = [];

        for (const user of allUsers) {
            const notification = new MessageNotificationEntity();
            notification.title = data.title;
            notification.content = data.content;
            notification.type = 1; // 公告
            notification.receiverId = user.id;
            notification.senderId = data.senderId;
            notification.priority = data.priority || 0;
            notification.publishTime = data.publishTime || new Date();
            notification.status = 1;
            notification.isRead = false;
            notification.sendStatus = 1; // 已发送
            notification.sendToAll = true; // 公告默认发给所有用户
            notification.senderRemark = data.senderRemark;

            const result = await this.messageNotificationEntity.save(notification);
            results.push(result);
        }

        console.log(`为 ${results.length} 个前端用户创建了公告记录`);

        if (results.length > 0) {
            results[0].receiverCount = results.length;
            return results[0];
        }

        // 如果没有用户，返回一个默认记录
        const notification = new MessageNotificationEntity();
        notification.title = data.title;
        notification.content = data.content;
        notification.type = 1;
        notification.receiverId = null;
        notification.senderId = data.senderId;
        notification.priority = data.priority || 0;
        notification.publishTime = data.publishTime || new Date();
        notification.status = 1;
        notification.isRead = false;
        notification.sendStatus = 1;
        notification.sendToAll = true;
        notification.receiverCount = 0;
        notification.senderRemark = data.senderRemark;

        return await this.messageNotificationEntity.save(notification);
    }

    /**
     * 获取用户消息列表
     */
    async getUserMessages(userId: number, type?: number, page: number = 1, limit: number = 10) {
        const queryBuilder = this.messageNotificationEntity.createQueryBuilder('message')
            .leftJoin('base_sys_user', 'sender', 'message.senderId = sender.id')
            .select([
                'message.*',
                'sender.name as senderName'
            ])
            .where('message.status = :status', { status: 1 })
            .andWhere('message.receiverId = :userId', { userId });

        if (type !== undefined) {
            queryBuilder.andWhere('message.type = :type', { type });
        }

        queryBuilder.orderBy('message.priority', 'DESC')
            .addOrderBy('message.publishTime', 'DESC')
            .offset((page - 1) * limit)
            .limit(limit);

        return await queryBuilder.getRawMany();
    }
} 