import { BaseEntity } from '../../base/entity/base';
import { Column, Entity, Index } from 'typeorm';

/**
 * 轮播图信息
 */
@Entity('banner_info')
export class BannerInfoEntity extends BaseEntity {
  @Column({ comment: '图片', type: 'json' })
  pics: string[];

  @Column({ comment: '位置', dict: ['首页', '业务范围', '合作协议', '联系我们', '关于我们'], default: 0 })
  position: number;

  @Column({ comment: '状态', dict: ['禁用', '启用'], default: 1 })
  status: number;

  @Column({ comment: '备注', nullable: true, type: 'text' })
  remark: string;
}
