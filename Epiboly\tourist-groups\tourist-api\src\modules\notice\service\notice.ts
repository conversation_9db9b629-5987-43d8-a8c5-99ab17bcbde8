import { Provide, Inject } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { NoticeNoticeEntity } from '../entity/notice';
import { BaseSysUserService } from '../../base/service/sys/user';
import { MessageNotificationService } from '../../message/service/notification';

/**
 * 通知信息
 */
@Provide()
export class NoticeNoticeService extends BaseService {
    @InjectEntityModel(NoticeNoticeEntity)
    noticeNoticeEntity: Repository<NoticeNoticeEntity>;

    @Inject()
    baseSysUserService: BaseSysUserService;

    @Inject()
    messageNotificationService: MessageNotificationService;

    /**
     * 批量发送通知
     */
    async batchSend(params: any) {
        const {
            title,
            content,
            type = 0,
            publishTime,
            senderId,
            senderName,
            senderRemark,
            receiverType = 'all',
            receiverIds = [],
            sendToAll = false,
            sendToAdmin = false
        } = params;

        // 获取接收人数量
        let receiverCount = 0;
        let finalReceiverIds = receiverIds;

        if (sendToAll) {
            // 获取所有普通用户
            const allUsers = await this.baseSysUserService.page({
                page: 1,
                size: 1000,
                status: 1
            });
            receiverCount = allUsers.list ? allUsers.list.length : 0;
            finalReceiverIds = allUsers.list ? allUsers.list.map(user => user.id) : [];
        } else if (sendToAdmin) {
            // 获取所有管理员
            const allAdmins = await this.baseSysUserService.page({
                page: 1,
                size: 1000,
                status: 1
            });
            receiverCount = allAdmins.list ? allAdmins.list.length : 0;
            finalReceiverIds = allAdmins.list ? allAdmins.list.map(admin => admin.id) : [];
        } else if (receiverIds && receiverIds.length > 0) {
            // 指定用户
            receiverCount = receiverIds.length;
            finalReceiverIds = receiverIds;
        } else {
            receiverCount = 0;
            finalReceiverIds = [];
        }

        // 创建通知记录
        const notice = await this.noticeNoticeEntity.save({
            title,
            content,
            type,
            publishTime: publishTime || new Date(),
            senderId,
            senderRemark,
            receiverType,
            receiverIds: finalReceiverIds,
            receiverCount,
            sendToAll,
            sendToAdmin,
            sendStatus: 1, // 已发送
            sendTime: new Date()
        });

        // 真正发送消息到用户 - 使用message模块
        try {
            console.log('开始发送消息到message模块...');
            console.log('消息数据:', { title, content, type, sendToAll, sendToAdmin, finalReceiverIds });

            let actualReceiverCount = 0;
            let messageResult = null;

            if (type === 1) {
                // 公告类型
                console.log('发送公告...');
                messageResult = await this.messageNotificationService.publishAnnouncement({
                    title,
                    content,
                    senderId,
                    priority: 0,
                    publishTime: publishTime ? new Date(publishTime) : new Date(),
                    senderRemark
                });
                console.log('公告发送结果:', messageResult);
                actualReceiverCount = messageResult.receiverCount || 0;
            } else {
                // 系统消息类型
                console.log('发送系统消息...');
                if (sendToAll) {
                    // 发送给所有用户的全局消息
                    console.log('发送给所有用户...');
                    messageResult = await this.messageNotificationService.sendSystemMessage({
                        title,
                        content,
                        senderId,
                        priority: 0,
                        sendToAll: true,
                        receiverCount,
                        senderRemark
                    });
                    console.log('全局消息发送结果:', messageResult);
                    actualReceiverCount = messageResult.receiverCount || 0;
                } else if (sendToAdmin) {
                    // 发送给管理员
                    console.log('发送给管理员...');
                    messageResult = await this.messageNotificationService.sendSystemMessage({
                        title,
                        content,
                        senderId,
                        priority: 0,
                        sendToAdmin: true,
                        receiverCount,
                        senderRemark
                    });
                    console.log('管理员消息发送结果:', messageResult);
                    actualReceiverCount = messageResult.receiverCount || 0;
                } else if (finalReceiverIds && finalReceiverIds.length > 0) {
                    // 给指定用户发送消息
                    console.log(`发送给指定用户，用户数量: ${finalReceiverIds.length}`);
                    actualReceiverCount = finalReceiverIds.length;
                    for (const receiverId of finalReceiverIds) {
                        console.log(`发送给用户ID: ${receiverId}`);
                        const result = await this.messageNotificationService.sendSystemMessage({
                            title,
                            content,
                            receiverId,
                            senderId,
                            priority: 0,
                            senderRemark
                        });
                        console.log(`用户${receiverId}消息发送结果:`, result);
                    }
                }
            }

            // 更新notice记录的实际接收人数
            await this.noticeNoticeEntity.update(notice.id, {
                receiverCount: actualReceiverCount
            });

            // 更新返回的notice对象
            notice.receiverCount = actualReceiverCount;

            console.log(`通知发送成功: ${title}, 实际接收人数: ${actualReceiverCount}`);
        } catch (error) {
            // 如果消息发送失败，更新notice状态
            await this.noticeNoticeEntity.update(notice.id, {
                sendStatus: 2, // 发送失败
                failReason: error.message
            });
            throw error;
        }

        return notice;
    }

    /**
     * 重新发送通知
     */
    async resend(id: number) {
        const notice = await this.noticeNoticeEntity.findOne({ where: { id } });
        if (!notice) {
            throw new Error('通知不存在');
        }

        try {
            // 重新发送到message模块
            if (notice.type === 1) {
                // 公告类型
                await this.messageNotificationService.publishAnnouncement({
                    title: notice.title,
                    content: notice.content,
                    senderId: notice.senderId,
                    priority: 0,
                    publishTime: new Date(),
                    senderRemark: notice.senderRemark
                });
            } else {
                // 系统消息类型
                if (notice.sendToAll) {
                    await this.messageNotificationService.sendSystemMessage({
                        title: notice.title,
                        content: notice.content,
                        senderId: notice.senderId,
                        priority: 0,
                        sendToAll: true,
                        receiverCount: notice.receiverCount,
                        senderRemark: notice.senderRemark
                    });
                } else if (notice.sendToAdmin) {
                    await this.messageNotificationService.sendSystemMessage({
                        title: notice.title,
                        content: notice.content,
                        senderId: notice.senderId,
                        priority: 0,
                        sendToAdmin: true,
                        receiverCount: notice.receiverCount,
                        senderRemark: notice.senderRemark
                    });
                } else if (notice.receiverIds && notice.receiverIds.length > 0) {
                    for (const receiverId of notice.receiverIds) {
                        await this.messageNotificationService.sendSystemMessage({
                            title: notice.title,
                            content: notice.content,
                            receiverId,
                            senderId: notice.senderId,
                            priority: 0,
                            senderRemark: notice.senderRemark
                        });
                    }
                }
            }

            // 更新发送状态
            await this.noticeNoticeEntity.update(id, {
                sendStatus: 1, // 已发送
                sendTime: new Date(),
                failReason: null
            });

            console.log(`通知重新发送成功: ${notice.title}`);
        } catch (error) {
            // 发送失败
            await this.noticeNoticeEntity.update(id, {
                sendStatus: 2, // 发送失败
                failReason: error.message
            });
            throw error;
        }

        return { message: '重新发送成功' };
    }

    /**
     * 获取发送详情
     */
    async getSendDetail(id: number) {
        const notice = await this.noticeNoticeEntity.findOne({
            where: { id: id }
        });

        if (!notice) {
            throw new Error('通知不存在');
        }

        // 获取接收人详细信息
        let receivers = [];
        if (notice.receiverIds && notice.receiverIds.length > 0) {
            // 使用page方法获取用户信息
            const usersResult = await this.baseSysUserService.page({
                page: 1,
                size: notice.receiverIds.length,
                // 这里需要根据实际的查询参数格式来调整
            });
            receivers = usersResult.list || [];
            // 过滤出指定ID的用户
            receivers = receivers.filter(user => notice.receiverIds.includes(user.id));
        }

        return {
            notice,
            receivers
        };
    }
}
