/**
 * 翻译工具类
 * 统一处理页面翻译相关的逻辑
 */

import { setupRussianTranslations } from './russianTranslations.js';

// 延迟执行翻译，避免频繁调用
let translateTimer = null;

/**
 * 执行翻译
 * @param {number} delay 延迟时间，默认50ms
 */
export const executeTranslate = (delay = 50) => {
  // 清除之前的定时器
  if (translateTimer) {
    clearTimeout(translateTimer);
  }
  
  translateTimer = setTimeout(() => {
    if (window.translate && window.translate.execute) {
      try {
        translate.execute();
        console.log('翻译已执行');
      } catch (error) {
        console.error('翻译执行失败:', error);
      }
    } else {
      console.warn('translate 对象未加载');
    }
  }, delay);
};

/**
 * 创建请求状态追踪器
 * @param {Array} requestNames 请求名称数组
 * @returns {Object} 包含状态和检查函数的对象
 */
export const createRequestTracker = (requestNames) => {
  // 创建状态对象
  const requestStatus = {};
  requestNames.forEach(name => {
    requestStatus[name] = false;
  });
  
  // 创建检查函数
  const checkAllRequestsComplete = () => {
    const allComplete = Object.values(requestStatus).every(status => status === true);
    if (allComplete) {
      executeTranslate();
    }
  };
  
  // 标记请求完成的函数
  const markRequestComplete = (requestName) => {
    if (requestStatus.hasOwnProperty(requestName)) {
      requestStatus[requestName] = true;
      checkAllRequestsComplete();
    }
  };
  
  return {
    requestStatus,
    checkAllRequestsComplete,
    markRequestComplete
  };
};

/**
 * DOM 监听器，监听DOM变化自动执行翻译
 */
export const setupDOMTranslateListener = () => {
  // 设置 MutationObserver 监听 DOM 变化
  const observer = new MutationObserver((mutations) => {
    let shouldTranslate = false;
    
    mutations.forEach((mutation) => {
      // 检查是否有新增的文本节点或元素
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        for (let node of mutation.addedNodes) {
          if (node.nodeType === Node.TEXT_NODE || 
              (node.nodeType === Node.ELEMENT_NODE && node.innerText && node.innerText.trim())) {
            shouldTranslate = true;
            break;
          }
        }
      }
      // 检查是否有文本内容变化
      if (mutation.type === 'characterData') {
        shouldTranslate = true;
      }
    });
    
    if (shouldTranslate) {
      executeTranslate(200); // 稍微延长延迟时间，避免频繁触发
    }
  });
  
  // 开始监听
  observer.observe(document.body, {
    childList: true,
    subtree: true,
    characterData: true
  });
  
  return observer;
};

/**
 * 等待 translate.js 加载完成
 * @returns {Promise}
 */
export const waitForTranslateReady = () => {
  return new Promise((resolve) => {
    const checkTranslate = () => {
      if (typeof window.translate !== 'undefined') {
        resolve();
      } else {
        setTimeout(checkTranslate, 100);
      }
    };
    checkTranslate();
  });
};

/**
 * 强制重新翻译页面（清除缓存）
 */
export const forceRetranslate = () => {
  if (window.translate) {
    // 清除翻译历史缓存
    if (window.translate.nodeHistory) {
      window.translate.nodeHistory = {};
    }
    executeTranslate();
  }
};

/**
 * 添加俄语词汇的自定义翻译
 * 解决俄语词汇无法正确翻译的问题
 */
export const addRussianTranslations = () => {
  // 使用专门的俄语翻译配置
  setupRussianTranslations();
  
  // 备用方案：手动配置（防止模块加载失败）
  if (window.translate && window.translate.nomenclature) {
    try {
      // 核心俄语词汇的快速配置（俄语→中文）
      const coreRussianToChinese = `
      Культурно-спортивные мероприятия=文化体育活动
      Культурно-спортивный=文化体育
      мероприятия=活动
      спортивные=体育
      культурные=文化
      Главная страница=首页
      Подробная информация=了解详情
      Горячие предложения=热门产品
      Все=全部
      Знакомство с компанией=公司介绍
      Связаться с нами=联系我们
      `;

      // 核心中文词汇的快速配置（中文→俄语）
      const coreChineseToRussian = `
      首页=Главная страница
      了解详情=Подробная информация
      热门产品=Горячие предложения
      全部=Все
      公司介绍=Знакомство с компанией
      联系我们=Связаться с нами
      产品详情=Детали тура
      预定=Забронировать
      咨询=Консультация
      `;

      const coreRussianToEnglish = `
      Культурно-спортивные мероприятия=Cultural and Sports Events
      Культурно-спортивный=Cultural and Sports
      мероприятия=events
      мероприятие=event
      спортивные=sports
      культурные=cultural
      `;
      
      // 添加核心翻译（备用）
      window.translate.nomenclature.append('russian', 'chinese_simplified', coreRussianToChinese);
      window.translate.nomenclature.append('chinese_simplified', 'russian', coreChineseToRussian);
      window.translate.nomenclature.append('russian', 'english', coreRussianToEnglish);
      
      console.log('✅ 俄语核心词汇翻译已配置（包含中俄双向翻译）');
      
    } catch (error) {
      console.error('❌ 俄语翻译备用配置失败:', error);
    }
  }
}; 