<script setup>
import { ref, onMounted, computed, reactive } from 'vue';
import TheHeader from '../components/TheHeader.vue';
import TheFooter from '../components/TheFooter.vue';
import banner1Img from '@/assets/common/banner.png';
import bgImg from '@/assets/about/bg.png';
import bg2Img from '@/assets/about/bg2.png';
import usImg from '@/assets/about/us2.png';
import wyImg from '@/assets/about/wy2.png';
import api from '@/api';

// 页面标题和描述
const pageTitle = ref('');
const pageDescription = ref(``);
const componyDetail = ref({})
const currentLanguage = ref('chinese_simplified');

// 企业文化文本
const cultureTxt = ref('');

// 定制师成员数据
const customDesigners = ref([]);

const bannerImages = ref([]);

// 导游成员数据
const tourGuides = ref([]);

// 加载状态
const loading = ref(false);
const bannerLoading = ref(false);
const cultureLoading = ref(false);

// 请求完成状态追踪
const requestStatus = reactive({
  banner: false,
  member: false,
  culture: false
});

// 分页相关
const itemsPerPage = 15; // 每页显示15个成员
const currentCustomPage = ref(1);
const currentGuidePage = ref(1);

// 计算分页后的成员数据
const paginatedCustomDesigners = computed(() => {
  const start = (currentCustomPage.value - 1) * itemsPerPage;
  const end = start + itemsPerPage;
  return customDesigners.value.slice(start, end);
});

const paginatedTourGuides = computed(() => {
  const start = (currentGuidePage.value - 1) * itemsPerPage;
  const end = start + itemsPerPage;
  return tourGuides.value.slice(start, end);
});

// 计算总页数
const totalCustomPages = computed(() => {
  return Math.ceil(customDesigners.value.length / itemsPerPage);
});

const totalGuidePages = computed(() => {
  return Math.ceil(tourGuides.value.length / itemsPerPage);
});

// 判断企业文化文本是否需要居中显示
const shouldCenterCultureText = computed(() => {
  // 如果文字长度小于200个字符，则居中显示
  return cultureTxt.value.length < 400;
});

// 分页控制函数
const prevCustomPage = () => {
  if (currentCustomPage.value > 1) {
    currentCustomPage.value--;
  }
};

const nextCustomPage = () => {
  if (currentCustomPage.value < totalCustomPages.value) {
    currentCustomPage.value++;
  }
};

const prevGuidePage = () => {
  if (currentGuidePage.value > 1) {
    currentGuidePage.value--;
  }
};

const nextGuidePage = () => {
  if (currentGuidePage.value < totalGuidePages.value) {
    currentGuidePage.value++;
  }
};

// 检查所有请求是否完成并执行翻译
const checkAllRequestsComplete = () => {
  if (requestStatus.banner && requestStatus.member && requestStatus.culture) {
    // 所有请求完成后执行翻译
    if (window.translate && window.translate.execute) {
      window.translate.execute();
    }
  }
};

// 获取Banner数据
const getBannerData = async () => {
  try {
    bannerLoading.value = true;
    const response = await api.commonApi.getBannerList({ position: 1 });
    if (response.code === 1000 && response.data) {
      if (response.data.pics && response.data.pics.length > 0) {
        bannerImages.value = response.data.pics.map(pic => ({
          src: pic,
          link: response.data.path || '#'
        }));
      } else {
        // 如果没有获取到图片，使用默认图片
        bannerImages.value = [
          {
            src: banner1Img,
            link: '#'
          }
        ];
      }
    } else {
      // API调用失败，使用默认图片
      bannerImages.value = [
        {
          src: banner1Img,
          link: '#'
        }
      ];
    }
  } catch (error) {
    console.error('获取Banner数据失败:', error);
    // 发生错误时使用默认图片
    bannerImages.value = [
      {
        src: banner1Img,
        link: '#'
      }
    ];
  } finally {
    bannerLoading.value = false;
    requestStatus.banner = true;
    checkAllRequestsComplete();
  }
};

// 获取成员数据
const getMemberData = async () => {
  try {
    loading.value = true;
    const response = await api.memberApi.getMemberList();
    if (response.code === 1000) {
      customDesigners.value = response.data.customDesigners || [];
      tourGuides.value = response.data.tourGuides || [];
    } else {
      console.error('获取成员数据失败:', response.message);
    }
  } catch (error) {
    console.error('获取成员数据出错:', error);
  } finally {
    loading.value = false;
    requestStatus.member = true;
    checkAllRequestsComplete();
  }
};

// 获取企业文化信息
const getCultureInfo = async () => {
  try {
    cultureLoading.value = true;
    const response = await api.cultureApi.getCultureInfo();
    if (response.code === 1000 && response.data) {
      const data = response.data;
      componyDetail.value = data;
      pageTitle.value = data.name || '姓名';
      pageDescription.value = data.mainIntroduction;
      cultureTxt.value = data.enterpriseCulture;
    } else {
      console.error('获取企业文化信息失败:', response.message);
    }
  } catch (error) {
    console.error('获取企业文化信息出错:', error);
  } finally {
    cultureLoading.value = false;
    requestStatus.culture = true;
    checkAllRequestsComplete();
  }
};

// 页面加载时获取数据
onMounted(() => {
  setTimeout(() => {
    currentLanguage.value = localStorage.getItem('to') || 'chinese_simplified';
  }, 200);
  setTimeout(() => {
    currentLanguage.value = localStorage.getItem('to') || 'chinese_simplified';
  }, 800);
  setTimeout(() => {
    currentLanguage.value = localStorage.getItem('to') || 'chinese_simplified';
  }, 2000);
  getBannerData();
  getMemberData();
  getCultureInfo();
});

// 处理图片加载错误
const handleImageError = (event) => {
  event.target.src = 'https://img0.baidu.com/it/u=3715720089,706972848&fm=253&fmt=auto&app=120&f=JPEG?w=800&h=1067';
};

// 获取显示成员的逻辑
const getDisplayMembers = (members) => {
  const displayMembers = [];
  for (let i = 0; i < members.length; i += 5) {
    displayMembers.push(members.slice(i, i + 5));
  }
  return displayMembers;
};
</script>

<template>
  <div class="main min-h-screen flex flex-col">
    <!-- 头部组件 -->
    <TheHeader />
    <!-- 顶部背景图 -->
    <div class="relative w-full h-[500px] overflow-hidden top-[85px]">
      <el-carousel v-if="bannerImages.length > 0" v-loading="bannerLoading" height="374px" class="banner-carousel"
        :autoplay="true" :interval="5000" :draggable="true">
        <el-carousel-item v-for="(image, index) in bannerImages" :key="index">
          <div class="relative h-full">
            <img :src="image.src" class="w-full h-full object-cover" />
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>

    <!-- 主体内容区域 -->
    <main class="mx-auto py-8">
      <section class="mb-64 mt-24 container mx-auto px-4 relative  bg-while">
        <div class="flex relative z-10" style="height: 500px;">
          <div class="flex flex-col" style="width: 70%;height: 614px;" v-if="false">
            <div class="mb-8 pr-8">
              <h1 class="text-3xl font-bold mb-4">{{ componyDetail.name }}</h1>
              <div class="text-gray-700 text-base leading-relaxed mb-4">{{ componyDetail.mainIntroduction }}</div>
              <div class="text-gray-500 text-base leading-relaxed">{{ componyDetail.secondaryIntroduction }}</div>
            </div>

            <div class="flex justify-between items-end flex-1 relative">
              <img :src="usImg" alt="图标" class="w-[740px] h-auto ml-20" />
              <div class="absolute top-[140px] left-[148px] text-[12px] text-white"
                v-if="currentLanguage === 'russian'">创始人</div>
              <div class="absolute top-[192px] left-[164px] text-[12px] text-white" v-else>创始人</div>
            </div>
          </div>
          <img src="@/assets/about/about-us.png" alt="about" class="h-[635px]  object-contain">

          <div class="flex " style="width: 30%;">
            <div class="p-4">
              <img :src="componyDetail.founderImage" alt="创始人" class="w-[485px] h-[614px] object-contain" />
            </div>
          </div>
        </div>
      </section>

      <img src="@/assets/about/corporate-culture.png" alt="about" class="w-[1590px]  object-contain">

      <!-- 企业文化 -->
      <section class="mb-16 relative top-14" style="width: 1536px; height: 1000px;" v-loading="cultureLoading"
        v-if="false">
        <div class="wyImg absolute left-0 top-14 z-0">
          <img class="" :src="wyImg" alt="古建筑" style="width: 1676px; height: 1040px; object-fit: cover;" />
        </div>

        <div class="relative z-10"
          style="padding-left: 50%;padding-top: 12%; display: flex; justify-content: flex-start;">
          <div
            class="enterpriseCopywriting absolute top-[120px] z-0 text-[20px] left-[60%] translate-x-[-50%] w-[1000px]">
            {{ componyDetail.enterpriseCopywriting }}</div>

          <div class="enterpriseContent" style="width: 984px;min-width: 75%;margin-top: 120px;">
            <div class="w-full bg-red-600 text-white flex flex-col justify-center items-start relative"
              style="height: 266px; padding: 0 48px;opacity: 0.9">
              <h2 class="text-3xl mb-2" style="font-size: 40px; font-weight: bold;margin-top: -45px;">企业文化</h2>
              <div class="absolute bottom-6 right-8" style="font-size: 40px; font-weight: bold;">-CULTURE</div>
            </div>
            <div class="w-full bg-while" style="height: 529px; background: #FFFCF9; opacity: 0.9; overflow-y: auto;">
              <div class="flex h-full" :class="shouldCenterCultureText ? 'items-center justify-center' : 'items-start'"
                style="padding: 15px 20px;">
                <p class="text-gray-700 leading-relaxed" :style="{
                  'line-height': 1.4,
                  'text-align': shouldCenterCultureText ? 'center' : 'left'
                }">{{ cultureTxt }}</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 定制师成员 -->
    <div class="mb-16 relative w-full mt-14"
      :style="{ backgroundImage: `url(${bgImg})`, backgroundSize: 'cover', backgroundPosition: 'top center', backgroundRepeat: 'no-repeat', padding: '40px 0' }">
      <section class="relative">
        <div class="container mx-auto px-4">
          <h2 class="text-3xl font-bold mb-16 text-left">定制师成员</h2>
          <div v-loading="loading" class="min-h-[500px]">
            <div v-if="customDesigners.length === 0 && !loading" class="text-center py-8 text-gray-500">
              暂无定制师成员数据
            </div>
            <div v-else class="grid gap-4">
              <div class="grid grid-cols-5 gap-4"
                v-for="(rowMembers, rowIndex) in getDisplayMembers(paginatedCustomDesigners)" :key="rowIndex">
                <div v-for="member in rowMembers" :key="member.id" class="mb-4">
                  <div class="overflow-hidden mb-2">
                    <img :src="member.avatar" :alt="member.name" style="width: 260px; height: 391px; object-fit: cover;"
                      @error="handleImageError" />
                  </div>
                  <div>
                    <div class="member-text-container">
                      <span class="member-name-text">{{ member.name }}</span>
                      <span class="member-position-text">{{ member.position }}</span>
                    </div>
                    <el-tooltip :content="member.description" placement="top" :show-after="300"
                      :disabled="member.description && member.description.length <= 10">
                      <div class="member-desc-text">{{ member.description }}</div>
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 定制师分页控件 -->
          <div v-if="totalCustomPages > 1" class="flex justify-center items-center mt-8 space-x-4">
            <button @click="prevCustomPage" :disabled="currentCustomPage === 1" :class="[
              'w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-200',
              currentCustomPage === 1
                ? 'cursor-not-allowed'
                : 'cursor-pointer'
            ]" :style="{
              backgroundColor: currentCustomPage === 1 ? '#e7e2e2' : '#c9161c',
              color: currentCustomPage === 1 ? '#999' : '#fff'
            }">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </button>

            <button @click="nextCustomPage" :disabled="currentCustomPage === totalCustomPages" :class="[
              'w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-200',
              currentCustomPage === totalCustomPages
                ? 'cursor-not-allowed'
                : 'cursor-pointer'
            ]" :style="{
              backgroundColor: currentCustomPage === totalCustomPages ? '#e7e2e2' : '#c9161c',
              color: currentCustomPage === totalCustomPages ? '#999' : '#fff'
            }">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </button>
          </div>
        </div>
      </section>
    </div>

    <!-- 导游成员 -->
    <div class="mb-16 relative w-full"
      :style="{ backgroundImage: `url(${bg2Img})`, backgroundSize: 'cover', backgroundPosition: 'center', padding: '20px 0' }">
      <section class="relative">
        <div class="container mx-auto px-4">
          <h2 class="text-3xl font-bold mb-16 text-center">导游成员</h2>
          <div v-loading="loading" class="min-h-[500px]">
            <div v-if="tourGuides.length === 0 && !loading" class="text-center py-8 text-gray-500">
              暂无导游成员数据
            </div>
            <div v-else class="grid gap-4">
              <!-- 动态显示成员，每行5个 -->
              <div class="grid grid-cols-5 gap-4"
                v-for="(rowMembers, rowIndex) in getDisplayMembers(paginatedTourGuides)" :key="rowIndex">
                <div v-for="member in rowMembers" :key="member.id" class="mb-4">
                  <div class="overflow-hidden mb-2">
                    <img :src="member.avatar" :alt="member.name" style="width: 260px; height: 391px; object-fit: cover;"
                      @error="handleImageError" />
                  </div>
                  <div>
                    <div class="member-text-container">
                      <span class="member-name-text">{{ member.name }}</span>
                      <span class="member-position-text">{{ member.position }}</span>
                    </div>
                    <el-tooltip :content="member.description" placement="top" :show-after="300"
                      :disabled="member.description && member.description.length <= 10">
                      <div class="member-desc-text">{{ member.description }}</div>
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 导游分页控件 -->
          <div v-if="totalGuidePages > 1" class="flex justify-center items-center mt-8 space-x-4">
            <button @click="prevGuidePage" :disabled="currentGuidePage === 1" :class="[
              'w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-200',
              currentGuidePage === 1
                ? 'cursor-not-allowed'
                : 'cursor-pointer'
            ]" :style="{
              backgroundColor: currentGuidePage === 1 ? '#e7e2e2' : '#c9161c',
              color: currentGuidePage === 1 ? '#999' : '#fff'
            }">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </button>

            <button @click="nextGuidePage" :disabled="currentGuidePage === totalGuidePages" :class="[
              'w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-200',
              currentGuidePage === totalGuidePages
                ? 'cursor-not-allowed'
                : 'cursor-pointer'
            ]" :style="{
              backgroundColor: currentGuidePage === totalGuidePages ? '#e7e2e2' : '#c9161c',
              color: currentGuidePage === totalGuidePages ? '#999' : '#fff'
            }">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </button>
          </div>
        </div>
      </section>
    </div>

    <TheFooter />
  </div>
</template>

<style scoped>
.main {
  background-color: #fff;
}

/* 成员文本容器 - 温和的换行策略 */
.member-text-container {
  max-width: 260px;
  overflow-wrap: break-word;
  white-space: normal;
}

/* 成员名称样式 */
.member-name-text {
  font-size: 20px;
  color: #000;
  font-weight: bold;
  line-height: 1.3;
  display: inline;
  overflow-wrap: break-word;
  white-space: nowrap;
}

/* 成员职位样式 */
.member-position-text {
  font-size: 16px;
  color: #000000;
  margin-left: 0;
  margin-top: 4px;
  line-height: 1.5;
  display: block;
  overflow-wrap: break-word;
  word-break: break-word;
  white-space: normal;
  max-width: 200px;
}

/* 当名称和职位一起超出容器时，允许换行 */
.member-text-container:has(.member-name-text + .member-position-text) {
  white-space: normal;
}

/* 如果不支持:has选择器，使用媒体查询作为备选 */
@supports not (selector(:has(*))) {
  .member-text-container {
    white-space: normal;
  }

  .member-name-text {
    white-space: nowrap;
    word-break: break-word;
    overflow-wrap: break-word;
  }

  .member-position-text {
    white-space: normal;
    word-break: break-word;
    overflow-wrap: break-word;
    display: block;
    margin-top: 4px;
  }
}

/* 成员描述样式 */
.member-desc-text {
  font-size: 15px;
  color: #7C7C7C;
  max-width: 260px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  cursor: help;
  overflow-wrap: break-word;
  word-break: break-word;
}

@media (min-width: 1400px) and (max-width: 1750px) {
  .wyImg {
    transform: scale(0.9);
    left: -100px;
  }

  .enterpriseContent {
    transform: scale(0.85);
    position: relative;
    top: 60px;
    left: -60px;
  }
}
</style>