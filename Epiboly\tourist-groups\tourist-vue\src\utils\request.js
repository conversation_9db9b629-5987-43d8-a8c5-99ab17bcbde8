import axios from 'axios';
import { ElMessage } from 'element-plus';
import { getToken, logout } from './auth';

const service = axios.create({
  baseURL: process.env.NODE_ENV === 'production' ? 'http://47.79.17.13:8080' : '/api',
  timeout: 25000,
  withCredentials: false,
});

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 在发送请求前可以做一些处理
    // 获取token并添加到请求头
    const token = getToken();
    if (token) {
      // 直接使用token，不添加Bearer前缀，因为后端期望的是直接的token
      config.headers['Authorization'] = token;
    }
    return config;
  },
  error => {
    // 请求错误处理
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 如果响应成功，直接返回数据
    const res = response.data;
    
    // 根据后端返回的状态码判断请求是否成功
    if (res.code !== 1000) {
      // 可以根据不同的错误码做不同的处理
      console.error('请求返回错误:', res.message || '未知错误');
      
      // 处理特定错误，例如token过期
      if (res.code === 1001) {
        if (res.message === '登录失效~') { 
          logout();
        }
        // 提示用户重新登录
        ElMessage.error(res.message);
        return Promise.reject(new Error(res.message));
      }
      
      return Promise.reject(new Error(res.message || '未知错误'));
    }
    
    return res;
  },
  error => {
    // 响应错误处理
    console.error('响应错误:', error);
    
    // 处理401未授权错误
    if (error.response?.status === 401) {
      console.log('登录已过期');
      // 提示用户重新登录
      ElMessage.error('登录已过期，请重新登录');
      
      // 清除认证信息并跳转到登录页
      logout();
      return Promise.reject(new Error('登录已过期'));
    }
    
    const message = error.response?.data?.message || '网络错误，请稍后再试';
    ElMessage.error(message);
    return Promise.reject(error);
  }
);

// 封装GET请求
export function get(url, params) {
  return service({
    url,
    method: 'get',
    params
  });
}

// 封装POST请求
export function post(url, data) {
  return service({
    url,
    method: 'post',
    data
  });
}

// 封装PUT请求
export function put(url, data) {
  return service({
    url,
    method: 'put',
    data
  });
}

// 封装DELETE请求
export function del(url, params) {
  return service({
    url,
    method: 'delete',
    params
  });
}

// 导出axios实例
export default service; 