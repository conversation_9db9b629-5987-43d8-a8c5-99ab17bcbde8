[{"prefix": "/admin/advert/space", "name": "AdvertSpaceEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "", "comment": "名称", "nullable": false, "source": "a.name"}]}}, {"prefix": "/admin/application/apply", "name": "ApplicationApplyEntity", "api": [{"method": "post", "path": "/assignAccount"}, {"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "companyType", "type": "number", "length": "", "comment": "公司性质", "nullable": false, "defaultValue": 0, "dict": ["企业", "个体工商户", "个人"], "source": "a.companyType"}, {"propertyName": "country", "type": "string", "length": "", "comment": "国家", "nullable": true, "source": "a.country"}, {"propertyName": "region", "type": "string", "length": "", "comment": "地区", "nullable": true, "source": "a.region"}, {"propertyName": "status", "type": "number", "length": "", "comment": "状态", "nullable": false, "defaultValue": 0, "dict": ["未分配账号", "已分配账号"], "source": "a.status"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "<PERSON><PERSON><PERSON>", "type": "string", "length": "", "comment": "联系人", "nullable": true, "source": "<PERSON><PERSON><PERSON>"}, {"propertyName": "contactPhone", "type": "string", "length": "", "comment": "联系电话", "nullable": true, "source": "a.<PERSON>"}, {"propertyName": "companyAddress", "type": "string", "length": "", "comment": "公司名称", "nullable": true, "source": "a.companyAddress"}]}}, {"prefix": "/admin/attraction/info", "name": "AttractionInfoEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "isFeatured", "type": "number", "length": "", "comment": "是否精选", "nullable": false, "defaultValue": 0, "dict": ["否", "是"], "source": "a.isFeatured"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "", "comment": "名称", "nullable": false, "source": "a.name"}]}}, {"prefix": "/admin/banner/info", "name": "BannerInfoEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "status", "type": "number", "length": "", "comment": "状态", "nullable": false, "defaultValue": 1, "dict": ["禁用", "启用"], "source": "a.status"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "position", "type": "number", "length": "", "comment": "位置", "nullable": false, "defaultValue": 0, "dict": ["首页", "业务范围", "合作协议", "联系我们", "关于我们"], "source": "a.position"}]}}, {"prefix": "/admin/base/coding", "name": "", "api": [{"method": "get", "path": "/getModuleTree"}, {"method": "post", "path": "/createCode"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/base/comm", "name": "", "api": [{"method": "post", "path": "/personUpdate"}, {"method": "get", "path": "/uploadMode"}, {"method": "get", "path": "/permmenu"}, {"method": "get", "path": "/program"}, {"method": "get", "path": "/person"}, {"method": "post", "path": "/upload"}, {"method": "post", "path": "/logout"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/base/open", "name": "", "api": [{"method": "get", "path": "/refreshToken"}, {"method": "get", "path": "/captcha"}, {"method": "post", "path": "/login"}, {"method": "get", "path": "/html"}, {"method": "get", "path": "/eps"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/base/sys/department", "name": "BaseSysDepartmentEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "post", "path": "/order"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/base/sys/log", "name": "BaseSysLogEntity", "api": [{"method": "post", "path": "/setKeep"}, {"method": "get", "path": "/getKeep"}, {"method": "post", "path": "/clear"}, {"method": "post", "path": "/page"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "", "comment": "姓名", "nullable": true, "source": "b.name"}, {"propertyName": "action", "type": "string", "length": "", "comment": "行为", "nullable": false, "source": "a.action"}, {"propertyName": "ip", "type": "string", "length": "", "comment": "ip", "nullable": true, "source": "a.ip"}]}}, {"prefix": "/admin/base/sys/menu", "name": "BaseSysMenuEntity", "api": [{"method": "post", "path": "/create"}, {"method": "post", "path": "/export"}, {"method": "post", "path": "/import"}, {"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "post", "path": "/parse"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/base/sys/param", "name": "BaseSysParamEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/html"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "dataType", "type": "number", "length": "", "comment": "数据类型 0-字符串 1-富文本 2-文件 ", "nullable": false, "defaultValue": 0, "source": "a.dataType"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "", "comment": "名称", "nullable": false, "source": "a.name"}, {"propertyName": "keyName", "type": "string", "length": "", "comment": "键", "nullable": false, "source": "a.key<PERSON>"}]}}, {"prefix": "/admin/base/sys/role", "name": "BaseSysRoleEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "", "comment": "名称", "nullable": false, "source": "a.name"}, {"propertyName": "label", "type": "string", "length": "50", "comment": "角色标签", "nullable": true, "source": "a.label"}]}}, {"prefix": "/admin/base/sys/user", "name": "BaseSysUserEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "post", "path": "/move"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/company/info", "name": "CompanyInfoEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "founderName", "type": "string", "length": "", "comment": "创始人姓名", "nullable": true, "source": "a.<PERSON><PERSON><PERSON>"}]}}, {"prefix": "/admin/cooperation/agreement", "name": "CooperationAgreementEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/culture/info", "name": "CultureInfoEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "", "comment": "姓名", "nullable": true, "source": "a.name"}]}}, {"prefix": "/admin/demo/goods", "name": "DemoGoodsEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "status", "type": "number", "length": "", "comment": "状态", "nullable": false, "defaultValue": 1, "dict": ["禁用", "启用"], "source": "a.status"}], "fieldLike": [{"propertyName": "title", "type": "string", "length": "50", "comment": "标题", "nullable": false, "source": "a.title"}], "keyWordLikeFields": [{"propertyName": "description", "type": "string", "length": "", "comment": "描述", "nullable": true, "source": "a.description"}]}}, {"prefix": "/admin/demo/tenant", "name": "DemoGoodsEntity", "api": [{"method": "post", "path": "/noTenant"}, {"method": "post", "path": "/noUse"}, {"method": "post", "path": "/use"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/dict/info", "name": "DictInfoEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/types"}, {"method": "post", "path": "/data"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/dict/type", "name": "DictTypeEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/member/info", "name": "MemberInfoEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "type", "type": "number", "length": "", "comment": "类型", "nullable": false, "defaultValue": 0, "dict": ["定制师", "导游"], "source": "a.type"}, {"propertyName": "status", "type": "number", "length": "", "comment": "状态", "nullable": false, "defaultValue": 1, "dict": ["禁用", "启用"], "source": "a.status"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "", "comment": "姓名", "nullable": false, "source": "a.name"}, {"propertyName": "position", "type": "string", "length": "", "comment": "职位", "nullable": false, "source": "a.position"}]}}, {"prefix": "/admin/message/info", "name": "MessageInfoEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "phone", "type": "string", "length": "", "comment": "电话", "nullable": true, "source": "a.phone"}, {"propertyName": "email", "type": "string", "length": "", "comment": "邮箱", "nullable": true, "source": "a.email"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "", "comment": "姓名", "nullable": true, "source": "a.name"}, {"propertyName": "content", "type": "text", "length": "", "comment": "留言内容", "nullable": true, "source": "a.content"}]}}, {"prefix": "/admin/message/notification", "name": "MessageNotificationEntity", "api": [{"method": "post", "path": "/publishAnnouncement"}, {"method": "post", "path": "/sendSystemMessage"}, {"method": "post", "path": "/markAsRead"}, {"method": "post", "path": "/statistics"}, {"method": "post", "path": "/batchSend"}, {"method": "post", "path": "/resend"}, {"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "type", "type": "number", "length": "", "comment": "类型", "nullable": false, "defaultValue": 0, "dict": ["系统消息", "公告"], "source": "a.type"}, {"propertyName": "status", "type": "number", "length": "", "comment": "状态", "nullable": false, "defaultValue": 1, "dict": ["禁用", "启用"], "source": "a.status"}, {"propertyName": "priority", "type": "number", "length": "", "comment": "优先级", "nullable": false, "defaultValue": 0, "dict": ["普通", "重要", "紧急"], "source": "a.priority"}, {"propertyName": "isRead", "type": "boolean", "length": "", "comment": "是否已读", "nullable": false, "defaultValue": false, "source": "a.is<PERSON>"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "title", "type": "string", "length": "", "comment": "标题", "nullable": false, "source": "a.title"}, {"propertyName": "content", "type": "text", "length": "", "comment": "内容", "nullable": false, "source": "a.content"}]}}, {"prefix": "/admin/notice/notice", "name": "NoticeNoticeEntity", "api": [{"method": "get", "path": "/sendDetail"}, {"method": "post", "path": "/batchSend"}, {"method": "post", "path": "/resend"}, {"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "type", "type": "number", "length": "", "comment": "通知类型", "nullable": false, "defaultValue": 0, "dict": ["通知", "公告"], "source": "a.type"}, {"propertyName": "sendStatus", "type": "number", "length": "", "comment": "发送状态", "nullable": false, "defaultValue": 0, "dict": ["待发送", "已发送", "发送失败"], "source": "a.send<PERSON>"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "title", "type": "string", "length": "", "comment": "通知名称", "nullable": false, "source": "a.title"}]}}, {"prefix": "/admin/order/order", "name": "OrderOrderEntity", "api": [{"method": "post", "path": "/update-unfulfilled-orders"}, {"method": "post", "path": "/clear-test-schedule-data"}, {"method": "post", "path": "/update-expired-orders"}, {"method": "post", "path": "/test-schedule-task"}, {"method": "get", "path": "/test-schedule-data"}, {"method": "post", "path": "/list-with-product"}, {"method": "post", "path": "/statistics"}, {"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}, {"method": "get", "path": "/count/product/:productId"}, {"method": "post", "path": "/test-success-notification/:productId"}, {"method": "post", "path": "/test-failure-notification/:productId"}, {"method": "get", "path": "/group-detail/:groupId"}, {"method": "get", "path": "/group-info/:productId"}, {"method": "get", "path": "/product/:productId"}, {"method": "get", "path": "/number/:orderNumber"}, {"method": "get", "path": "/groups/:productId"}], "search": {"fieldEq": [{"propertyName": "orderType", "type": "number", "length": "", "comment": "订单类型", "nullable": false, "defaultValue": 0, "dict": ["预定订单", "散拼团"], "source": "a.orderType"}, {"propertyName": "orderStatus", "type": "number", "length": "", "comment": "订单状态", "nullable": false, "defaultValue": 0, "dict": ["已预定", "已入团", "已失效"], "source": "a.orderStatus"}, {"propertyName": "productId", "type": "number", "length": "", "comment": "产品ID", "nullable": false, "source": "a.productId"}, {"propertyName": "userConfirmed", "type": "boolean", "length": "", "comment": "用户确认状态", "nullable": false, "defaultValue": false, "source": "a.userConfirmed"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "orderNumber", "type": "string", "length": "50", "comment": "订单号", "nullable": false, "source": "a.order<PERSON><PERSON>ber"}, {"propertyName": "contactName", "type": "string", "length": "50", "comment": "联系人姓名", "nullable": true, "source": "a.<PERSON>"}, {"propertyName": "contactPhone", "type": "string", "length": "50", "comment": "联系人电话", "nullable": true, "source": "a.<PERSON>"}]}}, {"prefix": "/admin/plugin/info", "name": "PluginInfoEntity", "api": [{"method": "post", "path": "/install"}, {"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/product/category", "name": "ProductCategoryEntity", "api": [{"method": "post", "path": "/getByProductId"}, {"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/product/info", "name": "ProductInfoEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "status", "type": "number", "length": "", "comment": "状态", "nullable": false, "defaultValue": 1, "dict": ["停用", "启用"], "source": "a.status"}, {"propertyName": "isMostPopular", "type": "number", "length": "", "comment": "是否最受欢迎", "nullable": false, "defaultValue": 0, "dict": ["否", "是"], "source": "a.<PERSON>"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "", "comment": "名称", "nullable": false, "source": "a.name"}]}}, {"prefix": "/admin/product/product", "name": "ProductProductEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "post", "path": "/info"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "status", "type": "number", "length": "", "comment": "状态", "nullable": false, "defaultValue": 1, "dict": ["下架", "上架"], "source": "a.status"}, {"propertyName": "productType", "type": "number", "length": "", "comment": "产品类型", "nullable": false, "defaultValue": 0, "dict": ["预定团", "散拼团"], "source": "a.productType"}, {"propertyName": "isHot", "type": "tinyint", "length": "", "comment": "是否热门推荐", "nullable": false, "defaultValue": 0, "source": "a.is<PERSON>"}, {"propertyName": "isPopular", "type": "tinyint", "length": "", "comment": "最受欢迎状态", "nullable": false, "defaultValue": 0, "source": "a.is<PERSON>"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "200", "comment": "产品名称", "nullable": false, "source": "a.name"}, {"propertyName": "productCode", "type": "string", "length": "50", "comment": "产品编号", "nullable": false, "source": "a.productCode"}, {"propertyName": "subtitle", "type": "string", "length": "500", "comment": "产品副标题", "nullable": true, "source": "a.subtitle"}, {"propertyName": "introduce", "type": "text", "length": "", "comment": "产品介绍", "nullable": true, "source": "a.introduce"}]}}, {"prefix": "/admin/recycle/data", "name": "RecycleDataEntity", "api": [{"method": "post", "path": "/restore"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/page"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "userName", "type": "string", "length": "", "comment": "姓名", "nullable": true, "source": "b.name"}, {"propertyName": "url", "type": "string", "length": "", "comment": "请求的接口", "nullable": true, "source": "a.url"}]}}, {"prefix": "/admin/reservation/info", "name": "ReservationInfoEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "", "comment": "名称", "nullable": false, "source": "a.name"}]}}, {"prefix": "/admin/site/company", "name": "CompanyEntity", "api": [{"method": "post", "path": "/setHeadOffice"}, {"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "", "comment": "公司名称", "nullable": false, "source": "a.name"}, {"propertyName": "address", "type": "string", "length": "", "comment": "公司名称", "nullable": true, "source": "a.address"}]}}, {"prefix": "/admin/site/info", "name": "SiteInfoEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "id", "type": "number", "length": "", "comment": "ID", "nullable": false, "source": "a.id"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "siteLogo", "type": "string", "length": "", "comment": "站点Logo", "nullable": true, "source": "a.<PERSON><PERSON>"}, {"propertyName": "complaintContact", "type": "string", "length": "", "comment": "投诉联系人", "nullable": true, "source": "a.complaintContact"}, {"propertyName": "complaintPhone", "type": "string", "length": "", "comment": "投诉电话", "nullable": true, "source": "a.<PERSON>"}, {"propertyName": "complaintEmail", "type": "string", "length": "", "comment": "投诉邮箱", "nullable": true, "source": "a.complaintEmail"}, {"propertyName": "cooperationContact", "type": "string", "length": "", "comment": "合作联系人", "nullable": true, "source": "a.cooperationContact"}, {"propertyName": "cooperationPhone", "type": "string", "length": "", "comment": "合作电话", "nullable": true, "source": "a.<PERSON>"}, {"propertyName": "cooperationEmail", "type": "string", "length": "", "comment": "合作邮箱", "nullable": true, "source": "a.cooperationEmail"}, {"propertyName": "emergencyContact", "type": "string", "length": "", "comment": "紧急联系人", "nullable": true, "source": "a.emergencyContact"}, {"propertyName": "emergencyPhone", "type": "string", "length": "", "comment": "紧急联系电话", "nullable": true, "source": "a.emergency<PERSON>hone"}, {"propertyName": "emergencyEmail", "type": "string", "length": "", "comment": "紧急联系邮箱", "nullable": true, "source": "a.emergencyEmail"}]}}, {"prefix": "/admin/space/info", "name": "SpaceInfoEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "type", "type": "string", "length": "", "comment": "类型", "nullable": false, "source": "a.type"}, {"propertyName": "classifyId", "type": "number", "length": "", "comment": "分类ID", "nullable": true, "source": "a.classifyId"}], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/space/type", "name": "SpaceTypeEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/task/info", "name": "TaskInfoEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "post", "path": "/start"}, {"method": "post", "path": "/once"}, {"method": "post", "path": "/stop"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/page"}, {"method": "get", "path": "/log"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "status", "type": "number", "length": "", "comment": "状态 0-停止 1-运行", "nullable": false, "defaultValue": 1, "source": "a.status"}, {"propertyName": "type", "type": "number", "length": "", "comment": "状态 0-系统 1-用户", "nullable": false, "defaultValue": 0, "source": "a.type"}], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/user/address", "name": "UserAddressEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/user/info", "name": "UserInfoEntity", "api": [{"method": "post", "path": "/updateBasicInfo"}, {"method": "post", "path": "/updatePassword"}, {"method": "post", "path": "/getApplyInfo"}, {"method": "post", "path": "/updateStatus"}, {"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "status", "type": "number", "length": "", "comment": "状态", "nullable": false, "defaultValue": 1, "dict": ["禁用", "正常", "已注销"], "source": "a.status"}, {"propertyName": "gender", "type": "number", "length": "", "comment": "性别", "nullable": false, "defaultValue": 0, "dict": ["未知", "男", "女"], "source": "a.gender"}, {"propertyName": "loginType", "type": "number", "length": "", "comment": "登录方式", "nullable": false, "defaultValue": 0, "dict": ["小程序", "公众号", "H5"], "source": "a.loginType"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "nick<PERSON><PERSON>", "type": "string", "length": "", "comment": "昵称", "nullable": true, "source": "a.nick<PERSON>"}, {"propertyName": "phone", "type": "string", "length": "", "comment": "手机号", "nullable": true, "source": "a.phone"}]}}]