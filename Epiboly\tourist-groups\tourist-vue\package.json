{"name": "game-catalog-vue", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.8.4", "element-plus": "^2.9.9", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue-virtual-scroller": "^2.0.0-beta.8"}, "devDependencies": {"@vitejs/plugin-vue": "^3.2.0", "autoprefixer": "^10.4.4", "postcss": "^8.4.12", "tailwindcss": "^3.0.24", "vite": "^3.2.7"}}