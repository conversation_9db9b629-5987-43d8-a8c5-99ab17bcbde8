import { get } from '../utils/request';

// 成员相关API
export const memberApi = {
  // 获取所有成员列表（按类型分组返回）
  getMemberList: () => get('/app/member/list'),
  
  // 根据类型获取成员列表
  getMemberListByType: (type) => get('/app/member/listByType', { type }),
  
  // 获取定制师成员列表
  getCustomDesigners: () => get('/app/member/listByType', { type: 0 }),
  
  // 获取导游成员列表
  getTourGuides: () => get('/app/member/listByType', { type: 1 }),
};

export default memberApi; 