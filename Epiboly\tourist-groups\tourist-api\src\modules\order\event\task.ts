import { Inject } from '@midwayjs/core';
import { CoolEvent, Event } from '@cool-midway/core';
import { TaskInfoService } from '../../task/service/info';
import config from '../config';

/**
 * 订单模块任务事件处理
 */
@CoolEvent()
export class OrderTaskEvent {
    @Inject()
    taskInfoService: TaskInfoService;

    @Event('onServerReady')
    async onServerReady() {
        await this.initOrderTasks();
    }

    /**
     * 初始化订单模块的定时任务
     */
    async initOrderTasks() {
        try {
            const orderConfig = config() as any;
            const tasks = orderConfig.tasks || [];

            console.log(`开始初始化订单模块定时任务，共 ${tasks.length} 个任务...`);

            for (const taskConfig of tasks) {
                if (!taskConfig.enable) {
                    console.log(`跳过已禁用的任务: ${taskConfig.name}`);
                    continue;
                }

                // 检查任务是否已存在
                const existingTask = await this.taskInfoService.taskInfoEntity.findOne({
                    where: {
                        name: taskConfig.name,
                        service: taskConfig.service + '.' + taskConfig.method
                    }
                });

                if (existingTask) {
                    console.log(`任务已存在，跳过: ${taskConfig.name}`);
                    // 更新任务配置（如果需要）
                    if (existingTask.cron !== taskConfig.cron || existingTask.status !== (taskConfig.enable ? 1 : 0)) {
                        await this.taskInfoService.taskInfoEntity.update(existingTask.id, {
                            cron: taskConfig.cron,
                            status: taskConfig.enable ? 1 : 0,
                            taskType: 0, // cron类型
                        });
                        console.log(`更新任务配置: ${taskConfig.name}`);
                    }
                    continue;
                }

                // 创建新任务
                const taskData = {
                    name: taskConfig.name,
                    service: taskConfig.service + '.' + taskConfig.method,
                    cron: taskConfig.cron,
                    taskType: 0, // cron类型
                    status: taskConfig.enable ? 1 : 0, // 启用状态
                    type: 0, // 系统任务
                    remark: `订单模块自动创建的定时任务: ${taskConfig.name}`,
                };

                const savedTask = await this.taskInfoService.taskInfoEntity.save(taskData);
                console.log(`✅ 创建定时任务成功: ${taskConfig.name}, ID: ${savedTask.id}`);

                // 如果任务是启用状态，启动任务
                if (taskConfig.enable) {
                    try {
                        await this.taskInfoService.start(savedTask.id, 0);
                        console.log(`✅ 启动定时任务成功: ${taskConfig.name}`);
                    } catch (error) {
                        console.error(`❌ 启动定时任务失败: ${taskConfig.name}`, error);
                    }
                }
            }

            console.log('订单模块定时任务初始化完成');
        } catch (error) {
            console.error('初始化订单模块定时任务失败:', error);
        }
    }
} 