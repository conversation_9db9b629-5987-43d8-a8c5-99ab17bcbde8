<template>
  <div class="min-h-screen bg-cover bg-center page-background">
    <!-- 头部导航 -->
    <TheHeader />
    
    <div class="form-container">
      <!-- 表单内容 -->
      <div class="form-card">
        <!-- 表单标题 -->
        <h1 class="form-title">资料填写</h1>
        
        <el-form :model="formData" :rules="rules" ref="formRef" label-position="right" label-width="100px" class="form-content">
          <!-- 性质 -->
          <el-form-item label="性质：" prop="type">
            <el-select v-model="formData.type" placeholder="组团社" class="form-input">
              <el-option label="组团社" value="organization"></el-option>
              <el-option label="个人" value="personal"></el-option>
              <el-option label="公司" value="company"></el-option>
            </el-select>
          </el-form-item>
          
          <!-- 公司名称 -->
          <el-form-item label="公司名称：" prop="companyName">
            <el-input v-model="formData.companyName" placeholder="请输入公司名称" class="form-input"></el-input>
          </el-form-item>
          
          <!-- 国家 -->
          <el-form-item label="国家：" prop="country">
            <el-input v-model="formData.country" placeholder="请输入国家" class="form-input"></el-input>
          </el-form-item>
          
          <!-- 地区 -->
          <el-form-item label="地区：" prop="region">
            <el-input v-model="formData.region" placeholder="请输入地区" class="form-input"></el-input>
          </el-form-item>
          
          <!-- 联系人 -->
          <el-form-item label="联系人：" prop="contactPerson">
            <el-input v-model="formData.contactPerson" placeholder="请输入联系人姓名" class="form-input"></el-input>
          </el-form-item>
          
          <!-- 电话 -->
          <el-form-item label="电话：" prop="phone">
            <el-input v-model="formData.phone" placeholder="请输入电话号码" class="form-input"></el-input>
          </el-form-item>
          
          <!-- 电子邮箱 -->
          <el-form-item label="E-mail：" prop="email">
            <el-input v-model="formData.email" placeholder="请输入电子邮箱" class="form-input"></el-input>
          </el-form-item>
          
          <!-- WhatsApp -->
          <el-form-item label="WhatsApp：" prop="whatsapp">
            <el-input v-model="formData.whatsapp" placeholder="请输入WhatsApp号码" class="form-input"></el-input>
          </el-form-item>
          
          <!-- 营业执照 -->
          <el-form-item label="营业执照：" prop="licenseUrl">
            <el-upload
              class="license-uploader"
              action="#"
              :http-request="uploadLicense"
              :show-file-list="false"
              :before-upload="beforeLicenseUpload"
              :disabled="uploadLoading"
              list-type="picture-card">
              <img v-if="formData.licenseUrl && !uploadLoading" :src="formData.licenseUrl" class="license-image" />
              <div v-else class="upload-placeholder">
                <el-icon v-if="uploadLoading"><Loading /></el-icon>
                <el-icon v-else><Plus /></el-icon>
                <div v-if="uploadLoading" class="upload-text">上传中...</div>
              </div>
            </el-upload>
          </el-form-item>
          
          <!-- 提交按钮 -->
          <el-form-item>
            <el-button type="danger" class="submit-button" @click="submitForm" :loading="loading">
              立即预订
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    
    <!-- 底部组件 -->
    <TheFooter />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Loading } from '@element-plus/icons-vue'
import TheHeader from '../components/TheHeader.vue'
import TheFooter from '../components/TheFooter.vue'
import { applicationApi, commonApi } from '../api'

// 表单数据
const formData = ref({
  type: '组团社',
  companyName: '',
  country: '',
  region: '',
  contactPerson: '',
  phone: '',
  email: '',
  whatsapp: '',
  licenseUrl: ''
})

const loading = ref(false)
const uploadLoading = ref(false)
const formRef = ref()

// 表单验证规则
const rules = ref({
  type: [
    { required: true, message: '请选择性质', trigger: 'change' }
  ],
  companyName: [
    { required: true, message: '请输入公司名称', trigger: 'blur' }
  ],
  country: [
    { required: true, message: '请输入国家', trigger: 'blur' }
  ],
  region: [
    { required: true, message: '请输入地区', trigger: 'blur' }
  ],
  contactPerson: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入电话号码', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入电子邮箱', trigger: 'blur' },
    { 
      pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, 
      message: '请输入正确的邮箱格式', 
      trigger: 'blur' 
    }
  ],
  whatsapp: [
    { required: true, message: '请输入WhatsApp号码', trigger: 'blur' }
  ],
  licenseUrl: [
    { required: false, message: '请上传营业执照', trigger: 'change' }
  ]
})

// 上传前验证
const beforeLicenseUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传营业执照只能是 JPG 或 PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('上传营业执照大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}

// 自定义上传
const uploadLicense = async (options) => {
  const file = options.file
  uploadLoading.value = true
  
  try {
    // 调用后端API上传文件
    const res = await commonApi.uploadFile(file)
    
    if (res && res.code === 1000) {
      formData.value.licenseUrl = res.data
      ElMessage.success('营业执照上传成功')
      // 触发表单验证
      if (formRef.value) {
        formRef.value.validateField('licenseUrl')
      }
    } else {
      ElMessage.error('营业执照上传失败')
    }
  } catch (error) {
    console.error('营业执照上传失败:', error)
    ElMessage.error('营业执照上传失败，请重试')
  } finally {
    uploadLoading.value = false
  }
}

// 提交表单
const submitForm = async () => {
  // 使用表单验证
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
  } catch (error) {
    ElMessage.warning('请完善表单信息')
    return
  }

  loading.value = true
  
  try {
    // 调用后端API提交申请
    const res = await applicationApi.submitApplication({
      type: formData.value.type,
      companyName: formData.value.companyName,
      country: formData.value.country,
      region: formData.value.region,
      contactPerson: formData.value.contactPerson,
      phone: formData.value.phone,
      email: formData.value.email,
      whatsapp: formData.value.whatsapp,
      licenseUrl: formData.value.licenseUrl
    })
    
    if (res && res.code === 1000) {
      ElMessage.success(res.data.message || '申请提交成功')
      // 清空表单
      formData.value = {
        type: '组团社',
        companyName: '',
        country: '',
        region: '',
        contactPerson: '',
        phone: '',
        email: '',
        whatsapp: '',
        licenseUrl: ''
      }
      // 重置表单验证状态
      formRef.value.resetFields()
    } else {
      ElMessage.error(res.data.message)
    }
  } catch (error) {
    console.error('申请提交失败:', error)
    ElMessage.error('申请提交失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 请求完成状态追踪 - 这个页面主要是表单，没有初始化请求，所以在mounted时直接调用翻译
const requestStatus = reactive({
  pageInit: false
})

// 检查所有请求是否完成并执行翻译
const checkAllRequestsComplete = () => {
    setTimeout(() => {
      translate.execute()
    }, 50)
}

// 页面初始化
onMounted(() => {
  // 表单页面没有异步请求，直接调用翻译
  requestStatus.pageInit = true;
  checkAllRequestsComplete();
});
</script>

<style scoped>
.page-background {
  background-image: url('../assets/images/home-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  width: 100vw;
  min-height: 100vh;
}

.form-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 190px 20px 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: calc(100vh - 200px); /* 确保表单容器占满主要视口区域 */
}

.form-title {
  font-size: 28px;
  font-weight: bold;
  color: #c9161c;
  text-align: center;
  margin-bottom: 30px;
  padding-top: 20px;
}

.form-card {
  width: 100%;
  max-width: 800px;
  background-image: url('../assets/form/card_bg.png');
  background-size: 100% 100%;
  background-position: center;
  border-radius: 20px;
  padding: 20px 40px 40px;
}

.form-content {
  width: 100%;
}

.form-input {
  width: 100%;
}

.license-uploader {
  display: flex;
}

.license-uploader :deep(.el-upload) {
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
}

.license-uploader :deep(.el-upload:hover) {
  border-color: #c9161c;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  font-size: 28px;
  color: #8c939d;
}

.upload-text {
  margin-top: 8px;
  font-size: 12px;
  color: #8c939d;
}

.license-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.submit-button {
  width: 85%;
  height: 50px;
  font-size: 18px;
  font-weight: bold;
  border-radius: 8px;
  margin-top: 20px;
  background-color: #c9161c;
  border: none;
}

.submit-button:hover {
  background-color: #a61016;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-card {
    padding: 20px;
  }
  
  .form-title {
    font-size: 24px;
  }
}
</style> 