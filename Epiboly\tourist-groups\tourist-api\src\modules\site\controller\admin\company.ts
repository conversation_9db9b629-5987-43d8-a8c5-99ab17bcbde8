import { Inject, Post, Body } from '@midwayjs/core';
import { CoolController, BaseController } from '@cool-midway/core';
import { CompanyEntity } from '../../entity/company';
import { CompanyService } from '../../service/company';

/**
 * 公司信息管理
 */
@CoolController({
    api: ['add', 'delete', 'update', 'info', 'list', 'page'],
    entity: CompanyEntity,
    service: CompanyService,
    pageQueryOp: {
        keyWordLikeFields: [
            'a.name',
            'a.address',
        ],
        select: ['a.*']
    },
})
export class AdminCompanyController extends BaseController {
    @Inject()
    companyService: CompanyService;

    /**
     * 设置总公司
     */
    @Post('/setHeadOffice', { summary: '设置总公司' })
    async setHeadOffice(@Body() body: { id: number }) {
        await this.companyService.setHeadOffice(body.id);
        return this.ok('设置成功');
    }
} 