<script setup>
import { ref } from 'vue'

defineProps({
    msg: String,
})

const count = ref(0)
</script>

<template>
    <div class="bg-white dark:bg-gray-800 shadow-xl rounded-lg overflow-hidden">
        <div class="p-8">
            <h1 class="text-3xl font-bold text-center text-gray-900 dark:text-white mb-6">{{ msg }}</h1>

            <div class="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg mb-6">
                <button type="button" @click="count++" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded-md transition-colors mb-4">
                    count is {{ count }}
                </button>
                <p class="text-gray-700 dark:text-gray-300">
                    编辑
                    <code class="bg-gray-200 dark:bg-gray-600 px-1 py-0.5 rounded">components/HelloWorld.vue</code> 测试热模块替换 (HMR)
                </p>
            </div>

            <div class="space-y-4">
                <p class="text-gray-600 dark:text-gray-400">
                    查看
                    <a href="https://vuejs.org/guide/quick-start.html#local" target="_blank" class="text-indigo-600 hover:text-indigo-500 dark:text-indigo-400">
                        create-vue
                    </a>, 官方 Vue + Vite 启动器
                </p>
                <p class="text-gray-600 dark:text-gray-400">
                    了解更多关于Vue的IDE支持，请查看
                    <a href="https://vuejs.org/guide/scaling-up/tooling.html#ide-support" target="_blank" class="text-indigo-600 hover:text-indigo-500 dark:text-indigo-400">
                        Vue文档扩展指南
                    </a>
                </p>
                <p class="text-center text-gray-500 dark:text-gray-500 mt-8">点击Vite和Vue图标了解更多</p>
            </div>
        </div>
    </div>
</template>
