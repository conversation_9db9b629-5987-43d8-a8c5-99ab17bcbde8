import { BaseEntity } from '../../base/entity/base';
import { Column, Entity, Index } from 'typeorm';

/**
 * 产品信息
 */
@Entity('product_info')
export class ProductInfoEntity extends BaseEntity {
  @Column({ comment: '名称' })
  name: string;

  @Column({ comment: '介绍', type: 'text', nullable: true })
  introduce: string;

  @Column({ comment: '评分', dict: ['1', '2', '3', '4', '5'], default: 4 })
  score: number;

  @Column({
    comment: '价格',
    type: 'decimal',
    precision: 10,
    scale: 2,
  })
  price: number;

  @Column({ comment: '封面', nullable: true })
  cover: string;

  @Column({ comment: '分类', type: 'simple-array', nullable: true })
  category: string[];

  @Column({ comment: '是否最受欢迎', dict: ['否', '是'], default: 0 })
  isMostPopular: number;

  @Column({ comment: '好评人数', default: 0 })
  praiseNum: number;

  @Index()
  @Column({ comment: '状态', dict: ['停用', '启用'], default: 1 })
  status: number;
}
