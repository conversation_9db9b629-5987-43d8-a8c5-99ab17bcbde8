<template>
  <cl-crud ref="Crud">
    <cl-row>
      <!-- 刷新按钮 -->
      <cl-refresh-btn />
      <!-- 新增按钮 -->
      <cl-add-btn />
      <!-- 删除按钮 -->
      <cl-multi-delete-btn />
      <!-- 发送系统消息按钮 -->
      <el-button type="warning" @click="openSendSystemMessage">
        发送系统消息
      </el-button>
      <!-- 发布公告按钮 -->
      <el-button type="success" @click="openPublishAnnouncement">
        发布公告
      </el-button>
      <cl-flex1 />
      <!-- 条件搜索 -->
      <cl-search ref="Search" />
    </cl-row>

    <cl-row>
      <!-- 数据表格 -->
      <cl-table ref="Table" />
    </cl-row>

    <cl-row>
      <cl-flex1 />
      <!-- 分页控件 -->
      <cl-pagination />
    </cl-row>

    <!-- 新增、编辑 -->
    <cl-upsert ref="Upsert" />
    
    <!-- 发送系统消息对话框 -->
    <el-dialog
      v-model="systemMessageDialog"
      title="发送系统消息"
      width="600px"
      destroy-on-close
    >
      <el-form ref="systemMessageForm" :model="systemMessageForm" label-width="100px">
        <el-form-item label="消息标题" prop="title">
          <el-input v-model="systemMessageForm.title" placeholder="请输入消息标题" />
        </el-form-item>
        <el-form-item label="消息内容" prop="content">
          <el-input
            v-model="systemMessageForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入消息内容"
          />
        </el-form-item>
        <el-form-item label="接收用户" prop="receiverIds">
          <el-select
            v-model="systemMessageForm.receiverIds"
            multiple
            placeholder="请选择接收用户（不选择表示发送给所有用户）"
            style="width: 100%"
          >
            <el-option
              v-for="user in userList"
              :key="user.id"
              :label="user.name"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-radio-group v-model="systemMessageForm.priority">
            <el-radio :value="0">普通</el-radio>
            <el-radio :value="1">重要</el-radio>
            <el-radio :value="2">紧急</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="systemMessageDialog = false">取消</el-button>
        <el-button type="primary" @click="sendSystemMessage">发送</el-button>
      </template>
    </el-dialog>
    
    <!-- 发布公告对话框 -->
    <el-dialog
      v-model="announcementDialog"
      title="发布公告"
      width="600px"
      destroy-on-close
    >
      <el-form ref="announcementForm" :model="announcementForm" label-width="100px">
        <el-form-item label="公告标题" prop="title">
          <el-input v-model="announcementForm.title" placeholder="请输入公告标题" />
        </el-form-item>
        <el-form-item label="公告内容" prop="content">
          <el-input
            v-model="announcementForm.content"
            type="textarea"
            :rows="6"
            placeholder="请输入公告内容"
          />
        </el-form-item>
        <el-form-item label="发布时间" prop="publishTime">
          <el-date-picker
            v-model="announcementForm.publishTime"
            type="datetime"
            placeholder="选择发布时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-radio-group v-model="announcementForm.priority">
            <el-radio :value="0">普通</el-radio>
            <el-radio :value="1">重要</el-radio>
            <el-radio :value="2">紧急</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="announcementDialog = false">取消</el-button>
        <el-button type="primary" @click="publishAnnouncement">发布</el-button>
      </template>
    </el-dialog>
  </cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
  name: "message-notification",
});

import { useCrud, useTable, useUpsert, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useI18n } from "vue-i18n";
import { reactive, ref, onMounted } from "vue";
import { ElMessage } from "element-plus";

const { service } = useCool();
const { t } = useI18n();

// 对话框状态
const systemMessageDialog = ref(false);
const announcementDialog = ref(false);

// 用户列表
const userList = ref([]);

// 表单数据
const systemMessageForm = ref({
  title: '',
  content: '',
  receiverIds: [],
  priority: 0,
});

const announcementForm = ref({
  title: '',
  content: '',
  publishTime: '',
  priority: 0,
});

// 选项
const options = reactive({
  type: [
    { label: "系统消息", value: 0, type: "primary" },
    { label: "公告", value: 1, type: "success" },
  ],
  status: [
    { label: "禁用", value: 0, type: "danger" },
    { label: "启用", value: 1, type: "success" },
  ],
  priority: [
    { label: "普通", value: 0, type: "info" },
    { label: "重要", value: 1, type: "warning" },
    { label: "紧急", value: 2, type: "danger" },
  ],
  isRead: [
    { label: "未读", value: false, type: "warning" },
    { label: "已读", value: true, type: "success" },
  ],
});

// cl-upsert
const Upsert = useUpsert({
  items: [
    {
      label: "标题",
      prop: "title",
      component: { name: "el-input", props: { clearable: true } },
      required: true,
    },
    {
      label: "内容",
      prop: "content",
      component: {
        name: "el-input",
        props: { type: "textarea", rows: 4 },
      },
      required: true,
    },
    {
      label: "类型",
      prop: "type",
      component: { name: "el-radio-group", options: options.type },
      value: 0,
      required: true,
    },
    {
      label: "接收者ID",
      prop: "receiverId",
      component: { name: "el-input-number", props: { min: 1 } },
    },
    {
      label: "发送者ID",
      prop: "senderId",
      component: { name: "el-input-number", props: { min: 1 } },
    },
    {
      label: "发布时间",
      prop: "publishTime",
      component: {
        name: "el-date-picker",
        props: { type: "datetime", valueFormat: "YYYY-MM-DD HH:mm:ss" },
      },
    },
    {
      label: "优先级",
      prop: "priority",
      component: { name: "el-radio-group", options: options.priority },
      value: 0,
    },
    {
      label: "状态",
      prop: "status",
      component: { name: "el-radio-group", options: options.status },
      value: 1,
    },
  ],
});

// cl-table
const Table = useTable({
  columns: [
    { type: "selection" },
    { 
      label: "类型", 
      prop: "type", 
      width: 100,
      dict: options.type,
    },
    { 
      label: "标题", 
      prop: "title", 
      minWidth: 180,
      showOverflowTooltip: true,
    },
    {
      label: "内容",
      prop: "content",
      showOverflowTooltip: true,
      minWidth: 200,
    },
    {
      label: "发送者",
      prop: "senderName",
      minWidth: 120,
    },
    {
      label: "接收者",
      prop: "receiverName",
      minWidth: 120,
    },
    {
      label: "是否已读",
      prop: "isRead",
      width: 100,
      dict: options.isRead,
    },
    {
      label: "优先级",
      prop: "priority",
      width: 100,
      dict: options.priority,
    },
    {
      label: "状态",
      prop: "status",
      width: 100,
      dict: options.status,
    },
    {
      label: "发布时间",
      prop: "publishTime",
      minWidth: 170,
      sortable: "custom",
      component: { name: "cl-date-text" },
    },
    {
      label: "创建时间",
      prop: "createTime",
      minWidth: 170,
      sortable: "desc",
      component: { name: "cl-date-text" },
    },
    { type: "op", buttons: ["edit", "delete"] },
  ],
});

// cl-search
const Search = useSearch();

// cl-crud
const Crud = useCrud(
  {
    service: service.message.notification,
  },
  (app) => {
    app.refresh();
  },
);

// 获取用户列表
async function getUserList() {
  try {
    const res = await service.base.sys.user.list();
    userList.value = res.map(user => ({
      id: user.id,
      name: user.name || user.username,
    }));
  } catch (error) {
    console.error('获取用户列表失败:', error);
  }
}

// 打开发送系统消息对话框
function openSendSystemMessage() {
  systemMessageForm.value = {
    title: '',
    content: '',
    receiverIds: [],
    priority: 0,
  };
  systemMessageDialog.value = true;
}

// 打开发布公告对话框
function openPublishAnnouncement() {
  announcementForm.value = {
    title: '',
    content: '',
    publishTime: '',
    priority: 0,
  };
  announcementDialog.value = true;
}

// 发送系统消息
async function sendSystemMessage() {
  try {
    await service.message.notification.sendSystemMessage(systemMessageForm.value);
    ElMessage.success('系统消息发送成功');
    systemMessageDialog.value = false;
    refresh();
  } catch (error) {
    ElMessage.error('发送失败');
    console.error(error);
  }
}

// 发布公告
async function publishAnnouncement() {
  try {
    await service.message.notification.publishAnnouncement(announcementForm.value);
    ElMessage.success('公告发布成功');
    announcementDialog.value = false;
    refresh();
  } catch (error) {
    ElMessage.error('发布失败');
    console.error(error);
  }
}

// 刷新
function refresh(params?: any) {
  Crud.value?.refresh(params);
}

// 组件挂载时获取用户列表
onMounted(() => {
  getUserList();
});
</script> 