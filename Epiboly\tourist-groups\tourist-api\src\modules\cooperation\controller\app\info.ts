import { Inject, Get, Query } from '@midwayjs/core';
import { CoolController, BaseController } from '@cool-midway/core';
import { CooperationAgreementEntity } from '../../entity/agreement';
import { CooperationAgreementService } from '../../service/agreement';

/**
 * 合作协议信息app端
 */
@CoolController({
    prefix: '/app/cooperation',
    api: [],
    entity: CooperationAgreementEntity,
    service: CooperationAgreementService,
})
export class AppCooperationInfoController extends BaseController {
    @Inject()
    cooperationAgreementService: CooperationAgreementService;

    /**
     * 获取合作协议信息
     */
    @Get('/info', { summary: '获取合作协议信息' })
    async info() {
        const dataList = await this.cooperationAgreementService.cooperationAgreementEntity.find({
            order: { createTime: 'DESC' },
            take: 1
        });

        const data = dataList.length > 0 ? dataList[0] : null;

        if (!data) {
            return this.ok({
                tabs: [
                    { title: '优势一', image: '' },
                    { title: '优势二', image: '' },
                    { title: '优势三', image: '' }
                ],
                supports: {
                    training: '公司聘请、协心游学拥有丰富经验的分公司，掌控第一手教育资源，跨区域集合共享资源优势',
                    technology: '公司聘请、协心游学拥有丰富经验的分公司，掌控第一手教育资源，跨区域集合共享资源优势',
                    market: '公司聘请、协心游学拥有丰富经验的分公司，掌控第一手教育资源，跨区域集合共享资源优势',
                    service: '公司聘请、协心游学拥有丰富经验的分公司，掌控第一手教育资源，跨区域集合共享资源优势'
                }
            });
        }

        return this.ok({
            tabs: [
                {
                    title: data.tab1Title || '优势一',
                    image: data.advantageOnePic || ''
                },
                {
                    title: data.tab2Title || '优势二',
                    image: data.advantageTwoPic || ''
                },
                {
                    title: data.tab3Title || '优势三',
                    image: data.advantageThreePic || ''
                }
            ],
            supports: {
                training: data.trainingSupport || '公司聘请、协心游学拥有丰富经验的分公司，掌控第一手教育资源，跨区域集合共享资源优势',
                technology: data.technologySupport || '公司聘请、协心游学拥有丰富经验的分公司，掌控第一手教育资源，跨区域集合共享资源优势',
                market: data.marketSupport || '公司聘请、协心游学拥有丰富经验的分公司，掌控第一手教育资源，跨区域集合共享资源优势',
                service: data.serviceSupport || '公司聘请、协心游学拥有丰富经验的分公司，掌控第一手教育资源，跨区域集合共享资源优势'
            }
        });
    }
} 