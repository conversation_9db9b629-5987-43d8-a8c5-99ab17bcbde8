<template>
    <div v-if="isOpen" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50">
        <div class="bg-gray-800 p-6 rounded-xl shadow-xl border border-gray-700 max-w-md w-full mx-4 transform transition-all">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-white">售后微信号</h3>
                <button @click="close" class="text-gray-400 hover:text-white">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="text-center">
                <p class="text-green-400 text-2xl font-bold mb-2">微信号：{{ siteInfo.customerWechat || 'epiboly' }}</p>
                <p class="text-gray-400">请添加此微信号获取售后服务</p>
            </div>
        </div>
    </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';

const props = defineProps({
    isOpen: {
        type: Boolean,
        required: true
    },
    siteInfo: {
        type: Object,
        required: true
    }
});

const emit = defineEmits(['close']);

const close = () => {
    emit('close');
};
</script> 