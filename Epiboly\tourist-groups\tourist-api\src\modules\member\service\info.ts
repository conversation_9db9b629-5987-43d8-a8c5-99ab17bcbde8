import { Provide } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { MemberInfoEntity } from '../entity/info';

/**
 * 成员信息
 */
@Provide()
export class MemberInfoService extends BaseService {
  @InjectEntityModel(MemberInfoEntity)
  memberInfoEntity: Repository<MemberInfoEntity>;
}
