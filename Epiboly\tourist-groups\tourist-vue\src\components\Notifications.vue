<template>
  <div class="notifications">
    <!-- 通知列表 -->
    <div v-if="type === 'notifications'">
      <div v-if="notifications.length > 0">
        <div v-for="notice in paginatedNotifications" :key="notice.id" class="notice-item" @click="toggleNoticeExpand(notice)">
          <div class="notice-content">
            <div class="notice-title">
              {{ notice.title }}
              <!-- 已读/未读状态标签 -->
              <el-tag v-if="!notice.isRead" type="danger" size="small">未读</el-tag>
              <el-tag v-else type="info" size="small">已读</el-tag>
            </div>
            <div class="notice-time">{{ notice.time }}</div>
            <!-- 展开的内容 -->
            <div v-if="notice.expanded" class="notice-expanded-content">
              <div class="notice-detail">{{ notice.content }}</div>
            </div>
          </div>
          <div class="notice-right">
            <el-icon class="right-icon" :class="{ 'expanded': notice.expanded }">
              <ArrowRight />
            </el-icon>
          </div>
        </div>
      </div>
      <div v-else class="empty-state">
        <el-empty description="暂无通知消息"></el-empty>
      </div>

      <!-- 通知分页 -->
      <div v-if="notifications.length > 0" class="pagination-container">
        <div class="custom-pagination">
          <button 
            class="pagination-btn prev-btn" 
            :disabled="currentNotificationPage === 1"
            @click="handleNotificationPageChange(currentNotificationPage - 1)">
            <span class="btn-icon">‹</span>
            <span class="btn-text">上一页</span>
          </button>
          
          <div class="page-numbers">
            <button 
              v-for="page in visibleNotificationPages" 
              :key="page"
              class="page-number"
              :class="{ active: page === currentNotificationPage }"
              @click="handleNotificationPageChange(page)">
              {{ page }}
            </button>
          </div>
          
          <button 
            class="pagination-btn next-btn"
            :disabled="currentNotificationPage === totalNotificationPages"
            @click="handleNotificationPageChange(currentNotificationPage + 1)">
            <span class="btn-text">下一页</span>
            <span class="btn-icon">›</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 公告列表 -->
    <div v-else-if="type === 'announcements'">
      <div v-if="announcements.length > 0">
        <div v-for="announcement in paginatedAnnouncements" :key="announcement.id" class="announcement-item" @click="toggleAnnouncementExpand(announcement)">
          <div class="announcement-content">
            <div class="announcement-header">
              <div class="announcement-title">
                {{ announcement.title }}
                <el-tag v-if="announcement.priority === 2" type="danger" size="small">紧急</el-tag>
                <el-tag v-else-if="announcement.priority === 1" type="warning" size="small">重要</el-tag>
                <!-- 已读/未读状态标签 -->
                <el-tag v-if="!announcement.isRead" type="danger" size="small">未读</el-tag>
                <el-tag v-else type="info" size="small">已读</el-tag>
              </div>
              <div class="announcement-time">{{ announcement.time }}</div>
            </div>
            <!-- 展开的内容 -->
            <div v-if="announcement.expanded" class="announcement-expanded-content">
              <div class="announcement-detail" v-html="announcement.content"></div>
              <div v-if="announcement.senderName" class="announcement-sender">发布者：{{ announcement.senderName }}</div>
            </div>
          </div>
          <div class="announcement-right">
            <el-icon class="right-icon" :class="{ 'expanded': announcement.expanded }">
              <ArrowRight />
            </el-icon>
          </div>
        </div>
      </div>
      <div v-else class="empty-state">
        <el-empty description="暂无公告信息"></el-empty>
      </div>

      <!-- 公告分页 -->
      <div v-if="announcements.length > 0" class="pagination-container">
        <div class="custom-pagination">
          <button 
            class="pagination-btn prev-btn" 
            :disabled="currentAnnouncementPage === 1"
            @click="handleAnnouncementPageChange(currentAnnouncementPage - 1)">
            <span class="btn-icon">‹</span>
            <span class="btn-text">上一页</span>
          </button>
          
          <div class="page-numbers">
            <button 
              v-for="page in visibleAnnouncementPages" 
              :key="page"
              class="page-number"
              :class="{ active: page === currentAnnouncementPage }"
              @click="handleAnnouncementPageChange(page)">
              {{ page }}
            </button>
          </div>
          
          <button 
            class="pagination-btn next-btn"
            :disabled="currentAnnouncementPage === totalAnnouncementPages"
            @click="handleAnnouncementPageChange(currentAnnouncementPage + 1)">
            <span class="btn-text">下一页</span>
            <span class="btn-icon">›</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { ArrowRight } from '@element-plus/icons-vue';
import { ElTag, ElEmpty } from 'element-plus';
import messageApi from '@/api/message';

// 定义 props
const props = defineProps({
  type: {
    type: String,
    default: 'notifications',
    validator: (value) => ['notifications', 'announcements'].includes(value)
  },
  userInfo: {
    type: Object,
    default: null
  }
});

// 定义 emits
const emit = defineEmits(['unread-count-changed']);

// 计算当前用户ID
const currentUserId = computed(() => {
  return props.userInfo?.id || null;
});

console.log('通知组件类型:', props.type);
console.log('用户信息:', props.userInfo);

// 分页相关
const currentNotificationPage = ref(1);
const currentAnnouncementPage = ref(1);
const pageSize = ref(6);

// 数据
const notifications = ref([]);
const announcements = ref([]);
const loading = ref(false);

// 获取消息列表
async function fetchMessages(type, page = 1) {
  if (!currentUserId.value) {
    console.log('当前用户ID为空，无法获取消息');
    return;
  }
  
  console.log(`获取消息列表: userId=${currentUserId.value}, type=${type}, page=${page}`);
  
  loading.value = true;
  try {
    const response = await messageApi.getNotificationList({
      userId: currentUserId.value,
      type: type, // 0-系统消息, 1-公告
      page: page,
      limit: pageSize.value
    });
    
    console.log('API响应:', response);
    
    if (response.code === 1000 && response.data) {
      const messages = response.data.list.map(msg => ({
        ...msg,
        expanded: false
      }));
      
      if (type === 0) {
        notifications.value = messages;
      } else if (type === 1) {
        announcements.value = messages;
      }
      
      console.log(`成功获取${messages.length}条消息`);
    } else {
      console.error('API返回错误:', response);
    }
  } catch (error) {
    console.error('获取消息失败:', error);
  } finally {
    loading.value = false;
  }
}

// 标记消息为已读
async function markAsRead(messageId) {
  if (!currentUserId.value) return;
  
  try {
    const response = await messageApi.markAsRead({
      id: messageId,
      userId: currentUserId.value
    });
    
    if (response.code === 1000) {
      // 通知父组件未读数量已更改
      emit('unread-count-changed');
      console.log('消息已标记为已读:', messageId);
    }
  } catch (error) {
    console.error('标记已读失败:', error);
  }
}

// 通知分页计算
const paginatedNotifications = computed(() => {
  const start = (currentNotificationPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return notifications.value.slice(start, end);
});

const totalNotificationPages = computed(() => {
  return Math.ceil(notifications.value.length / pageSize.value);
});

const visibleNotificationPages = computed(() => {
  const total = totalNotificationPages.value;
  const current = currentNotificationPage.value;
  const pages = [];
  
  if (total <= 7) {
    for (let i = 1; i <= total; i++) {
      pages.push(i);
    }
  } else {
    if (current <= 4) {
      for (let i = 1; i <= 5; i++) {
        pages.push(i);
      }
    } else if (current >= total - 3) {
      for (let i = total - 4; i <= total; i++) {
        pages.push(i);
      }
    } else {
      for (let i = current - 2; i <= current + 2; i++) {
        pages.push(i);
      }
    }
  }
  
  return pages;
});

// 公告分页计算
const paginatedAnnouncements = computed(() => {
  const start = (currentAnnouncementPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return announcements.value.slice(start, end);
});

const totalAnnouncementPages = computed(() => {
  return Math.ceil(announcements.value.length / pageSize.value);
});

const visibleAnnouncementPages = computed(() => {
  const total = totalAnnouncementPages.value;
  const current = currentAnnouncementPage.value;
  const pages = [];
  
  if (total <= 7) {
    for (let i = 1; i <= total; i++) {
      pages.push(i);
    }
  } else {
    if (current <= 4) {
      for (let i = 1; i <= 5; i++) {
        pages.push(i);
      }
    } else if (current >= total - 3) {
      for (let i = total - 4; i <= total; i++) {
        pages.push(i);
      }
    } else {
      for (let i = current - 2; i <= current + 2; i++) {
        pages.push(i);
      }
    }
  }
  
  return pages;
});

// 处理通知分页变化
const handleNotificationPageChange = (page) => {
  if (page >= 1 && page <= totalNotificationPages.value) {
    currentNotificationPage.value = page;
  }
};

// 处理公告分页变化
const handleAnnouncementPageChange = (page) => {
  if (page >= 1 && page <= totalAnnouncementPages.value) {
    currentAnnouncementPage.value = page;
  }
};

// 切换通知展开状态
const toggleNoticeExpand = async (notice) => {
  notice.expanded = !notice.expanded;
  
  // 将通知标记为已读
  if (!notice.isRead && notice.expanded) {
    await markAsRead(notice.id);
    notice.isRead = true;
  }
};

// 切换公告展开状态
const toggleAnnouncementExpand = async (announcement) => {
  announcement.expanded = !announcement.expanded;
  
  // 将公告标记为已读
  if (!announcement.isRead && announcement.expanded) {
    await markAsRead(announcement.id);
    announcement.isRead = true;
  }
};

// 初始化数据
function initData() {
  if (props.type === 'notifications') {
    fetchMessages(0); // 获取系统消息
  } else if (props.type === 'announcements') {
    fetchMessages(1); // 获取公告
  }
}

// 监听类型变化
watch(() => props.type, () => {
  initData();
});

// 监听用户ID变化
watch(() => currentUserId.value, () => {
  if (currentUserId.value) {
    initData();
  }
});

// 组件挂载时初始化
onMounted(() => {
    initData();
});

// 暴露刷新方法
defineExpose({
  refresh: initData
});
</script>

<style scoped>
.notifications {
  padding: 10px;
}

.component-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
  border-left: 4px solid #c9161c;
  padding-left: 10px;
}

.notice-item,
.announcement-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 15px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.3s;
}

.notice-item:hover,
.announcement-item:hover {
  background-color: #f9f9f9;
}

.notice-content,
.announcement-content {
  flex: 1;
}

.notice-title,
.announcement-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.unread-tag {
  margin-left: 10px;
}

.notice-time,
.announcement-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}

.notice-expanded-content,
.announcement-expanded-content {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.notice-detail,
.announcement-detail {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 10px;
}

.notice-sender,
.announcement-sender {
  font-size: 12px;
  color: #999;
  text-align: right;
}

.notice-right,
.announcement-right {
  color: #ccc;
  margin-left: 15px;
  transition: transform 0.3s ease;
}

.right-icon.expanded {
  transform: rotate(90deg);
}

.announcement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  color: #999;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state p {
  font-size: 16px;
  margin: 0;
}

.pagination-container {
  padding: 20px;
  text-align: center;
  border-radius: 0 0 8px 8px;
  background-color: transparent;
}

.custom-pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.pagination-btn {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  height: 26px;
  min-width: 68px;
  background-color: #fff;
  border: 1px solid #BEBDBD;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #BEBDBD;
  transition: all 0.3s ease;
  justify-content: center;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #f5f5f5;
  border-color: #808080;
  color: #808080;
}

.pagination-btn:disabled {
  background-color: #f9f9f9;
  color: #BEBDBD;
  cursor: not-allowed;
  border-color: #BEBDBD;
}

.pagination-btn .btn-icon {
  font-size: 14px;
  font-weight: bold;
}

.pagination-btn .btn-text {
  margin: 0 2px;
}

.prev-btn .btn-icon {
  margin-right: 2px;
}

.next-btn .btn-icon {
  margin-left: 2px;
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 2px;
  margin: 0 8px;
}

.page-number {
  width: 26px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #BEBDBD;
  transition: all 0.3s ease;
}

.page-number:hover {
  background-color: #f5f5f5;
  border-color: #808080;
  color: #808080;
}

.page-number.active {
  border-color: #808080;
  color: #808080;
  font-weight: bold;
}

.page-number.active:hover {
  background-color: #f5f5f5;
  border-color: #808080;
  color: #808080;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .notice-item,
  .announcement-item {
    padding: 12px;
  }
  
  .notice-title,
  .announcement-title {
    font-size: 14px;
  }
  
  .notice-detail,
  .announcement-detail {
    font-size: 13px;
  }
  
  .custom-pagination {
    flex-wrap: wrap;
    gap: 2px;
  }
  
  .pagination-btn {
    padding: 3px 8px;
    font-size: 11px;
    height: 24px;
    min-width: 60px;
  }
  
  .pagination-btn .btn-text {
    display: none;
  }
  
  .page-numbers {
    margin: 0 4px;
  }
  
  .page-number {
    width: 24px;
    height: 24px;
    font-size: 11px;
  }
}
</style> 