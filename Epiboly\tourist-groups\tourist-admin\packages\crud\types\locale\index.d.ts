export declare const locale: {
    en: {
        op: string;
        add: string;
        delete: string;
        multiDelete: string;
        update: string;
        refresh: string;
        info: string;
        search: string;
        reset: string;
        clear: string;
        save: string;
        close: string;
        confirm: string;
        advSearch: string;
        searchKey: string;
        placeholder: string;
        tips: string;
        saveSuccess: string;
        deleteSuccess: string;
        deleteConfirm: string;
        empty: string;
        desc: string;
        asc: string;
        select: string;
        deselect: string;
        seeMore: string;
        hideContent: string;
        nonEmpty: string;
        collapse: string;
        expand: string;
    };
    ja: {
        op: string;
        add: string;
        delete: string;
        multiDelete: string;
        update: string;
        refresh: string;
        info: string;
        search: string;
        reset: string;
        clear: string;
        save: string;
        close: string;
        confirm: string;
        advSearch: string;
        searchKey: string;
        placeholder: string;
        tips: string;
        saveSuccess: string;
        deleteSuccess: string;
        deleteConfirm: string;
        empty: string;
        desc: string;
        asc: string;
        select: string;
        deselect: string;
        seeMore: string;
        hideContent: string;
        nonEmpty: string;
        collapse: string;
        expand: string;
    };
    "zh-cn": {
        op: string;
        add: string;
        delete: string;
        multiDelete: string;
        update: string;
        refresh: string;
        info: string;
        search: string;
        reset: string;
        clear: string;
        save: string;
        close: string;
        confirm: string;
        advSearch: string;
        searchKey: string;
        placeholder: string;
        tips: string;
        saveSuccess: string;
        deleteSuccess: string;
        deleteConfirm: string;
        empty: string;
        desc: string;
        asc: string;
        select: string;
        deselect: string;
        seeMore: string;
        hideContent: string;
        nonEmpty: string;
        collapse: string;
        expand: string;
    };
    "zh-tw": {
        op: string;
        add: string;
        delete: string;
        multiDelete: string;
        update: string;
        refresh: string;
        info: string;
        search: string;
        reset: string;
        clear: string;
        save: string;
        close: string;
        confirm: string;
        advSearch: string;
        searchKey: string;
        placeholder: string;
        tips: string;
        saveSuccess: string;
        deleteSuccess: string;
        deleteConfirm: string;
        empty: string;
        desc: string;
        asc: string;
        select: string;
        deselect: string;
        seeMore: string;
        hideContent: string;
        nonEmpty: string;
        collapse: string;
        expand: string;
    };
};
