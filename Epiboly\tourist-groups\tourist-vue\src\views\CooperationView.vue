<template>
  <div class="cooperation-page">
    <!-- 头部导航 -->
    <TheHeader />

    <!-- 页面顶部轮播图 -->
    <section class="relative" style="padding-top: 85px;">
      <el-carousel v-if="bannerImages.length > 0" v-loading="bannerLoading" height="450px" class="banner-carousel"
        :autoplay="true" :interval="5000" :draggable="true">
        <el-carousel-item v-for="(image, index) in bannerImages" :key="index">
          <div class="relative h-full">
            <img :src="image.src" class="w-full h-full object-cover" :alt="image.alt || '合作协议'" />
          </div>
        </el-carousel-item>
      </el-carousel>

      <!-- 如果没有banner数据，显示默认图片 -->
      <div v-else class="banner-image">
        <img src="@/assets/cooperation/top-bg.png" alt="合作协议" class="w-full h-full object-cover" />
      </div>
    </section>

    <!-- 我们的优势区域 -->
    <section class="advantage-section relative">
      <div class=" mx-auto px-4 py-16">
        <!-- 标题 -->
        <h2 class="section-title text-center mb-10">我们的优势</h2>

        <!-- Tab切换 -->
        <div class="tab-container mx-auto" v-loading="cooperationLoading">
          <div class="tab-header flex justify-around mb-4">
            <div v-for="(tab, index) in tabs" :key="index" class="tab-item text-lg cursor-pointer py-2 px-4"
              :class="{ 'active-tab': activeTabIndex === index }" @click="activeTabIndex = index">
              {{ tab.title }}
            </div>
          </div>

          <!-- Tab内容 -->
          <div class="tab-content p-6">
            <div v-for="(tab, index) in tabs" :key="index" v-show="activeTabIndex === index">
              <img :src="tab.image" :alt="tab.title" class="w-full h-auto object-contain">
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 合作支持区域 -->
    <section class="support-section bg-gray-100 py-16">
      <div class=" mx-auto px-4">
        <!-- 标题 -->
        <h2 class="section-title text-center mb-12 mt-8">为什么选择我们</h2>

        <!-- 卡片和背景区域 -->
        <div class="relative mx-auto w-[1452px]  h-[580px]" v-loading="cooperationLoading">
          <!-- 背景图 -->
          <div class="card-bg">
            <img src="@/assets/cooperation/card-bg.jpg" alt="合作支持背景" class="w-full h-auto">
          </div>

          <!-- 卡片网格 -->
          <div
            class="card-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 px-10 absolute top-1/2 left-0 right-0 transform -translate-y-1/2 mt-6">
            <!-- 支持卡片1 -->
            <div class="support-card bg-white p-6 flex flex-col items-center">
              <div class="card-icon mb-4 mt-10">
                <img src="@/assets/cooperation/icon1.png" alt="覆盖全国的服务网络" class="w-16 h-16">
              </div>
              <h3 class="card-title text-lg font-bold mb-3 text-[#c9161c]">覆盖全国的服务网络</h3>
              <p class="card-text text-gray-600 text-center">{{ cooperationData.supports.training }}</p>
            </div>

            <!-- 支持卡片2 -->
            <div class="support-card bg-white p-6 flex flex-col items-center">
              <div class="card-icon mb-4 mt-10">
                <img src="@/assets/cooperation/icon2.png" alt="无语言障碍" class="w-16 h-16">
              </div>
              <h3 class="card-title text-lg font-bold mb-3 text-[#c9161c]">无语言障碍</h3>
              <p class="card-text text-gray-600 text-center">{{ cooperationData.supports.technology }}</p>
            </div>

            <!-- 支持卡片3 -->
            <div class="support-card bg-white p-6 flex flex-col items-center">
              <div class="card-icon mb-4 mt-10">
                <img src="@/assets/cooperation/icon3.png" alt="灵活的支付制度" class="w-16 h-16">
              </div>
              <h3 class="card-title text-lg font-bold mb-3 text-[#c9161c]">灵活的支付制度</h3>
              <p class="card-text text-gray-600 text-center">{{ cooperationData.supports.market }}</p>
            </div>

            <!-- 支持卡片4 -->
            <div class="support-card bg-white p-6 flex flex-col items-center">
              <div class="card-icon mb-4 mt-10">
                <img src="@/assets/cooperation/icon4.png" alt="可靠性和安全性" class="w-16 h-16">
              </div>
              <h3 class="card-title text-lg font-bold mb-3 text-[#c9161c]">可靠性和安全性</h3>
              <p class="card-text text-gray-600 text-center">{{ cooperationData.supports.service }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 申请流程区域 -->
    <section class="apply-process pt-8 pb-14 bg-white">
      <div class=" mx-auto px-4">
        <!-- 标题 -->
        <h2 class="section-title text-center relative top-[80px] mb-36">申请流程</h2>

        <!-- 流程图 -->
        <div class="process-image mx-auto w-[1452px] ">
          <img src="@/assets/cooperation/step3.png" alt="申请流程" class="w-full h-auto object-contain">
        </div>

        <!-- 立即申请按钮 -->
        <div class="apply-button-container text-center relative top-10 mb-14">
          <button
            class="apply-now-btn bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-14 transition duration-300"
            @click="handleApply">
            立即申请
          </button>
        </div>
      </div>
    </section>

    <!-- 底部组件 -->
    <TheFooter />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, reactive } from 'vue';
import TheHeader from '../components/TheHeader.vue';
import TheFooter from '../components/TheFooter.vue';
import { commonApi, cooperationApi } from '../api';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
// Banner轮播图数据
const bannerImages = ref([]);
const bannerLoading = ref(false);
const router = useRouter();

// 请求完成状态追踪
const requestStatus = reactive({
  banner: false,
  cooperation: false
});

// 检查所有请求是否完成并执行翻译
const checkAllRequestsComplete = () => {
  const allComplete = Object.values(requestStatus).every(status => status === true);
  if (allComplete) {
    // 所有请求完成后执行翻译
    if (window.translate && window.translate.execute) {
      window.translate.execute();
    }
  }
};
// 合作协议数据
const cooperationData = ref({
  tabs: [
    { title: '优势一', image: '@/assets/cooperation/tab-bg.png' },
    { title: '优势二', image: '@/assets/cooperation/tab-bg.png' },
    { title: '优势三', image: '@/assets/cooperation/tab-bg.png' }
  ],
  supports: {
    training: '公司聘请、协心游学拥有丰富经验的分公司，掌控第一手教育资源，跨区域集合共享资源优势',
    technology: '公司聘请、协心游学拥有丰富经验的分公司，掌控第一手教育资源，跨区域集合共享资源优势',
    market: '公司聘请、协心游学拥有丰富经验的分公司，掌控第一手教育资源，跨区域集合共享资源优势',
    service: '公司聘请、协心游学拥有丰富经验的分公司，掌控第一手教育资源，跨区域集合共享资源优势'
  }
});
const cooperationLoading = ref(false);

const handleApply = () => {
  router.push('/apply');
}

// 获取Banner数据
const fetchBannerData = async () => {
  bannerLoading.value = true;
  try {
    const res = await commonApi.getBannerList({ position: 3 });

    if (res && res.data) {
      bannerImages.value = res.data.pics.map(item => ({
        src: item,
        link: res.data.path || '#',
        alt: '合作协议'
      }));
    }
  } catch (error) {
    console.error('获取Banner数据失败:', error);
    ElMessage.error('获取Banner数据失败，使用默认图片');
    // 使用默认数据
    bannerImages.value = [];
  } finally {
    bannerLoading.value = false;
    requestStatus.banner = true;
    checkAllRequestsComplete();
  }
};

// 获取合作协议数据
const fetchCooperationData = async () => {
  cooperationLoading.value = true;
  try {
    const res = await cooperationApi.getCooperationInfo();

    if (res && res.data) {
      // 更新Tab数据，如果后端有图片则使用，否则使用默认图片
      cooperationData.value.tabs = res.data.tabs.map((tab, index) => ({
        title: tab.title,
        image: tab.image || '@/assets/cooperation/tab-bg.png'
      }));

      // 更新支持内容
      cooperationData.value.supports = res.data.supports;
    }
  } catch (error) {
    console.error('获取合作协议数据失败:', error);
    ElMessage.error('获取合作协议数据失败，使用默认数据');
  } finally {
    cooperationLoading.value = false;
    requestStatus.cooperation = true;
    checkAllRequestsComplete();
  }
};

// Tab数据 - 改为计算属性，从cooperationData中获取
const tabs = computed(() => cooperationData.value.tabs);

// 当前激活的Tab索引
const activeTabIndex = ref(0);

// 页面加载时获取Banner数据和合作协议数据
onMounted(() => {
  fetchBannerData();
  fetchCooperationData();
});
</script>

<style scoped>
.cooperation-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.tab-container {
  width: 1452px;
}

/* Banner轮播图样式 */
.banner-carousel {
  overflow: hidden;
}

/* 自定义轮播箭头样式 */
:deep(.el-carousel__arrow) {
  background-color: rgba(255, 255, 255, 0.5);
  width: 36px;
  height: 36px;
  border-radius: 50%;
}

:deep(.el-carousel__arrow:hover) {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 自定义轮播指示器样式 */
:deep(.el-carousel__indicator button) {
  background-color: rgba(255, 255, 255, 0.5);
}

.banner-image {
  height: 450px;
  width: 100%;
  overflow: hidden;
}

.section-title {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  position: relative;
}

/* 我们的优势区域背景 */
.advantage-section {
  background-image: url('@/assets/images/home-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-color: #fdfbfb;
  width: 100%;
}

.apply-process {
  background-image: url('@/assets/cooperation/step-bg.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-color: #fff;
  width: 100%;
}

/* Tab样式 */
.tab-item {
  position: relative;
  color: #666;
  font-weight: 500;
  transition: all 0.3s ease;
}

.active-tab {
  color: #c9161c;
  font-weight: bold;
}

.active-tab::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: -100px;
  width: calc(100% + 200px);
  height: 2.5px;
  background-color: #c9161c;
}

/* 合作支持区域 */
.card-bg {
  position: relative;
}

.support-card {
  width: 300px;
  min-height: 350px !important;
  transition: all 0.3s ease;
  height: 100%;
}

.support-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

/* 立即申请按钮 */
.apply-now-btn {
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(201, 22, 28, 0.2);
}

.apply-now-btn:hover {
  box-shadow: 0 6px 12px rgba(201, 22, 28, 0.3);
}

.card-grid {
  position: relative;
  top: 10px;
  ;
}

@media (max-width: 768px) {
  .banner-image {
    height: 250px;
  }

  .card-grid {
    position: relative;
    transform: none;
    top: auto;
    margin-top: -30%;
  }
}
</style>