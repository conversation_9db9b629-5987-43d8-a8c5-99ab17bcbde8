import { BaseEntity } from '../../base/entity/base';
import { Column, Entity } from 'typeorm';

/**
 * 站点信息
 */
@Entity('site_info')
export class SiteInfoEntity extends BaseEntity {
  @Column({ comment: '站点Logo', nullable: true })
  siteLogo: string;

  @Column({ comment: '投诉联系人', nullable: true })
  complaintContact: string;

  @Column({ comment: '投诉电话', nullable: true })
  complaintPhone: string;

  @Column({ comment: '投诉邮箱', nullable: true })
  complaintEmail: string;

  @Column({ comment: '合作联系人', nullable: true })
  cooperationContact: string;

  @Column({ comment: '合作电话', nullable: true })
  cooperationPhone: string;

  @Column({ comment: '合作邮箱', nullable: true })
  cooperationEmail: string;

  @Column({ comment: '紧急联系人', nullable: true })
  emergencyContact: string;

  @Column({ comment: '紧急联系电话', nullable: true })
  emergencyPhone: string;

  @Column({ comment: '紧急联系邮箱', nullable: true })
  emergencyEmail: string;
}
