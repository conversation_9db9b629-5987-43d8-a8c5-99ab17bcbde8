import { BaseEntity } from '../../base/entity/base';
import { Column, Entity, Index } from 'typeorm';

/**
 * 企业文化信息
 */
@Entity('culture_info')
export class CultureInfoEntity extends BaseEntity {
  @Column({ comment: '姓名', nullable: true })
  name: string;

  @Column({ comment: '主要介绍', nullable: true })
  mainIntroduction: string;

  @Column({ comment: '次要介绍', nullable: true })
  secondaryIntroduction: string;

  @Column({ comment: '创始人图片', nullable: true })
  founderImage: string;

  @Column({ comment: '企业文化', type: 'text', nullable: true })
  enterpriseCulture: string;

  @Column({ comment: '企业文案', nullable: true })
  enterpriseCopywriting: string;
}
