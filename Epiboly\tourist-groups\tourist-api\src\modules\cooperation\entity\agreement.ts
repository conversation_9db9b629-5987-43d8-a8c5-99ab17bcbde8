import { BaseEntity } from '../../base/entity/base';
import { Column, Entity, Index } from 'typeorm';

/**
 * 协议信息
 */
@Entity('cooperation_agreement')
export class CooperationAgreementEntity extends BaseEntity {

  @Column({ comment: 'tab1标题', nullable: true })
  tab1Title: string;

  @Column({ comment: 'tab1封面', nullable: true })
  advantageOnePic: string;

  @Column({ comment: 'tab2标题', nullable: true })
  tab2Title: string;

  @Column({ comment: 'tab2封面', nullable: true })
  advantageTwoPic: string;

  @Column({ comment: 'tab3标题', nullable: true })
  tab3Title: string;

  @Column({ comment: 'tab3封面', nullable: true })
  advantageThreePic: string;

  @Column({ comment: '培训支持内容', type: 'text', nullable: true })
  trainingSupport: string;

  @Column({ comment: '技术支持内容', type: 'text', nullable: true })
  technologySupport: string;

  @Column({ comment: '市场支持内容', type: 'text', nullable: true })
  marketSupport: string;

  @Column({ comment: '服务支持内容', type: 'text', nullable: true })
  serviceSupport: string;
}
