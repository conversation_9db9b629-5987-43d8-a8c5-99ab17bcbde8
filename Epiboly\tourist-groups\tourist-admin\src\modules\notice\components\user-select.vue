<template>
	<el-select
		v-model="selectedValues"
		:placeholder="placeholder"
		multiple
		filterable
		remote
		reserve-keyword
		:remote-method="handleSearch"
		:loading="loading"
		clearable
		collapse-tags
		collapse-tags-tooltip
		@change="handleChange"
	>
		<el-option
			v-for="item in options"
			:key="item.value"
			:label="item.label"
			:value="item.value"
		/>
	</el-select>
</template>

<script setup lang="ts">
defineOptions({
	name: "notice-user-select",
});

import { ref, watch, onMounted } from 'vue';
import { useCool } from '/@/cool';

interface UserOption {
	label: string;
	value: number;
	[key: string]: any;
}

interface Props {
	modelValue?: number[];
	userType?: 'user' | 'admin';
	placeholder?: string;
}

const props = withDefaults(defineProps<Props>(), {
	modelValue: () => [],
	userType: 'user',
	placeholder: '请选择用户'
});

const emit = defineEmits<{
	'update:modelValue': [value: number[]];
}>();

const { service } = useCool();

const selectedValues = ref<number[]>([]);
const options = ref<UserOption[]>([]);
const loading = ref(false);

// 获取用户列表
const getUserList = async (keyword = '') => {
	try {
		loading.value = true;
		let res;
		
		if (props.userType === 'admin') {
			// 获取管理员
			res = await service.base.sys.user.page({
				page: 1,
				size: 50,
				keyWord: keyword,
				status: 1
			});
		} else {
			// 获取普通用户
			res = await service.user.info.page({
				page: 1,
				size: 50,
				keyWord: keyword,
				status: 1
			});
		}

		if (res && res.list) {
			return res.list.map((item: any) => ({
				label: props.userType === 'admin' 
					? `${item.name || item.username} (${item.phone || item.username})`
					: `${item.nickName} (${item.phone})`,
				value: item.id,
				...item
			}));
		}
		return [];
	} catch (error) {
		console.error('获取用户列表失败:', error);
		return [];
	} finally {
		loading.value = false;
	}
};

// 搜索处理
const handleSearch = async (query: string) => {
	const list = await getUserList(query);
	options.value = list;
};

// 值变化处理
const handleChange = (value: number[]) => {
	emit('update:modelValue', value);
};

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
	selectedValues.value = newVal;
}, { immediate: true });

// 监听 selectedValues 变化
watch(selectedValues, (newVal) => {
	emit('update:modelValue', newVal);
});

// 组件挂载时加载初始数据
onMounted(async () => {
	const list = await getUserList();
	options.value = list;
});
</script> 