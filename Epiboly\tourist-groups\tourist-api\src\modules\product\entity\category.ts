import { BaseEntity } from '../../base/entity/base';
import { Column, Entity, Index } from 'typeorm';

/**
 * 产品分类
 */
@Entity('product_category')
export class ProductCategoryEntity extends BaseEntity {
    @Column({ comment: '分类名称', length: 100 })
    name: string;

    @Column({ comment: '排序', type: 'int', default: 0 })
    sort: number;

    @Column({ comment: '明细文件', nullable: true, length: 255 })
    detailFile: string;

    @Index()
    @Column({ comment: '是否展示首页', dict: ['否', '是'], default: 1 })
    isShowHome: number;

    @Index()
    @Column({ comment: '是否展示业务范围', dict: ['否', '是'], default: 1 })
    isShowBusinessScope: number;

    @Index()
    @Column({ comment: '状态', dict: ['禁用', '启用'], default: 1 })
    status: number;
} 