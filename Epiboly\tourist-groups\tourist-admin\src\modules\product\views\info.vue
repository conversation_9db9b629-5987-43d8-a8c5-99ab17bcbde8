<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<cl-flex1 />
			<!-- 条件搜索 -->
			<cl-search ref="Search" />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
	</cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
	name: "product-info",
});

import { useCrud, useTable, useUpsert, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useI18n } from "vue-i18n";
import { reactive } from "vue";

const { service } = useCool();
const { t } = useI18n();

// 选项
const options = reactive({
	score: [
		{ label: t("1"), value: 1 },
		{ label: t("2"), value: 2 },
		{ label: t("3"), value: 3 },
		{ label: t("4"), value: 4 },
		{ label: t("5"), value: 5 },
	],
	isMostPopular: [
		{ label: t("否"), value: 0, type: "danger" },
		{ label: t("是"), value: 1, type: "success" },
	],
	status: [
		{ label: t("停用"), value: 0, type: "danger" },
		{ label: t("启用"), value: 1, type: "success" },
	],
});

// cl-upsert
const Upsert = useUpsert({
	items: [
		{
			label: t("名称"),
			prop: "name",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
			required: true,
		},
		{
			label: t("介绍"),
			prop: "introduce",
			component: {
				name: "el-input",
				props: { type: "textarea", rows: 4 },
			},
		},
		{
			label: t("评分"),
			prop: "score",
			component: { name: "cl-select", props: { options: options.score } },
			value: 5,
			span: 12,
			required: true,
		},
		{
			label: t("价格"),
			prop: "price",
			hook: "number",
			component: { name: "el-input-number", props: { min: 0 } },
			span: 12,
			required: true,
		},
		{ label: t("封面"), prop: "cover", component: { name: "cl-upload" } },
		{
			label: t("分类"),
			prop: "category",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("是否最受欢迎"),
			prop: "isMostPopular",
			component: {
				name: "el-radio-group",
				options: options.isMostPopular,
			},
			value: 0,
			required: true,
		},
		{
			label: t("好评人数"),
			prop: "praiseNum",
			hook: "number",
			component: { name: "el-input-number", props: { min: 0 } },
			span: 12,
			required: true,
		},
		{
			label: t("状态"),
			prop: "status",
			component: { name: "el-radio-group", options: options.status },
			value: 1,
			required: true,
		},
	],
});

// cl-table
const Table = useTable({
	columns: [
		{ type: "selection" },
		{ label: t("名称"), prop: "name", minWidth: 140 },
		{
			label: t("介绍"),
			prop: "introduce",
			showOverflowTooltip: true,
			minWidth: 200,
		},
		{
			label: t("评分"),
			prop: "score",
			minWidth: 150,
			component: { 
				name: "el-rate",
				props: {
					disabled: true,
					colors: ["#F7BA2A", "#F7BA2A", "#F7BA2A"],
				},
			},
			dict: options.score,
		},
		{ label: t("价格"), prop: "price", minWidth: 140, sortable: "custom" },
		{
			label: t("封面"),
			prop: "cover",
			minWidth: 100,
			component: { name: "cl-image", props: { size: 60 } },
		},
		{ label: t("分类"), prop: "category", minWidth: 120 },
		{
			label: t("是否最受欢迎"),
			prop: "isMostPopular",
			minWidth: 100,
			component: { name: "cl-switch" },
			dict: options.isMostPopular,
		},
		{
			label: t("好评人数"),
			prop: "praiseNum",
			minWidth: 140,
			sortable: "custom",
		},
		{
			label: t("状态"),
			prop: "status",
			minWidth: 120,
			dict: options.status,
		},
		{
			label: t("创建时间"),
			prop: "createTime",
			minWidth: 170,
			sortable: "desc",
			component: { name: "cl-date-text" },
		},
		{
			label: t("更新时间"),
			prop: "updateTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{ type: "op", buttons: ["edit", "delete"] },
	],
});

// cl-search
const Search = useSearch();

// cl-crud
const Crud = useCrud(
	{
		service: service.product.info,
	},
	(app) => {
		app.refresh();
	},
);

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}
</script>
