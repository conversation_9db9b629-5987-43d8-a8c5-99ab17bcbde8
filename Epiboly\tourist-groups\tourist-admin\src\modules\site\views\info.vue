<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn v-if="Table?.data?.length === 0" />
			<cl-flex1 />
			<!-- 条件搜索 -->
			<cl-search ref="Search" />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
	</cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
	name: "site-info",
});

import { useCrud, useTable, useUpsert, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useI18n } from "vue-i18n";
import { computed } from "vue";

const { service } = useCool();
const { t } = useI18n();

// cl-upsert
const Upsert = useUpsert({
	items: [
		{
			label: t("站点Logo"),
			prop: "siteLogo",
			component: { name: "cl-upload" },
		},
		{
			label: t("投诉联系人"),
			prop: "complaintContact",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("投诉电话"),
			prop: "complaintPhone",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("投诉邮箱"),
			prop: "complaintEmail",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("合作联系人"),
			prop: "cooperationContact",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("合作电话"),
			prop: "cooperationPhone",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("合作邮箱"),
			prop: "cooperationEmail",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("紧急联系人"),
			prop: "emergencyContact",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("紧急联系电话"),
			prop: "emergencyPhone",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
		{
			label: t("紧急联系邮箱"),
			prop: "emergencyEmail",
			component: { name: "el-input", props: { clearable: true } },
			span: 12,
		},
	],
});

// cl-table
const Table = useTable({
	columns: [
		{ type: "selection" },
		{
			label: t("站点Logo"),
			prop: "siteLogo",
			minWidth: 100,
			component: { name: "cl-image", props: { size: 60 } },
		},
		{ label: t("投诉联系人"), prop: "complaintContact", minWidth: 140 },
		{ label: t("投诉电话"), prop: "complaintPhone", minWidth: 140 },
		{ label: t("投诉邮箱"), prop: "complaintEmail", minWidth: 140 },
		{ label: t("合作联系人"), prop: "cooperationContact", minWidth: 140 },
		{ label: t("合作电话"), prop: "cooperationPhone", minWidth: 140 },
		{ label: t("合作邮箱"), prop: "cooperationEmail", minWidth: 140 },
		{ label: t("紧急联系人"), prop: "emergencyContact", minWidth: 140 },
		{ label: t("紧急联系电话"), prop: "emergencyPhone", minWidth: 140 },
		{ label: t("紧急联系邮箱"), prop: "emergencyEmail", minWidth: 140 },
		{
			label: t("创建时间"),
			prop: "createTime",
			minWidth: 170,
			sortable: "desc",
			component: { name: "cl-date-text" },
		},
		{
			label: t("更新时间"),
			prop: "updateTime",
			minWidth: 170,
			sortable: "custom",
			component: { name: "cl-date-text" },
		},
		{ type: "op", buttons: ["edit", "delete"] },
	],
});

// cl-search
const Search = useSearch();

// cl-crud
const Crud = useCrud(
	{
		service: service.site.info,
	},
	(app) => {
		app.refresh();
	},
);

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}
</script>
