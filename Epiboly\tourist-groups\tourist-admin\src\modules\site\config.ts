import { ModuleConfig } from "/@/cool";

export default (): ModuleConfig => {
    return {
        // 模块是否启用
        enable: true,

        // 模块名称
        label: "站点管理",

        // 模块描述
        description: "站点信息和公司信息管理",

        // 版本
        version: "1.0.0",

        // 视图路由
        views: [
            {
                path: "/site/info",
                meta: {
                    label: "站点信息",
                },
                component: () => import("./views/info.vue"),
            },
            {
                path: "/site/company",
                meta: {
                    label: "公司管理",
                },
                component: () => import("./views/company.vue"),
            },
        ],
    };
}; 