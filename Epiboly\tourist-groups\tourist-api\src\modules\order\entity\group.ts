import { BaseEntity } from '../../base/entity/base';
import { Column, Entity, Index, ManyToOne, JoinColumn, OneToMany } from 'typeorm';
import { ProductProductEntity } from '../../product/entity/product';
import { OrderOrderEntity } from './order';

/**
 * 拼团信息
 */
@Entity('order_group')
export class OrderGroupEntity extends BaseEntity {
    @Index({ unique: true })
    @Column({ comment: '团号', length: 50 })
    groupNumber: string;

    @Index()
    @Column({ comment: '产品ID' })
    productId: number;

    // 与产品表的关联关系
    @ManyToOne(() => ProductProductEntity, { eager: true })
    @JoinColumn({ name: 'productId' })
    product: ProductProductEntity;

    // 与订单表的关联关系
    @OneToMany(() => OrderOrderEntity, order => order.group)
    orders: OrderOrderEntity[];

    @Column({
        comment: '团状态',
        dict: ['组团中', '已成团', '已失效'],
        default: 0,
    })
    groupStatus: number;

    @Column({ comment: '最大人数限制', type: 'int', default: 5 })
    maxSize: number;

    @Column({ comment: '当前人数', type: 'int', default: 0 })
    currentSize: number;

    @Column({ comment: '剩余人数', type: 'int', default: 5 })
    remainingSize: number;

    @Column({ comment: '团开始时间', type: 'datetime', nullable: true })
    groupStartTime: Date;

    @Column({ comment: '团结束时间', type: 'datetime', nullable: true })
    groupEndTime: Date;

    @Column({ comment: '是否已成团', default: false })
    isCompleted: boolean;

    @Column({ comment: '备注信息', type: 'text', nullable: true })
    remarks: string;
} 