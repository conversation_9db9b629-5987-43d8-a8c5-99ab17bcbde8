import { get, post } from '../utils/request';

// 用户相关API
export const userApi = {
  // 登录
  login: (data) => post('/app/user/login/password', data),
  
  // 获取用户信息
  getUserInfo: () => get('/app/user/info/person'),
  
  // 退出登录
  logout: () => post('/app/user/logout'),
  
  // 注册
  register: (data) => post('/app/user/register', data),
  
  // 发送验证码
  sendSmsCode: (data) => post('/app/user/login/smsCode', data),
  
  // 验证码登录
  loginBySms: (data) => post('/app/user/login/phone', data),
  
  // 刷新token
  refreshToken: (data) => post('/app/user/login/refreshToken', data),
  
  // 更新用户信息
  updateUserInfo: (data) => post('/app/user/info/updatePerson', data),
  
  // 更新密码
  updatePassword: (data) => post('/app/user/info/updatePassword', data),
  
  // 绑定手机号
  bindPhone: (data) => post('/app/user/info/bindPhone', data),
  
  // 注销账户
  logoff: () => post('/app/user/info/logoff'),
  
  // 忘记密码
  forgetPassword: (data) => post('/app/user/login/forgetPassword', data),
};

export const commonApi = {
  // 获取Banner列表
  getBannerList: (params) => get('/app/banner/list', params),
  
  // 获取验证码
  getCaptcha: (params) => get('/app/user/login/captcha', params),
};

export default {
  userApi,
  commonApi
}; 