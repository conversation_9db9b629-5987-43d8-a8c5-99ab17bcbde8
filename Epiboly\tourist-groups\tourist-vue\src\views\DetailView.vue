<script setup>
import { ref, onMounted, reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAppStore } from '../stores/gameStore';

const route = useRoute();
const router = useRouter();
const appStore = useAppStore();

const item = ref(null);
const isLoading = ref(true);
const relatedItems = ref([]);

// 请求完成状态追踪
const requestStatus = reactive({
  dataInit: false
});

// 检查所有请求是否完成并执行翻译
const checkAllRequestsComplete = () => {
  const allComplete = Object.values(requestStatus).every(status => status === true);
  if (allComplete) {
    setTimeout(() => {
      translate.execute();
    }, 50);
  }
};

onMounted(async () => {
    try {
        isLoading.value = true;

        // 初始化数据（如果尚未初始化）
        if (appStore.items.length === 0) {
            await appStore.initData();
        }

        // 获取项目ID
        const itemId = parseInt(route.params.id);

        // 获取项目详情
        item.value = appStore.getItemById(itemId);

        // 如果项目不存在，返回列表页
        if (!item.value) {
            console.error('项目不存在');
            router.push('/list');
            return;
        }

        // 获取相关项目（相同分类的其他项目）
        relatedItems.value = appStore.items
            .filter(i => i.category === item.value.category && i.id !== itemId)
            .slice(0, 3);

    } catch (error) {
        console.error('获取项目详情失败:', error);
    } finally {
        isLoading.value = false;
        requestStatus.dataInit = true;
        checkAllRequestsComplete();
    }
});

// 切换收藏状态
const toggleFavorite = () => {
    if (item.value) {
        appStore.toggleItemSelection(item.value.id);
    }
};

// 返回上一页
const goBack = () => {
    router.back();
};
</script>

<template>
    <div class="max-w-7xl mx-auto px-4 py-8">
        <!-- 加载状态 -->
        <div v-if="isLoading" class="flex justify-center items-center py-20">
            <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
        </div>

        <template v-else-if="item">
            <!-- 返回按钮 -->
            <button @click="goBack" class="flex items-center text-gray-600 hover:text-gray-900 mb-6">
                <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
                返回列表
            </button>

            <!-- 项目详情 -->
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <!-- 项目图片 -->
                <div class="relative h-64 sm:h-96">
                    <img :src="item.image" :alt="item.name" class="w-full h-full object-cover">
                    <div class="absolute top-4 right-4">
                        <button @click="toggleFavorite" class="rounded-full p-3 bg-white shadow-md hover:bg-gray-100">
                            <svg class="h-6 w-6" :class="item.selected ? 'text-red-500' : 'text-gray-400'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path v-if="item.selected" fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                                <path v-else fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- 项目信息 -->
                <div class="p-6">
                    <div class="flex flex-wrap items-center gap-2 mb-4">
                        <span class="px-3 py-1 bg-primary-100 text-primary-800 text-sm rounded-full">
                            {{ item.category }}
                        </span>
                        <span class="px-3 py-1 bg-gray-100 text-gray-800 text-sm rounded-full">
                            {{ item.type }}
                        </span>
                    </div>

                    <h1 class="text-2xl font-bold text-gray-900 mb-4">{{ item.name }}</h1>

                    <div class="prose max-w-none">
                        <p class="text-gray-700">这是项目的详细描述，您可以在这里展示项目的完整信息。此处可以包含丰富的文本内容，介绍项目的特点、优势、用途等信息。</p>

                        <h2 class="text-xl font-semibold mt-6 mb-3">项目特点</h2>
                        <ul class="list-disc pl-5 text-gray-700">
                            <li>特点一: 可以描述项目的主要特点</li>
                            <li>特点二: 可以描述项目的主要特点</li>
                            <li>特点三: 可以描述项目的主要特点</li>
                            <li>特点四: 可以描述项目的主要特点</li>
                        </ul>

                        <h2 class="text-xl font-semibold mt-6 mb-3">技术规格</h2>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div class="flex">
                                    <span class="font-medium text-gray-500 w-24">ID:</span>
                                    <span class="text-gray-900">{{ item.id }}</span>
                                </div>
                                <div class="flex">
                                    <span class="font-medium text-gray-500 w-24">分类:</span>
                                    <span class="text-gray-900">{{ item.category }}</span>
                                </div>
                                <div class="flex">
                                    <span class="font-medium text-gray-500 w-24">类型:</span>
                                    <span class="text-gray-900">{{ item.type }}</span>
                                </div>
                                <div class="flex">
                                    <span class="font-medium text-gray-500 w-24">状态:</span>
                                    <span class="text-gray-900">{{ item.selected ? '已选择' : '未选择' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 相关项目 -->
            <div v-if="relatedItems.length > 0" class="mt-12">
                <h2 class="text-xl font-bold mb-6">相关项目</h2>
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div v-for="relatedItem in relatedItems" :key="relatedItem.id" class="bg-white rounded-lg shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow" @click="router.push({name: 'detail', params: {id: relatedItem.id}})">
                        <div class="h-40">
                            <img :src="relatedItem.image" :alt="relatedItem.name" class="w-full h-full object-cover">
                        </div>
                        <div class="p-4">
                            <div class="flex items-center mb-2">
                                <span class="px-2 py-1 bg-gray-100 text-xs text-gray-600 rounded">{{ relatedItem.type }}</span>
                            </div>
                            <h3 class="font-semibold text-gray-900">{{ relatedItem.name }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </template>

        <div v-else class="bg-white rounded-lg shadow-md p-12 text-center">
            <svg class="mx-auto h-16 w-16 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 class="mt-4 text-lg font-medium text-gray-900">项目不存在</h3>
            <p class="mt-2 text-sm text-gray-500 mb-6">无法找到您请求的项目</p>
            <button @click="router.push('/list')" class="px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600">
                返回列表
            </button>
        </div>
    </div>
</template> 