<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<cl-flex1 />

			<!-- 搜索 -->
			<cl-search ref="Search" />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />

		<!-- 申请信息弹窗 -->
		<el-dialog 
			v-model="applyDialog.visible" 
			title="申请信息" 
			width="800px"
		>
			<div v-if="applyDialog.data">
				<el-descriptions :column="2" border>
					<el-descriptions-item label="公司性质">
						{{ ['企业', '个体工商户', '个人'][applyDialog.data.companyType] }}
					</el-descriptions-item>
					<el-descriptions-item label="状态">
						<el-tag :type="applyDialog.data.status === 1 ? 'success' : 'warning'">
							{{ applyDialog.data.status === 1 ? '已分配账号' : '未分配账号' }}
						</el-tag>
					</el-descriptions-item>
					<el-descriptions-item label="公司" :span="2">
						{{ applyDialog.data.companyAddress }}
					</el-descriptions-item>
					<el-descriptions-item label="国家">
						{{ applyDialog.data.country }}
					</el-descriptions-item>
					<el-descriptions-item label="地区">
						{{ applyDialog.data.region }}
					</el-descriptions-item>
					<el-descriptions-item label="联系人">
						{{ applyDialog.data.contactPerson }}
					</el-descriptions-item>
					<el-descriptions-item label="联系电话">
						{{ applyDialog.data.contactPhone }}
					</el-descriptions-item>
					<el-descriptions-item label="电子邮箱">
						{{ applyDialog.data.email }}
					</el-descriptions-item>
					<el-descriptions-item label="WhatsApp账号">
						{{ applyDialog.data.whatsApp }}
					</el-descriptions-item>
					<el-descriptions-item label="营业执照" :span="2">
						<cl-image 
							v-if="applyDialog.data.businessLicense" 
							:src="applyDialog.data.businessLicense" 
							:size="100" 
						/>
						<span v-else>无</span>
					</el-descriptions-item>
					<el-descriptions-item label="创建时间">
						{{ applyDialog.data.createTime }}
					</el-descriptions-item>
					<el-descriptions-item label="更新时间">
						{{ applyDialog.data.updateTime }}
					</el-descriptions-item>
				</el-descriptions>
			</div>
			<div v-else style="text-align: center; padding: 40px;">
				<el-empty description="该用户暂无申请信息" />
			</div>
			<template #footer>
				<el-button @click="applyDialog.visible = false">关闭</el-button>
			</template>
		</el-dialog>

		<!-- 修改密码弹窗 -->
		<el-dialog 
			v-model="passwordDialog.visible" 
			title="修改密码" 
			width="500px"
		>
			<el-form 
				ref="passwordForm" 
				:model="passwordDialog.form" 
				:rules="passwordDialog.rules"
				label-width="100px"
			>
				<el-form-item label="用户" prop="nickName">
					<el-input 
						v-model="passwordDialog.form.nickName" 
						disabled
					/>
				</el-form-item>
				<el-form-item label="新密码" prop="password">
					<el-input 
						v-model="passwordDialog.form.password" 
						type="password" 
						placeholder="请输入新密码" 
						clearable 
						show-password
					/>
				</el-form-item>
				<el-form-item label="确认密码" prop="confirmPassword">
					<el-input 
						v-model="passwordDialog.form.confirmPassword" 
						type="password" 
						placeholder="请再次输入新密码" 
						clearable 
						show-password
					/>
				</el-form-item>
			</el-form>
			<template #footer>
				<el-button @click="passwordDialog.visible = false">取消</el-button>
				<el-button type="primary" @click="handleUpdatePassword" :loading="passwordDialog.loading">
					确定
				</el-button>
			</template>
		</el-dialog>

		<!-- 编辑用户信息弹窗 -->
		<EditUserDialog 
			v-model="editDialog.visible" 
			:userData="editDialog.userData"
			@success="handleEditSuccess"
		/>
	</cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
	name: 'user-list'
});

import { useCrud, useSearch, useTable, useUpsert } from '@cool-vue/crud';
import { useI18n } from 'vue-i18n';
import { useCool } from '/@/cool';
import { reactive, ref, h } from 'vue';
import { ElMessage } from 'element-plus';
import EditUserDialog from '../components/edit-user-dialog.vue';

const { t } = useI18n();
const { service } = useCool();

// 表单引用
const passwordForm = ref();

const options = reactive({
	loginType: [
		{
			label: t('小程序'),
			value: 0,
			type: 'danger'
		},
		{
			label: t('公众号'),
			value: 1,
			type: 'success'
		},
		{
			label: t('H5'),
			value: 2
		}
	],
	gender: [
		{
			label: t('未知'),
			value: 0,
			type: 'info'
		},
		{
			label: t('男'),
			value: 1,
			type: 'success'
		},
		{
			label: t('女'),
			value: 2,
			type: 'danger'
		}
	],
	status: [
		{
			label: t('禁用'),
			value: 0,
			type: 'danger'
		},
		{
			label: t('启用'),
			value: 1,
			type: 'success'
		},
	]
});

// 申请信息弹窗
const applyDialog = reactive<{
	visible: boolean;
	loading: boolean;
	data: any;
}>({
	visible: false,
	loading: false,
	data: null
});

// 修改密码弹窗
const passwordDialog = reactive<{
	visible: boolean;
	loading: boolean;
	form: any;
	rules: any;
}>({
	visible: false,
	loading: false,
	form: {
		id: null,
		nickName: '',
		password: '',
		confirmPassword: ''
	},
	rules: {
		password: [
			{ required: true, message: '请输入新密码', trigger: 'blur' },
			{ min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
		],
		confirmPassword: [
			{ required: true, message: '请再次输入新密码', trigger: 'blur' },
			{
				validator: (rule: any, value: any, callback: any) => {
					if (value !== passwordDialog.form.password) {
						callback(new Error('两次输入的密码不一致'));
					} else {
						callback();
					}
				},
				trigger: 'blur'
			}
		]
	}
});

// 编辑用户信息弹窗
const editDialog = reactive({
	visible: false,
	userData: null as any
});

// cl-table
const Table = useTable({
	columns: [
		{
			type: 'selection',
			width: 60
		},
		{
			label: t('昵称'),
			prop: 'nickName',
			minWidth: 150
		},
		{
			label: t('头像'),
			prop: 'avatarUrl',
			minWidth: 100,
			component: {
				name: 'cl-avatar'
			}
		},
		{
			label: t('手机号'),
			prop: 'phone',
			minWidth: 120
		},
		{
			label: t('性别'),
			prop: 'gender',
			dict: options.gender,
			minWidth: 120
		},
		{
			label: t('状态'),
			prop: 'status',
			minWidth: 120,
			dict: options.status
		},
		{
			label: t('创建时间'),
			prop: 'createTime',
			sortable: 'desc',
			minWidth: 170
		},
		{
			width: 250,
			type: 'op',
			buttons: [
				{
					label: '编辑',
					onClick: (row: any) => openEditDialog(row.scope.row)
				},
				{
					label: '修改密码',
					onClick: (row: any) => openPasswordDialog(row.scope.row)
				},
				{
					label: '查看申请信息',
					onClick: (row: any) => viewApplyInfo(row.scope.row)
				},
			]
		}
	]
});

// cl-upsert
const Upsert = useUpsert({
	items: [
		{
			prop: 'avatarUrl',
			label: t('头像'),
			component: { name: 'cl-upload' }
		},
		{
			prop: 'nickName',
			label: t('昵称'),
			component: { name: 'el-input' },
			required: true
		},
		{
			prop: 'phone',
			label: t('手机号'),
			component: {
				name: 'el-input',
				props: {
					maxlength: 11
				}
			}
		},
		{
			prop: 'gender',
			label: t('性别'),
			value: 1,
			component: {
				name: 'el-radio-group',
				options: options.gender
			}
		},
		{
			prop: 'status',
			label: t('状态'),
			value: 1,
			component: {
				name: 'el-radio-group',
				options: options.status
			}
		}
	]
});

// cl-search
const Search = useSearch();

// cl-crud
const Crud = useCrud(
	{
		service: service.user.info
	},
	app => {
		app.refresh();
	}
);

// 打开修改密码弹窗
function openPasswordDialog(row: any) {
	passwordDialog.form.id = row.id;
	passwordDialog.form.nickName = row.nickName || row.phone || '未知用户';
	passwordDialog.form.password = '';
	passwordDialog.form.confirmPassword = '';
	passwordDialog.visible = true;
}

// 查看申请信息
async function viewApplyInfo(row: any) {
	try {
		applyDialog.loading = true;
		applyDialog.visible = true;
		
		const result = await service.user.info.request({
			url: '/getApplyInfo',
			method: 'POST',
			data: {
				userId: row.id
			}
		});
		
		applyDialog.data = result;
	} catch (error: any) {
		ElMessage.error(error.message || '获取申请信息失败');
	} finally {
		applyDialog.loading = false;
	}
}

// 处理修改密码
async function handleUpdatePassword() {
	try {
		await passwordForm.value.validate();
		passwordDialog.loading = true;
		
		await service.user.info.request({
			url: '/updatePassword',
			method: 'POST',
			data: {
				id: passwordDialog.form.id,
				password: passwordDialog.form.password
			}
		});
		
		ElMessage.success('密码修改成功');
		passwordDialog.visible = false;
	} catch (error: any) {
		ElMessage.error(error.message || '修改密码失败');
	} finally {
		passwordDialog.loading = false;
	}
}

// 处理用户状态切换
async function toggleUserStatus(row: any) {
	try {
		await service.user.info.request({
			url: '/updateStatus',
			method: 'POST',
			data: {
				id: row.id,
				status: row.status === 1 ? 0 : 1
			}
		});
		ElMessage.success(row.status === 1 ? '用户已禁用' : '用户已启用');
		// 更新本地数据
		row.status = row.status === 1 ? 0 : 1;
	} catch (error: any) {
		ElMessage.error(error.message || '状态更新失败');
	}
}

// 打开编辑用户信息弹窗
function openEditDialog(row: any) {
	editDialog.userData = {
		id: row.id,
		avatarUrl: row.avatarUrl,
		nickName: row.nickName,
		gender: row.gender,
		status: row.status
	};
	editDialog.visible = true;
}

// 处理编辑用户信息成功
function handleEditSuccess() {
	// 刷新列表
	Crud.value.refresh();
}
</script>
