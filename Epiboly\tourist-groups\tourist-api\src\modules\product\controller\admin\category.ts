import { Inject, Post, Body } from '@midwayjs/core';
import { CoolController, BaseController } from '@cool-midway/core';
import { ProductCategoryService } from '../../service/category';
import { ProductCategoryEntity } from '../../entity/category';

/**
 * 后台管理-产品分类
 */
@CoolController({
    api: ['add', 'delete', 'update', 'info', 'page', 'list'],
    entity: ProductCategoryEntity,
    service: ProductCategoryService,
})
export class AdminProductCategoryController extends BaseController {
    @Inject()
    productCategoryService: ProductCategoryService;

    /**
     * 根据产品ID获取分类列表
     */
    @Post('/getByProductId')
    async getByProductId(@Body() body: any) {
        try {
            const { productId } = body;
            if (!productId) {
                return this.fail('产品ID不能为空');
            }
            const categories = await this.productCategoryService.getByProductId(productId);
            return this.ok(categories);
        } catch (error) {
            return this.fail(error.message);
        }
    }
} 