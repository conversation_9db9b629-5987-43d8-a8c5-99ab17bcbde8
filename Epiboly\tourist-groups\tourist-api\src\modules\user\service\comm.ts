import { Inject, Provide } from '@midwayjs/core';
import { BaseService, CoolCommException } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { UserInfoEntity } from '../entity/info';
import { UserSmsService } from './sms';
import * as md5 from 'md5';

/**
 * 用户公共服务
 */
@Provide()
export class UserCommService extends BaseService {
    @InjectEntityModel(UserInfoEntity)
    userInfoEntity: Repository<UserInfoEntity>;

    @Inject()
    userSmsService: UserSmsService;

    /**
     * 用户注册
     * @param phone 手机号
     * @param password 密码
     * @param smsCode 短信验证码
     */
    async register(phone: string, password: string, smsCode: string) {
        // 校验短信验证码
        const check = await this.userSmsService.checkCode(phone, smsCode);
        if (!check) {
            throw new CoolCommException('验证码错误');
        }

        // 检查用户是否已存在
        const exist = await this.userInfoEntity.findOneBy({ phone });
        if (exist) {
            throw new CoolCommException('该手机号已注册');
        }

        // 创建新用户
        const user = {
            phone,
            password: md5(password),
            unionid: phone,
            loginType: 3, // 3表示账号密码注册
            nickName: phone.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2'),
        };

        // 保存到数据库
        await this.userInfoEntity.insert(user);

        return true;
    }
} 