import { Cool<PERSON><PERSON>roller, BaseController } from '@cool-midway/core';
import { AttractionInfoEntity } from '../../entity/info';
import { AttractionInfoService } from '../../service/info';
import { Inject } from '@midwayjs/core';

/**
 * 景点信息
 */
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: AttractionInfoEntity,
  service: AttractionInfoService,
  pageQueryOp: {
    keyWordLikeFields: ['a.name'],
    fieldEq: ['a.isFeatured'],
  },
})
export class AdminAttractionInfoController extends BaseController {
  @Inject()
  attractionInfoService: AttractionInfoService;
}
