import { Inject, Get } from '@midwayjs/core';
import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core';
import { CompanyEntity } from '../../entity/company';
import { CompanyService } from '../../service/company';

/**
 * 公司信息（应用端）
 */
@CoolController({
    prefix: '/app/site',
    api: [],
    entity: CompanyEntity,
    service: CompanyService,
})
export class AppCompanyController extends BaseController {
    @Inject()
    companyService: CompanyService;

    /**
     * 获取所有公司列表
     */
    @Get('/companies', { summary: '获取公司列表' })
    async companies() {
        const companies = await this.companyService.getAllCompanies();
        return this.ok(companies);
    }
} 