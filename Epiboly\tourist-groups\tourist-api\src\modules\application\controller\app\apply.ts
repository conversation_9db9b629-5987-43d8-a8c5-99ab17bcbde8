import { Inject, Post, Body } from '@midwayjs/core';
import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core';
import { ApplicationApplyEntity } from '../../entity/apply';
import { ApplicationApplyService } from '../../service/apply';

/**
 * 申请信息app端
 */
@CoolController({
    prefix: '/app/application',
    api: [],
    entity: ApplicationApplyEntity,
    service: ApplicationApplyService,
})
export class AppApplicationApplyController extends BaseController {
    @Inject()
    applicationApplyService: ApplicationApplyService;

    /**
     * 提交申请
     */
    @Post('/submit', { summary: '提交申请信息' })
    async submit(@Body() body: any) {
        const {
            type,
            companyName,
            country,
            region,
            contactPerson,
            phone,
            email,
            whatsapp,
            licenseUrl
        } = body;

        // 验证必填字段
        if (!companyName) {
            return this.fail('公司名称不能为空');
        }
        if (!contactPerson) {
            return this.fail('联系人不能为空');
        }
        if (!phone) {
            return this.fail('联系电话不能为空');
        }

        // 映射前端字段到数据库字段
        let companyType = 0; // 默认企业
        if (type === 'organization' || type === '组团社') {
            companyType = 0; // 企业
        } else if (type === 'personal' || type === '个人') {
            companyType = 2; // 个人
        } else if (type === 'company' || type === '公司') {
            companyType = 0; // 企业
        }

        const applicationData = {
            companyType,
            companyAddress: companyName,
            country: country || '',
            region: region || '',
            contactPerson: contactPerson || '',
            contactPhone: phone || '',
            email: email || '',
            whatsApp: whatsapp || '',
            businessLicense: licenseUrl || ''
        };

        try {
            const result = await this.applicationApplyService.applicationApplyEntity.save(applicationData);

            return this.ok({
                id: result.id,
                message: '申请提交成功，我们会尽快与您联系'
            });
        } catch (error) {
            console.error('申请提交失败:', error);
            return this.fail('申请提交失败，请稍后重试');
        }
    }
} 