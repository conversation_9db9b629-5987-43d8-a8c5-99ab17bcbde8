import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core';
import { MessageInfoEntity } from '../../entity/info';
import { MessageInfoService } from '../../service/info';
import { Inject } from '@midwayjs/core';

/**
 * 留言信息
 */
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: MessageInfoEntity,
  service: MessageInfoService,
  pageQueryOp: {
    keyWordLikeFields: ['a.name', 'a.content'],
    fieldEq: ['a.phone', 'a.email'],
    where: async ctx => {
      const { createTime } = ctx.request.body;
      return [
        [
          'a.createTime BETWEEN :start AND :end',
          {
            start: createTime ? createTime[0] : null,
            end: createTime ? createTime[1] : null,
          },
          createTime && createTime.length > 1,
        ],
      ];
    },
  },
})
export class AdminMessageInfoController extends BaseController {
  @Inject()
  messageInfoService: MessageInfoService;
}
