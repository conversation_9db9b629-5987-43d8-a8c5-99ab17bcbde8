import { defineStore } from 'pinia'

export const useAppStore = defineStore('appStore', {
  state: () => ({
    items: [],
    selectedItems: [],
    isLoading: false,
    statistics: {
      category1: { count: 0, percentage: 0 },
      category2: { count: 0, percentage: 0 },
      category3: { count: 0, percentage: 0 }
    }
  }),

  getters: {
    getItemById: (state) => (id) => {
      return state.items.find(item => item.id === id)
    },
    
    itemsByCategory: (state) => (category) => {
      if (category === 'all') return state.items
      return state.items.filter(item => item.category === category)
    },
    
    itemsByType: (state) => (type) => {
      if (type === 'all') return state.items
      return state.items.filter(item => item.type === type)
    },
    
    filteredItems: (state) => (category, type, search) => {
      let result = state.items;
      
      // 分类筛选
      if (category !== 'all') {
        result = result.filter(item => item.category === category);
      }
      
      // 类型筛选
      if (type !== 'all') {
        result = result.filter(item => item.type === type);
      }
      
      // 搜索筛选
      if (search) {
        const searchLower = search.toLowerCase();
        result = result.filter(item => item.name.toLowerCase().includes(searchLower));
      }
      
      return result;
    }
  },

  actions: {
    // 初始化数据
    initData() {
      this.isLoading = true
      
      // 模拟加载数据（实际应用中应从API获取）
      setTimeout(() => {
        this.items = this.generateSampleData()
        this.isLoading = false
        
        // 从本地存储中恢复选择的项目
        this.loadSelectedItems()
      }, 500)
    },
    
    // 生成示例数据（实际应用应删除此方法）
    generateSampleData() {
      const categories = ['category1', 'category2', 'category3'];
      const types = ['type1', 'type2', 'type3', 'type4'];
      
      const items = [];
      
      for (let i = 1; i <= 50; i++) {
        const category = categories[Math.floor(Math.random() * categories.length)];
        const type = types[Math.floor(Math.random() * types.length)];
        
        items.push({
          id: i,
          name: `Item ${i}`,
          category,
          type,
          selected: false,
          image: `https://picsum.photos/seed/item${i}/300/150`
        });
      }
      
      return items;
    },
    
    // 选择/取消选择项目
    toggleItemSelection(itemId) {
      const item = this.items.find(i => i.id === itemId)
      if (item) {
        item.selected = !item.selected
        
        // 更新选中的项目列表
        if (item.selected) {
          this.selectedItems.push({...item})
        } else {
          const index = this.selectedItems.findIndex(i => i.id === itemId)
          if (index !== -1) {
            this.selectedItems.splice(index, 1)
          }
        }
        
        // 保存到本地存储
        this.saveSelectedItems()
      }
    },
    
    // 批量选择项目
    selectItems(itemIds) {
      itemIds.forEach(id => {
        const item = this.items.find(i => i.id === id)
        if (item && !item.selected) {
          item.selected = true
          this.selectedItems.push({...item})
        }
      })
      
      this.saveSelectedItems()
    },
    
    // 批量取消选择项目
    deselectItems(itemIds) {
      itemIds.forEach(id => {
        const item = this.items.find(i => i.id === id)
        if (item) {
          item.selected = false
        }
        
        const index = this.selectedItems.findIndex(i => i.id === id)
        if (index !== -1) {
          this.selectedItems.splice(index, 1)
        }
      })
      
      this.saveSelectedItems()
    },
    
    // 清空所有选择
    clearAllSelections() {
      this.items.forEach(item => {
        item.selected = false
      })
      this.selectedItems = []
      
      this.saveSelectedItems()
    },
    
    // 保存选择的项目到本地存储
    saveSelectedItems() {
      const selectedIds = this.selectedItems.map(item => item.id)
      localStorage.setItem('selectedItems', JSON.stringify(selectedIds))
      
      // 更新统计信息
      this.updateStatistics()
    },
    
    // 从本地存储加载选择的项目
    loadSelectedItems() {
      try {
        const storedSelections = localStorage.getItem('selectedItems')
        if (storedSelections) {
          const selectedIds = JSON.parse(storedSelections)
          
          // 清空当前选择
          this.selectedItems = []
          
          // 重新加载选择
          selectedIds.forEach(id => {
            const item = this.items.find(i => i.id === id)
            if (item) {
              item.selected = true
              this.selectedItems.push({...item})
            }
          })
        }
        
        // 更新统计信息
        this.updateStatistics()
      } catch (error) {
        console.error('加载选择的项目时出错:', error)
      }
    },
    
    // 更新统计信息
    updateStatistics() {
      const selectedItemsByCategory = {
        category1: this.selectedItems.filter(i => i.category === 'category1').length,
        category2: this.selectedItems.filter(i => i.category === 'category2').length,
        category3: this.selectedItems.filter(i => i.category === 'category3').length
      }
      
      // 更新统计信息
      this.statistics = {
        category1: {
          count: selectedItemsByCategory.category1 || 0,
          percentage: selectedItemsByCategory.category1 ? Math.min(100, (selectedItemsByCategory.category1 / this.items.length) * 100) : 0
        },
        category2: {
          count: selectedItemsByCategory.category2 || 0,
          percentage: selectedItemsByCategory.category2 ? Math.min(100, (selectedItemsByCategory.category2 / this.items.length) * 100) : 0
        },
        category3: {
          count: selectedItemsByCategory.category3 || 0,
          percentage: selectedItemsByCategory.category3 ? Math.min(100, (selectedItemsByCategory.category3 / this.items.length) * 100) : 0
        }
      }
    }
  }
}) 