import { BaseEntity } from '../../base/entity/base';
import { Column, Entity, Index, ManyToOne, JoinColumn } from 'typeorm';
import { ProductProductEntity } from './product';
import { ProductCategoryEntity } from './category';

/**
 * 产品分类关联
 */
@Entity('product_product_category')
export class ProductProductCategoryEntity extends BaseEntity {
    @Index()
    @Column({ comment: '产品ID' })
    productId: number;

    @Index()
    @Column({ comment: '分类ID' })
    categoryId: number;

    @ManyToOne(() => ProductProductEntity, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'productId' })
    product: ProductProductEntity;

    @ManyToOne(() => ProductCategoryEntity, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'categoryId' })
    category: ProductCategoryEntity;
} 