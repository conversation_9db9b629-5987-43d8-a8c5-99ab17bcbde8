<template>
	<el-dialog 
		v-model="visible" 
		title="编辑用户信息" 
		width="600px"
		@close="handleClose"
	>
		<el-form 
			ref="formRef" 
			:model="form" 
			:rules="rules"
			label-width="100px"
		>
			<el-form-item label="头像" prop="avatarUrl">
				<cl-upload 
					v-model="form.avatarUrl"
					:size="[100, 100]"
					accept="image/*"
				/>
			</el-form-item>
			<el-form-item label="昵称" prop="nickName">
				<el-input 
					v-model="form.nickName" 
					placeholder="请输入昵称" 
					clearable
				/>
			</el-form-item>
			<el-form-item label="性别" prop="gender">
				<el-radio-group v-model="form.gender">
					<el-radio :label="0">未知</el-radio>
					<el-radio :label="1">男</el-radio>
					<el-radio :label="2">女</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="状态" prop="status">
				<el-radio-group v-model="form.status">
					<el-radio :label="0">禁用</el-radio>
					<el-radio :label="1">正常</el-radio>
					<el-radio :label="2">已注销</el-radio>
				</el-radio-group>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="handleClose">取消</el-button>
			<el-button type="primary" @click="handleSubmit" :loading="loading">
				确定
			</el-button>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { useCool } from '/@/cool';

defineOptions({
	name: 'EditUserDialog'
});

interface Props {
	modelValue: boolean;
	userData?: any;
}

interface Emits {
	(e: 'update:modelValue', value: boolean): void;
	(e: 'success'): void;
}

const props = withDefaults(defineProps<Props>(), {
	modelValue: false,
	userData: null
});

const emit = defineEmits<Emits>();

const { service } = useCool();

const formRef = ref();
const loading = ref(false);

const visible = ref(props.modelValue);

const form = reactive({
	id: null,
	avatarUrl: '',
	nickName: '',
	gender: 0,
	status: 1
});

const rules = {
	nickName: [
		{ required: true, message: '请输入昵称', trigger: 'blur' }
	],
	gender: [
		{ required: true, message: '请选择性别', trigger: 'blur' }
	],
	status: [
		{ required: true, message: '请选择状态', trigger: 'blur' }
	]
};

// 监听 modelValue 变化
watch(() => props.modelValue, (val) => {
	visible.value = val;
});

// 监听 visible 变化
watch(visible, (val) => {
	emit('update:modelValue', val);
});

// 监听用户数据变化
watch(() => props.userData, (userData) => {
	if (userData) {
		form.id = userData.id;
		form.avatarUrl = userData.avatarUrl || '';
		form.nickName = userData.nickName || '';
		form.gender = userData.gender ?? 0;
		form.status = userData.status ?? 1;
	}
}, { immediate: true });

// 关闭弹窗
function handleClose() {
	visible.value = false;
	// 重置表单
	if (formRef.value) {
		formRef.value.resetFields();
	}
}

// 提交表单
async function handleSubmit() {
	try {
		await formRef.value.validate();
		loading.value = true;
		
		await service.user.info.request({
			url: '/updateBasicInfo',
			method: 'POST',
			data: {
				id: form.id,
				avatarUrl: form.avatarUrl,
				nickName: form.nickName,
				gender: form.gender,
				status: form.status
			}
		});
		
		ElMessage.success('用户信息更新成功');
		emit('success');
		handleClose();
	} catch (error: any) {
		ElMessage.error(error.message || '更新用户信息失败');
	} finally {
		loading.value = false;
	}
}
</script> 