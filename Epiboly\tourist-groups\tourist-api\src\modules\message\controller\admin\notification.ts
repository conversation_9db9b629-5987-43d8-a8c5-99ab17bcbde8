import { Inject, Post, Body, Provide } from '@midwayjs/core';
import { <PERSON><PERSON><PERSON><PERSON>er, BaseController } from '@cool-midway/core';
import { MessageNotificationEntity } from '../../entity/notification';
import { MessageNotificationService } from '../../service/notification';
import { BaseSysUserEntity } from '../../../base/entity/sys/user';
import { BaseSysUserService } from '../../../base/service/sys/user';

/**
 * 后台通知消息管理
 */
@CoolController({
    api: ['add', 'delete', 'update', 'info', 'list', 'page'],
    entity: MessageNotificationEntity,
    service: MessageNotificationService,
    pageQueryOp: {
        keyWordLikeFields: ['a.title', 'a.content'],
        fieldEq: ['a.type', 'a.status', 'a.priority', 'a.isRead'],
        join: [
            {
                entity: BaseSysUserEntity,
                alias: 'sender',
                condition: 'a.senderId = sender.id',
                type: 'leftJoin',
            },
            {
                entity: BaseSysUserEntity,
                alias: 'receiver',
                condition: 'a.receiverId = receiver.id',
                type: 'leftJoin',
            },
        ],
        select: ['a.*', 'sender.name as senderName', 'receiver.name as receiverName'],
        where: async ctx => {
            const { publishTime } = ctx.request.body;
            const conditions = [];

            if (publishTime && publishTime.length === 2) {
                conditions.push([
                    'a.publishTime BETWEEN :start AND :end',
                    { start: publishTime[0], end: publishTime[1] },
                ]);
            }

            return conditions;
        },
    },
})
@Provide()
export class AdminMessageNotificationController extends BaseController {
    @Inject()
    ctx;

    @Inject()
    messageNotificationService: MessageNotificationService;

    @Inject()
    baseSysUserService: BaseSysUserService;

    /**
     * 批量发送系统消息
     */
    @Post('/sendSystemMessage')
    async sendSystemMessage(@Body() body: any) {
        const { title, content, receiverIds, priority = 0 } = body;
        const results = [];

        if (receiverIds && receiverIds.length > 0) {
            // 发送给指定用户
            for (const receiverId of receiverIds) {
                const result = await this.messageNotificationService.sendSystemMessage({
                    title,
                    content,
                    receiverId,
                    senderId: 1,
                    priority,
                });
                results.push(result);
            }
        } else {
            // 发送给所有用户（全局消息）
            const result = await this.messageNotificationService.sendSystemMessage({
                title,
                content,
                senderId: 1,
                priority,
            });
            results.push(result);
        }

        return this.ok(results);
    }

    /**
     * 发布公告
     */
    @Post('/publishAnnouncement')
    async publishAnnouncement(@Body() body: any) {
        const { title, content, priority = 0, publishTime } = body;

        const result = await this.messageNotificationService.publishAnnouncement({
            title,
            content,
            senderId: 1,
            priority,
            publishTime: publishTime ? new Date(publishTime) : new Date(),
        });

        return this.ok(result);
    }

    /**
     * 批量标记为已读
     */
    @Post('/markAsRead')
    async markAsRead(@Body() body: any) {
        const { ids } = body;

        const result = await this.messageNotificationService.markMultipleAsRead(ids);

        return this.ok(result);
    }

    /**
     * 获取消息统计
     */
    @Post('/statistics')
    async getStatistics() {
        const total = await this.messageNotificationService.messageNotificationEntity.count();
        const unreadCount = await this.messageNotificationService.messageNotificationEntity.count({
            where: { isRead: false, status: 1 }
        });
        const systemMessageCount = await this.messageNotificationService.messageNotificationEntity.count({
            where: { type: 0, status: 1 }
        });
        const announcementCount = await this.messageNotificationService.messageNotificationEntity.count({
            where: { type: 1, status: 1 }
        });

        return this.ok({
            total,
            unreadCount,
            systemMessageCount,
            announcementCount,
        });
    }

    /**
     * 重新发送消息
     */
    @Post('/resend')
    async resendMessage(@Body() body: any) {
        const { id } = body;

        // 获取原消息
        const originalMessage = await this.messageNotificationService.messageNotificationEntity.findOne({
            where: { id }
        });

        if (!originalMessage) {
            return this.fail('消息不存在');
        }

        // 重新发送
        let result;
        if (originalMessage.type === 0) {
            // 系统消息
            result = await this.messageNotificationService.sendSystemMessage({
                title: originalMessage.title,
                content: originalMessage.content,
                receiverId: originalMessage.receiverId,
                senderId: originalMessage.senderId,
                priority: originalMessage.priority,
            });
        } else {
            // 公告
            result = await this.messageNotificationService.publishAnnouncement({
                title: originalMessage.title,
                content: originalMessage.content,
                senderId: originalMessage.senderId,
                priority: originalMessage.priority,
                publishTime: new Date(),
            });
        }

        // 更新原消息状态为已重发
        await this.messageNotificationService.messageNotificationEntity.update(id, {
            status: 0  // 标记为禁用，表示已重发
        });

        return this.ok(result);
    }

    /**
     * 批量发送通知（支持多种类型）
     */
    @Post('/batchSend')
    async batchSend(@Body() body: any) {
        const {
            title,
            content,
            type,
            receiverType,
            receiverIds,
            sendToAll,
            sendToAdmin,
            priority = 0,
            publishTime
        } = body;

        const results = [];
        const senderId = this.ctx.admin?.userId || 1;

        if (type === 1) {
            // 发布公告
            const result = await this.messageNotificationService.publishAnnouncement({
                title,
                content,
                senderId,
                priority,
                publishTime: publishTime ? new Date(publishTime) : new Date(),
            });
            results.push(result);
        } else {
            // 发送系统消息
            if (sendToAll) {
                // 发送给所有用户
                const result = await this.messageNotificationService.sendSystemMessage({
                    title,
                    content,
                    senderId,
                    priority,
                });
                results.push(result);
            } else if (sendToAdmin) {
                // 发送给所有管理员
                const adminUsers = await this.baseSysUserService.baseSysUserEntity.find({
                    where: { status: 1 } // 只获取启用的管理员
                });

                for (const admin of adminUsers) {
                    const result = await this.messageNotificationService.sendSystemMessage({
                        title,
                        content,
                        receiverId: admin.id,
                        senderId,
                        priority,
                        sendToAdmin: true,
                        receiverCount: adminUsers.length,
                    });
                    results.push(result);
                }
            } else if (receiverIds && receiverIds.length > 0) {
                // 发送给指定用户
                for (const receiverId of receiverIds) {
                    const result = await this.messageNotificationService.sendSystemMessage({
                        title,
                        content,
                        receiverId,
                        senderId,
                        priority,
                    });
                    results.push(result);
                }
            }
        }

        return this.ok({
            success: true,
            count: results.length,
            results
        });
    }
} 