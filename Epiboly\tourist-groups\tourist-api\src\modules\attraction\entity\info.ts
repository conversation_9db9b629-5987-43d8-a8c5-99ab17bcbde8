import { BaseEntity } from '../../base/entity/base';
import { Column, Entity } from 'typeorm';

/**
 * 景点信息
 */
@Entity('attraction_info')
export class AttractionInfoEntity extends BaseEntity {
  @Column({ comment: '图片', nullable: true })
  pic: string;

  @Column({ comment: '名称' })
  name: string;

  @Column({ comment: '介绍', type: 'text', nullable: true })
  introduce: string;

  @Column({ comment: '是否精选', dict: ['否', '是'], default: 0 })
  isFeatured: number;
}
