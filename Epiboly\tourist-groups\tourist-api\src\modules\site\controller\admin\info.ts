import { Inject } from '@midwayjs/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BaseController } from '@cool-midway/core';
import { SiteInfoEntity } from '../../entity/info';
import { SiteInfoService } from '../../service/info';

/**
 * 站点信息
 */
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: SiteInfoEntity,
  service: SiteInfoService,
  pageQueryOp: {
    keyWordLikeFields: [
      'a.siteLogo',
      'a.complaintContact',
      'a.complaintPhone',
      'a.complaintEmail',
      'a.cooperationContact',
      'a.cooperationPhone',
      'a.cooperationEmail',
      'a.emergencyContact',
      'a.emergencyPhone',
      'a.emergencyEmail',
    ],
    fieldEq: ['a.id'],
  },
})
export class AdminSiteInfoController extends BaseController {
  @Inject()
  siteInfoService: SiteInfoService;
}
