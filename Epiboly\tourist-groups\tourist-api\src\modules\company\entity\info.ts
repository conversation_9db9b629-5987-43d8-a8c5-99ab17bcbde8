import { BaseEntity } from '../../base/entity/base';
import { Column, Entity } from 'typeorm';

/**
 * 公司信息
 */
@Entity('company_info')
export class CompanyInfoEntity extends BaseEntity {
  @Column({ comment: '创始人姓名', nullable: true })
  founderName: string;

  @Column({ comment: '介绍', type: 'text', nullable: true })
  introduction: string;

  @Column({ comment: '二级介绍', type: 'text', nullable: true })
  secondaryIntroduction: string;

  @Column({ comment: '创始人照片', nullable: true })
  founderPhoto: string;

  @Column({ comment: '文案', type: 'text', nullable: true })
  copywriting: string;

  @Column({ comment: '企业文化', type: 'text', nullable: true })
  corporateCulture: string;
}
