import { Inject, Post, Get, Query, Param } from '@midwayjs/core';
import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core';
import { OrderOrderEntity } from '../../entity/order';
import { OrderGroupEntity } from '../../entity/group';
import { OrderOrderService } from '../../service/order';
import { OrderGroupService } from '../../service/group';
import { ProductProductEntity } from '../../../product/entity/product';
import { UserInfoEntity } from '../../../user/entity/info';
import { BaseSysUserEntity } from '../../../base/entity/sys/user';

/**
 * 订单
 */
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: OrderOrderEntity,
  service: OrderOrderService,
  insertParam: ctx => {
    return {
      adminUserId: ctx.admin.userId,
    };
  },
  pageQueryOp: {
    keyWordLikeFields: ['a.orderNumber', 'a.contactName', 'a.contactPhone'],
    fieldEq: ['a.orderType', 'a.orderStatus', 'a.productId', 'a.userConfirmed'],
    select: [
      'a.*', // 订单信息
      'b.id as productId',
      'b.productCode',
      'b.name as productName', // 产品名称
      'b.subtitle as productSubtitle',
      'b.price as productPrice',
      'b.originalPrice as productOriginalPrice',
      'b.cover as productCover',
      'b.city as productCity',
      'b.destination as productDestination',
      'b.days as productDays',
      'b.productType',
      'b.status as productStatus',
      'c.groupNumber', // 团号
      'c.groupStatus', // 团状态
      'c.currentSize as groupCurrentSize', // 团当前人数
      'c.maxSize as groupMaxSize', // 团最大人数
    ],
    join: [
      {
        entity: ProductProductEntity,
        alias: 'b',
        condition: 'a.productId = b.id',
        type: 'leftJoin',
      },
      {
        entity: OrderGroupEntity,
        alias: 'c',
        condition: 'a.groupId = c.id',
        type: 'leftJoin',
      },
    ],
  },
})
export class AdminOrderOrderController extends BaseController {
  @Inject()
  orderOrderService: OrderOrderService;

  @Inject()
  orderGroupService: OrderGroupService;

  /**
   * 根据产品ID查询订单列表
   */
  @Get('/product/:productId')
  async getOrdersByProductId(@Param('productId') productId: number) {
    const orders = await this.orderOrderService.getOrdersByProductId(productId);
    return this.ok(orders);
  }

  /**
   * 根据订单号查询订单详情
   */
  @Get('/number/:orderNumber')
  async getOrderByNumber(@Param('orderNumber') orderNumber: string) {
    const order = await this.orderOrderService.getOrderByNumber(orderNumber);
    return this.ok(order);
  }

  /**
   * 统计产品的订单数量
   */
  @Get('/count/product/:productId')
  async countOrdersByProduct(@Param('productId') productId: number) {
    const count = await this.orderOrderService.countOrdersByProduct(productId);
    return this.ok({ count });
  }

  /**
   * 获取订单统计信息
   */
  @Post('/statistics')
  async getOrderStatistics(@Query() query: any) {
    const statistics = await this.orderOrderService.getOrderStatistics(query);
    return this.ok(statistics);
  }

  /**
   * 获取带产品信息的订单列表
   */
  @Post('/list-with-product')
  async getOrdersWithProduct(@Query() query: any) {
    const orders = await this.orderOrderService.list(query);
    return this.ok(orders);
  }

  /**
   * 获取拼团详情 - 支持多团
   */
  @Get('/group-info/:productId')
  async getGroupInfo(@Param('productId') productId: number) {
    try {
      const groupInfo = await this.orderGroupService.getAvailableGroups(productId);
      return this.ok(groupInfo);
    } catch (error) {
      return this.fail(error.message || '获取拼团详情失败');
    }
  }

  /**
   * 获取产品的所有团列表
   */
  @Get('/groups/:productId')
  async getProductGroups(@Param('productId') productId: number) {
    try {
      const groupsInfo = await this.orderGroupService.getAvailableGroups(productId);
      return this.ok(groupsInfo);
    } catch (error) {
      return this.fail(error.message || '获取团列表失败');
    }
  }

  /**
   * 获取单个团的详细信息
   */
  @Get('/group-detail/:groupId')
  async getGroupDetail(@Param('groupId') groupId: number) {
    try {
      const members = await this.orderGroupService.getGroupMembers(groupId);
      return this.ok({ members });
    } catch (error) {
      return this.fail(error.message || '获取团详情失败');
    }
  }

  /**
   * 手动更新过期拼团订单状态
   */
  @Post('/update-expired-orders')
  async updateExpiredOrders() {
    try {
      const result = await this.orderOrderService.updateExpiredGroupOrders();
      return this.ok(result);
    } catch (error) {
      return this.fail(error.message || '更新过期订单失败');
    }
  }

  /**
   * 手动更新未成团的过期订单状态
   */
  @Post('/update-unfulfilled-orders')
  async updateUnfulfilledOrders() {
    try {
      const result = await this.orderOrderService.updateUnfulfilledGroupOrders();
      return this.ok(result);
    } catch (error) {
      return this.fail(error.message || '更新未成团订单失败');
    }
  }

  /**
   * 测试发送拼团成功通知
   */
  @Post('/test-success-notification/:productId')
  async testSuccessNotification(@Param('productId') productId: number) {
    try {
      const groupInfo = await this.orderOrderService.getGroupInfo(productId);

      // 创建测试用的拼团成功通知
      const title = '🎉 拼团成功通知（测试）';
      const content = `恭喜！${groupInfo.product.name} 拼团已成功，人数已满(${groupInfo.totalRequired}人)，行程即将开始。请注意查看行程安排和集合信息。`;

      const result = await this.orderOrderService['messageNotificationService'].publishAnnouncement({
        title,
        content,
        senderId: 1,
        priority: 1,
        publishTime: new Date(),
        senderRemark: `拼团成功测试-产品ID:${productId}`
      });

      return this.ok({
        message: '拼团成功通知发送完成',
        notificationResult: result,
        groupInfo
      });
    } catch (error) {
      return this.fail(error.message || '发送拼团成功通知失败');
    }
  }

  /**
   * 测试发送拼团失败通知
   */
  @Post('/test-failure-notification/:productId')
  async testFailureNotification(@Param('productId') productId: number) {
    try {
      const groupInfo = await this.orderOrderService.getGroupInfo(productId);

      // 创建测试用的拼团失败通知
      const title = '😔 拼团失败通知（测试）';
      const content = `很遗憾，${groupInfo.product.name} 拼团已截止，需要${groupInfo.totalRequired}人成团，实际只有${groupInfo.currentCount}人参团，未能成团。已为您取消订单，如有疑问请联系客服。感谢您的理解与支持！`;

      const result = await this.orderOrderService['messageNotificationService'].publishAnnouncement({
        title,
        content,
        senderId: 1,
        priority: 1,
        publishTime: new Date(),
        senderRemark: `拼团失败测试-产品ID:${productId}-需要${groupInfo.totalRequired}人-实际${groupInfo.currentCount}人`
      });

      return this.ok({
        message: '拼团失败通知发送完成',
        notificationResult: result,
        groupInfo
      });
    } catch (error) {
      return this.fail(error.message || '发送拼团失败通知失败');
    }
  }

  /**
   * 手动执行测试定时任务
   */
  @Post('/test-schedule-task')
  async testScheduleTask() {
    try {
      const result = await this.orderOrderService.testScheduleTask();
      return this.ok(result);
    } catch (error) {
      return this.fail(error.message || '执行测试定时任务失败');
    }
  }

  /**
   * 查询最近的测试定时任务数据
   */
  @Get('/test-schedule-data')
  async getTestScheduleData() {
    try {
      // 查询最近10条测试数据
      const testData = await this.orderOrderService['messageInfoEntity']
        .createQueryBuilder('message')
        .where('message.name = :name', { name: '定时任务测试' })
        .orderBy('message.createTime', 'DESC')
        .limit(10)
        .getMany();

      return this.ok({
        message: '获取测试数据成功',
        count: testData.length,
        data: testData
      });
    } catch (error) {
      return this.fail(error.message || '获取测试数据失败');
    }
  }

  /**
   * 清理测试定时任务数据
   */
  @Post('/clear-test-schedule-data')
  async clearTestScheduleData() {
    try {
      const result = await this.orderOrderService['messageInfoEntity']
        .createQueryBuilder()
        .delete()
        .from('message_info')
        .where('name = :name', { name: '定时任务测试' })
        .execute();

      return this.ok({
        message: '清理测试数据成功',
        deletedCount: result.affected
      });
    } catch (error) {
      return this.fail(error.message || '清理测试数据失败');
    }
  }
}
