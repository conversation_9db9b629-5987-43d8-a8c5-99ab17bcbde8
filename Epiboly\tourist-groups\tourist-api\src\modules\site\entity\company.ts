import { BaseEntity } from '../../base/entity/base';
import { Column, Entity } from 'typeorm';

/**
 * 公司信息
 */
@Entity('site_company')
export class CompanyEntity extends BaseEntity {
    @Column({ comment: '公司名称' })
    name: string;

    @Column({ comment: '公司照片', nullable: true })
    image: string;

    @Column({ comment: '公司名称', nullable: true })
    address: string;

    @Column({ comment: '公司电话', nullable: true })
    phone: string;

    @Column({ comment: '公司邮箱', nullable: true })
    email: string;

    @Column({ comment: '是否为总公司', default: false })
    isHeadOffice: boolean;

    @Column({ comment: '公司描述', type: 'text', nullable: true })
    description: string;

    @Column({ comment: '联系人', nullable: true })
    contactPerson: string;

    @Column({ comment: '状态', default: 1 })
    status: number;

    @Column({ comment: '排序', default: 0 })
    sort: number;
} 