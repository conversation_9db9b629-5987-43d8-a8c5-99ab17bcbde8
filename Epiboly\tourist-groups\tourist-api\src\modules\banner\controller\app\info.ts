import { Inject } from '@midwayjs/core';
import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core';
import { BannerInfoEntity } from '../../entity/info';
import { BannerInfoService } from '../../service/info';
import { Get, Query } from '@midwayjs/core';
@CoolController({
  prefix: '/app/banner',
  api: [],
  entity: BannerInfoEntity,
  service: BannerInfoService,
})

export class AppBannerInfoController extends BaseController {
  @Inject()
  bannerInfoService: BannerInfoService;

  /**
   * 轮播图列表
   * @param position 位置 0-首页 1-主要景点
   */
  @Get('/list', { summary: '轮播图列表' })
  async list(@Query('position') position?: number) {
    // 构建查询条件
    const queryOptions: any = {
      where: {
        status: 1, // 只查询启用状态的轮播图
      },
      order: {
        createTime: 'DESC', // 按创建时间降序排序
      },
    };

    // 如果提供了位置参数，则添加到查询条件中
    if (position !== undefined && position !== null) {
      queryOptions.where.position = position;
    }

    const bannerList = await this.bannerInfoService.bannerInfoEntity.findOne(queryOptions);

    return this.ok(bannerList);
  }
}