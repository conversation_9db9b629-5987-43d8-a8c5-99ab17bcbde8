import { Provide } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { CompanyInfoEntity } from '../entity/info';

/**
 * 公司信息
 */
@Provide()
export class CompanyInfoService extends BaseService {
  @InjectEntityModel(CompanyInfoEntity)
  companyInfoEntity: Repository<CompanyInfoEntity>;
}
