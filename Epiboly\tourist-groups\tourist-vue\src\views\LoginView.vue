<template>
  <div class="login-container">
    <div class="login-card">
      <div class="logo-container">
        <img src="../assets/logo.png" alt="HAIYUE CHINA TOURS" class="logo" />
      </div>
      
      <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" class="login-form">
        <el-form-item prop="phone">
          <el-input 
            v-model="loginForm.phone" 
            placeholder="账号/用户名/邮箱"
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input 
            v-model="loginForm.password" 
            type="password" 
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        
        <div class="remember-container">
          <el-checkbox v-model="rememberPassword">记住密码</el-checkbox>
          <span class="forget-password" @click="showForgetPasswordDialog">忘记密码</span>
        </div>
        
        <el-button 
          type="danger" 
          class="login-button" 
          :loading="loading" 
          @click="handleLogin"
        >
          登录
        </el-button>
      </el-form>
      
      <div class="register-tip">
        还没有账号？ <span class="register-link" @click="handleRegister">去注册</span>
      </div>
    </div>

    <!-- 忘记密码弹窗 -->
    <el-dialog 
      v-model="forgetPasswordDialogVisible" 
      title="忘记密码" 
      width="900px"
      :before-close="handleForgetPasswordClose"
      class="forget-password-dialog"
      center
    >
      <div class="forget-dialog-content">
        <div class="dialog-header">
          <p class="dialog-desc">请填写您注册时的准确信息，我们将为您重置密码</p>
        </div>
        
        <div class="unified-form-container">
          <el-form 
            ref="forgetPasswordFormRef" 
            :model="forgetPasswordForm" 
            :rules="forgetPasswordRules" 
            class="forget-form"
            label-width="100px"
          >
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="联系人" prop="contactPerson">
                  <el-input 
                    v-model="forgetPasswordForm.contactPerson" 
                    placeholder="请输入联系人姓名"
                    size="large"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="手机号" prop="phone">
                  <el-input 
                    v-model="forgetPasswordForm.phone" 
                    placeholder="请输入手机号码"
                    size="large"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="邮箱" prop="email">
                  <el-input 
                    v-model="forgetPasswordForm.email" 
                    placeholder="请输入电子邮箱"
                    size="large"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="WhatsApp" prop="whatsApp">
                  <el-input 
                    v-model="forgetPasswordForm.whatsApp" 
                    placeholder="请输入WhatsApp账号"
                    size="large"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            
            
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="新密码" prop="newPassword">
                  <el-input 
                    v-model="forgetPasswordForm.newPassword" 
                    type="password" 
                    placeholder="请输入新密码"
                    show-password
                    size="large"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="确认密码" prop="confirmPassword">
                  <el-input 
                    v-model="forgetPasswordForm.confirmPassword" 
                    type="password" 
                    placeholder="请确认新密码"
                    show-password
                    size="large"
                  />
                </el-form-item>
              </el-col>
            </el-row>


            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="验证码" prop="code">
                  <div class="captcha-container">
                    <el-input 
                      v-model="forgetPasswordForm.code" 
                      placeholder="请输入验证码" 
                      size="large"
                      class="captcha-input"
                    />
                    <div class="captcha-wrapper h-[40px]">
                      <img 
                        :src="captchaImage" 
                        @click="refreshCaptcha" 
                        class="captcha-img"
                        alt="验证码"
                      />
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button 
            size="large" 
            @click="handleForgetPasswordClose"
            class="cancel-btn"
          >
            取消
          </el-button>
          <el-button 
            type="primary" 
            size="large"
            @click="handleForgetPasswordSubmit" 
            :loading="forgetPasswordLoading"
            class="submit-btn"
          >
            确认重置
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {userApi, commonApi} from '../api/user'
import { setToken, setRefreshToken, setUserInfo } from '../utils/auth'

const router = useRouter()
const loginFormRef = ref(null)
const loading = ref(false)
const rememberPassword = ref(false)

// 忘记密码相关
const forgetPasswordDialogVisible = ref(false)
const forgetPasswordFormRef = ref(null)
const forgetPasswordLoading = ref(false)
const captchaImage = ref('')
const captchaId = ref('')

const loginForm = reactive({
  phone: '',
  password: ''
})

const loginRules = {
  phone: [
    { required: true, message: '输入账号/用户名/邮箱', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '输入密码', trigger: 'blur' },
    { min: 6, message: '密码不能少于6个字符', trigger: 'blur' }
  ]
}

// 忘记密码表单
const forgetPasswordForm = reactive({
  contactPerson: '',
  phone: '',
  email: '',
  whatsApp: '',
  code: '',
  newPassword: '',
  confirmPassword: ''
})

// 确认密码验证函数
const validateConfirmPassword = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请确认密码'))
  } else if (value !== forgetPasswordForm.newPassword) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

const forgetPasswordRules = {
  contactPerson: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入电话号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入电子邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  whatsApp: [
    { required: true, message: '请输入WhatsApp账号', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码不能少于6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 检查是否记住了密码
onMounted(() => {
  const savedPhone = localStorage.getItem('saved_phone')
  const savedPassword = localStorage.getItem('saved_password')
  
  if (savedPhone && savedPassword) {
    loginForm.phone = savedPhone
    loginForm.password = savedPassword
    rememberPassword.value = true
  }

  checkAllRequestsComplete();
})

const checkAllRequestsComplete = () => {
    setTimeout(() => {
      // translate.execute() // 注释掉未定义的translate
    }, 50)
}

const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true
        const res = await userApi.login({
          phone: loginForm.phone,
          password: loginForm.password
        })
        
        // 检查响应数据结构
        if (res && res.data) {
          const { token, refreshToken, expire, refreshExpire } = res.data
          
          // 保存登录状态和token信息
          localStorage.setItem('isLoggedIn', 'true')
          setToken(token)
          setRefreshToken(refreshToken)
          
          // 保存token过期时间（可选）
          if (expire) {
            const expireTime = Date.now() + expire * 1000
            localStorage.setItem('tokenExpire', expireTime.toString())
          }
          
          if (refreshExpire) {
            const refreshExpireTime = Date.now() + refreshExpire * 1000
            localStorage.setItem('refreshTokenExpire', refreshExpireTime.toString())
          }
          
          // 如果选择记住密码，则保存用户名和密码
          if (rememberPassword.value) {
            localStorage.setItem('saved_phone', loginForm.phone)
            localStorage.setItem('saved_password', loginForm.password)
          } else {
            localStorage.removeItem('saved_phone')
            localStorage.removeItem('saved_password')
          }
          
          // 获取用户信息
          try {
            const userInfoRes = await userApi.getUserInfo()
            if (userInfoRes && userInfoRes.data) {
              setUserInfo(userInfoRes.data)
            }
          } catch (userInfoError) {
            console.warn('获取用户信息失败:', userInfoError)
            // 即使获取用户信息失败，也不影响登录流程
          }
          
          ElMessage.success('登录成功')
          router.push('/home')
        } else {
          throw new Error('登录响应数据格式错误')
        }
      } catch (error) {
        console.error('登录失败:', error)
        ElMessage.error(error.message || '登录失败，请检查账号密码')
      } finally {
        loading.value = false
      }
    }
  })
}

const handleRegister = () => {
  router.push('/apply')
}

// 获取验证码
const getCaptcha = async () => {
  try {
    const res = await commonApi.getCaptcha({
      width: 150,
      height: 40
    })
    if (res && res.data) {
      captchaImage.value = res.data.data
      captchaId.value = res.data.captchaId
    }
  } catch (error) {
    console.error('获取验证码失败:', error)
  }
}

// 刷新验证码
const refreshCaptcha = () => {
  getCaptcha()
}

// 显示忘记密码弹窗
const showForgetPasswordDialog = () => {
  forgetPasswordDialogVisible.value = true
  getCaptcha()
}

// 关闭忘记密码弹窗
const handleForgetPasswordClose = () => {
  forgetPasswordDialogVisible.value = false
  // 重置表单
  if (forgetPasswordFormRef.value) {
    forgetPasswordFormRef.value.resetFields()
  }
  // 清空表单数据
  Object.keys(forgetPasswordForm).forEach(key => {
    forgetPasswordForm[key] = ''
  })
}

// 提交忘记密码
const handleForgetPasswordSubmit = async () => {
  if (!forgetPasswordFormRef.value) return
  
  await forgetPasswordFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        forgetPasswordLoading.value = true

        console.log('captchaId', captchaId)
        const requestData = {
          contactPerson: forgetPasswordForm.contactPerson,
          phone: forgetPasswordForm.phone,
          email: forgetPasswordForm.email,
          whatsApp: forgetPasswordForm.whatsApp,
          newPassword: forgetPasswordForm.newPassword,
          captchaId: captchaId.value,
          code: forgetPasswordForm.code
        }
        
        const res = await userApi.forgetPassword(requestData)
        
        if (res && res.code === 1000) {
          ElMessage.success('密码重置成功，请使用新密码登录')
          handleForgetPasswordClose()
        }
      } catch (error) {
        refreshCaptcha()
      } finally {
        forgetPasswordLoading.value = false
      }
    }
  })
}
</script>

<style scoped>
.login-container {
  height: 100vh;
  width: 100vw;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background-image: url('../assets/images/login_bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding-right: 10%;
}

.login-card {
  width:520px;
  padding: 50px;
  background-color: white;
  border-radius: 40px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.logo-container {
  display: flex;
  justify-content: center;
  margin-bottom: 35px;
}

.logo {
  height: 80px;
}

.login-form {
  margin-bottom: 20px;
}

.remember-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  font-size: 14px;
}

.forget-password {
  color: #999999;
  cursor: pointer;
  font-size: 14px;
}

.forget-password:hover {
  color: #C01E2E;
}

.login-button {
  width: 100%;
  height: 60px;
  background-color: #c9161c;
  border-color: #c9161c;
  font-size: 18px;
  border-radius: 16px;
}

.login-button:hover,
.login-button:focus {
  background-color: #b11419;
  border-color: #b11419;
}

.register-tip {
  text-align: center;
  font-size: 14px;
  color: #999999;
  margin-top: 15px;
}

.register-link {
  cursor: pointer;
}

.register-link:hover {
  text-decoration: underline;
}

.login-form ::v-deep .el-input__wrapper{
    height: 50px;
    font-size: 16px;
    border-radius: 0;
    padding-left: 0;
    padding: 0;
    background-color: transparent;
    box-shadow: none !important;
    border: none;
    border-bottom: 1px solid #ececec;
}

.login-form :deep(.el-input__wrapper:hover) {
  box-shadow: none !important;
}

.login-form :deep(.el-form-item) {
  margin-bottom: 30px;
}

:deep(.el-checkbox__label) {
  font-size: 14px;
  color: #999999;
}

</style>

<style>
.el-checkbox__input.is-checked .el-checkbox__inner{
  background-color: #c9161c !important;
  border-color: #c9161c !important;
}

.el-checkbox__label {
  color: #999999 !important;
}

/* 原来的验证码样式，已被新样式替代，保留以防兼容性问题 */
.captcha-container {
  display: flex;
  align-items: center;
}

.captcha-container img {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

/* 忘记密码弹窗样式 */
.forget-password-dialog {
  border-radius: 12px;
  overflow: hidden;
}

.forget-password-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 25px 30px;
  margin: 0;
}

.forget-password-dialog :deep(.el-dialog__title) {
  font-size: 20px;
  font-weight: 600;
  color: white;
}

.forget-password-dialog :deep(.el-dialog__body) {
  padding: 30px;
  background-color: #fafbfc;
}

.forget-dialog-content {
  padding: 0;
}

.dialog-header {
  text-align: center;
  margin-bottom: 10px;
  padding: 25px;
  border-radius: 12px;
}

.header-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-icon i {
  font-size: 24px;
  color: white;
}

.dialog-desc {
  color: #666;
  margin: 0;
  font-size: 16px;
  line-height: 1.6;
}

.unified-form-container {
  background: white;
  border-radius: 12px;
  padding: 30px 30px 20px 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e8ecf0;
}

.forget-form {
  margin-bottom: 0;
}

.unified-form-container :deep(.el-form-item) {
  margin-bottom: 25px;
}

.unified-form-container :deep(.el-form-item__label) {
  color: #606266;
  font-weight: 500;
  line-height: 40px;
}

.unified-form-container :deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.unified-form-container :deep(.el-input__wrapper:hover) {
  border-color: #c0c4cc;
}

.unified-form-container :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.captcha-container {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.captcha-input {
  flex: 1;
  max-width: 300px;
}

.captcha-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.captcha-img {
  height: 45px;
  width: 130px;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  background-color: #999;
  transition: all 0.3s ease;
}

.captcha-img:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.refresh-tip {
  font-size: 12px;
  color: #909399;
  cursor: pointer;
  transition: color 0.3s ease;
}

.refresh-tip:hover {
  color: #667eea;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 15px;
  padding: 25px 30px;
  background: white;
  border-top: 1px solid #e8ecf0;
}

.cancel-btn {
  min-width: 100px;
  border-radius: 8px;
  border: 1px solid #dcdfe6;
  color: #606266;
}

.cancel-btn:hover {
  color: #409eff;
  border-color: #c6e2ff;
  background-color: #ecf5ff;
}

.submit-btn {
  min-width: 120px;
  background: #c9161c;
  border: none;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}
</style>