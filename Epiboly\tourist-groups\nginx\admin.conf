upstream apiProxy {
    server 127.0.0.1:8001;
}

server
{
    listen 9000;
    server_name ***********;
    index index.php index.html index.htm default.php default.htm default.html;
    root /www/wwwroot/***********_9000;
    
    # 设置客户端请求体大小限制为500MB
    client_max_body_size 500m;
    

    #ERROR-PAGE-START  错误页配置，可以注释、删除或修改
    error_page 404 /404.html;
    #error_page 502 /502.html;
    #ERROR-PAGE-END

    #PHP-INFO-START  PHP引用配置，可以注释或修改
    include enable-php-00.conf;
    #PHP-INFO-END


    #REWRITE-END

    #禁止访问的文件或目录
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE|README.md)
    {
        return 404;
    }


    #禁止在证书验证目录放入敏感文件
    if ( $uri ~ "^/\.well-known/.*\.(php|jsp|py|js|css|lua|ts|go|zip|tar\.gz|rar|7z|sql|bak)$" ) {
        return 403;
    }

    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$
    {
        expires      30d;
        error_log /dev/null;
        access_log /dev/null;
    }

    location ~ .*\.(js|css)?$
    {
        expires      12h;
        error_log /dev/null;
        access_log /dev/null;
    }
    access_log  /www/wwwlogs/***********_9000.log;
    error_log  /www/wwwlogs/***********_9000.error.log;
    
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 代理服务端地址  访问/api 表示访问服务端接口而不是静态资源
    location /api/
    {
        proxy_pass http://apiProxy/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header REMOTE-HOST $remote_addr;

        proxy_cache_bypass no_cache;

        #缓存相关配置
        #proxy_cache cache_one;
        #proxy_cache_key $host$request_uri$is_args$args;
        #proxy_cache_valid 200 304 301 302 1h;

        #持久化连接相关配置
        proxy_connect_timeout 3000s;
        proxy_read_timeout 86400s;
        proxy_send_timeout 3000s;
        #proxy_http_version 1.1;
        #proxy_set_header Upgrade $http_upgrade;
        #proxy_set_header Connection "upgrade";

        add_header X-Cache $upstream_cache_status;

        #expires 12h;
    }
    
     # 添加对 /dev 路径的处理
    # 修改转发规则，去掉/dev前缀
      location /dev/ {
          # 移除请求路径中的/dev前缀后转发
          rewrite ^/dev(.*)$ $1 break;
          proxy_pass http://apiProxy;
          
          # 其他配置保持不变
          proxy_set_header Host $host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header REMOTE-HOST $remote_addr;
          
          # 文件上传相关设置
          client_max_body_size 500m;
          
          # 增加超时设置
          proxy_connect_timeout 300s;
          proxy_read_timeout 300s;
          proxy_send_timeout 300s;
          
          # 禁用缓冲
          proxy_request_buffering off;
          proxy_buffering off;
      }

    # socket需额外配置
    location /socket {
        proxy_pass http://apiProxy/socket;
        proxy_connect_timeout 3600s; #配置点1
        proxy_read_timeout 3600s; #配置点2,如果没效,可以考虑这个时间配置长一点
        proxy_send_timeout 3600s; #配置点3
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header REMOTE-HOST $remote_addr;
        #proxy_bind $remote_addr transparent;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        rewrite /socket/(.*) /$1 break;
        proxy_redirect off;
    }
}

server
{
    listen 80;
    server_name admin.haiyuetravel.cn;
    index index.php index.html index.htm default.php default.htm default.html;
    root /www/wwwroot/***********_9000;
    
    # 设置客户端请求体大小限制为500MB
    client_max_body_size 500m;
    

    #ERROR-PAGE-START  错误页配置，可以注释、删除或修改
    error_page 404 /404.html;
    #error_page 502 /502.html;
    #ERROR-PAGE-END

    #PHP-INFO-START  PHP引用配置，可以注释或修改
    include enable-php-00.conf;
    #PHP-INFO-END


    #REWRITE-END

    #禁止访问的文件或目录
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE|README.md)
    {
        return 404;
    }


    #禁止在证书验证目录放入敏感文件
    if ( $uri ~ "^/\.well-known/.*\.(php|jsp|py|js|css|lua|ts|go|zip|tar\.gz|rar|7z|sql|bak)$" ) {
        return 403;
    }

    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$
    {
        expires      30d;
        error_log /dev/null;
        access_log /dev/null;
    }

    location ~ .*\.(js|css)?$
    {
        expires      12h;
        error_log /dev/null;
        access_log /dev/null;
    }
    access_log  /www/wwwlogs/***********_9000.log;
    error_log  /www/wwwlogs/***********_9000.error.log;
    
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 代理服务端地址  访问/api 表示访问服务端接口而不是静态资源
    location /api/
    {
        proxy_pass http://apiProxy/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header REMOTE-HOST $remote_addr;

        proxy_cache_bypass no_cache;

        #缓存相关配置
        #proxy_cache cache_one;
        #proxy_cache_key $host$request_uri$is_args$args;
        #proxy_cache_valid 200 304 301 302 1h;

        #持久化连接相关配置
        proxy_connect_timeout 3000s;
        proxy_read_timeout 86400s;
        proxy_send_timeout 3000s;
        #proxy_http_version 1.1;
        #proxy_set_header Upgrade $http_upgrade;
        #proxy_set_header Connection "upgrade";

        add_header X-Cache $upstream_cache_status;

        #expires 12h;
    }
    
     # 添加对 /dev 路径的处理
    # 修改转发规则，去掉/dev前缀
      location /dev/ {
          # 移除请求路径中的/dev前缀后转发
          rewrite ^/dev(.*)$ $1 break;
          proxy_pass http://apiProxy;
          
          # 其他配置保持不变
          proxy_set_header Host $host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header REMOTE-HOST $remote_addr;
          
          # 文件上传相关设置
          client_max_body_size 500m;
          
          # 增加超时设置
          proxy_connect_timeout 300s;
          proxy_read_timeout 300s;
          proxy_send_timeout 300s;
          
          # 禁用缓冲
          proxy_request_buffering off;
          proxy_buffering off;
      }

    # socket需额外配置
    location /socket {
        proxy_pass http://apiProxy/socket;
        proxy_connect_timeout 3600s; #配置点1
        proxy_read_timeout 3600s; #配置点2,如果没效,可以考虑这个时间配置长一点
        proxy_send_timeout 3600s; #配置点3
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header REMOTE-HOST $remote_addr;
        #proxy_bind $remote_addr transparent;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        rewrite /socket/(.*) /$1 break;
        proxy_redirect off;
    }
}